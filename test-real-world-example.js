import { generateOrderDataHash, generateDraftCacheKey } from './src/utils/keyGenerator.js';

/**
 * Test với real-world example từ user để prove fix hoạt động
 */

console.log('🧪 Testing REAL-WORLD example: User changing BUY MORE SAVE MORE options\n');

// User chọn BUY 1
const userOrder_Buy1 = [
  {
    "sku": "20241128-60ts-18-01-273RO4",
    "name": "🔥 Last Day - 49% OFF 🔥 DIY Sponge Finger Painting Kit-BUY MORE SAVE MORE BUY 1",
    "image": "https://admin.rarehotdeals.com/wp-content/uploads/2024/11/1-29-1.jpg",
    "price": 32.99,
    "total": "32.99",
    "quantity": 1,
    "meta_data": [
      {
        "id": 0,
        "name": "BUY MORE SAVE MORE",
        "option": "BUY 1"
      }
    ],
    "product_id": 30480,
    "product_link": "https://infinitesuccesslab.com/product/fingerpaintkit60ts",
    "variation_id": 300570
  }
];

// User đổ<PERSON> sang BUY 2 (gi<PERSON> có thể khác)
const userOrder_Buy2 = [
  {
    "sku": "20241128-60ts-18-01-273RO4", // Same SKU
    "name": "🔥 Last Day - 49% OFF 🔥 DIY Sponge Finger Painting Kit-BUY MORE SAVE MORE BUY 2",
    "image": "https://admin.rarehotdeals.com/wp-content/uploads/2024/11/1-29-1.jpg",
    "price": 59.99, // Different price
    "total": "59.99",
    "quantity": 1,    // Same quantity
    "meta_data": [
      {
        "id": 0,
        "name": "BUY MORE SAVE MORE",
        "option": "BUY 2"  // Different option!
      }
    ],
    "product_id": 30480, // Same product ID
    "product_link": "https://infinitesuccesslab.com/product/fingerpaintkit60ts",
    "variation_id": 300571 // Different variation ID
  }
];

// User đổi sang BUY 3 
const userOrder_Buy3 = [
  {
    "sku": "20241128-60ts-18-01-273RO4",
    "name": "🔥 Last Day - 49% OFF 🔥 DIY Sponge Finger Painting Kit-BUY MORE SAVE MORE BUY 3",
    "image": "https://admin.rarehotdeals.com/wp-content/uploads/2024/11/1-29-1.jpg",
    "price": 79.99,
    "total": "79.99", 
    "quantity": 1,
    "meta_data": [
      {
        "id": 0,
        "name": "BUY MORE SAVE MORE",
        "option": "BUY 3"
      }
    ],
    "product_id": 30480,
    "product_link": "https://infinitesuccesslab.com/product/fingerpaintkit60ts", 
    "variation_id": 300572
  }
];

const hash1 = generateOrderDataHash(userOrder_Buy1, []);
const hash2 = generateOrderDataHash(userOrder_Buy2, []);
const hash3 = generateOrderDataHash(userOrder_Buy3, []);

console.log('📦 User Order Scenarios:');
console.log(`BUY 1 ($32.99) → Hash: ${hash1}`);
console.log(`BUY 2 ($59.99) → Hash: ${hash2}`);
console.log(`BUY 3 ($79.99) → Hash: ${hash3}`);

console.log('\n🔍 Validation:');
console.log(`BUY 1 vs BUY 2: ${hash1 === hash2 ? '❌ SAME (Problem!)' : '✅ DIFFERENT (Good!)'}`);
console.log(`BUY 2 vs BUY 3: ${hash2 === hash3 ? '❌ SAME (Problem!)' : '✅ DIFFERENT (Good!)'}`);
console.log(`BUY 1 vs BUY 3: ${hash1 === hash3 ? '❌ SAME (Problem!)' : '✅ DIFFERENT (Good!)'}`);

// Test full cache keys
const metaData = [
  { key: "ip", value: "*************" },
  { key: "UA", value: "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N)" }
];

const cacheKey1 = generateDraftCacheKey('infinitesuccesslab.com', metaData, userOrder_Buy1, []);
const cacheKey2 = generateDraftCacheKey('infinitesuccesslab.com', metaData, userOrder_Buy2, []);
const cacheKey3 = generateDraftCacheKey('infinitesuccesslab.com', metaData, userOrder_Buy3, []);

console.log('\n🔑 Full Cache Keys:');
console.log(`BUY 1: ${cacheKey1}`);
console.log(`BUY 2: ${cacheKey2}`);
console.log(`BUY 3: ${cacheKey3}`);

console.log('\n🎯 CONCLUSION:');
if (hash1 !== hash2 && hash2 !== hash3 && hash1 !== hash3) {
  console.log('✅ SUCCESS: Each option creates DIFFERENT cache keys');
  console.log('✅ User changing BUY 1 → BUY 2 → BUY 3 will NOT hit old cache');
  console.log('✅ Each option will create NEW PayPal orders as expected');
} else {
  console.log('❌ PROBLEM: Some options have same cache keys');
  console.log('❌ Users may hit old cache when changing options');
} 