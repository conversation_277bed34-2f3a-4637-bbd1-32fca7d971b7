#!/usr/bin/env node

import axios from 'axios';

const API_BASE = process.env.API_BASE || 'http://localhost:3009';

// Test data with real PayPal config (simple calculation)
const testDraftOrder = {
  domain: "test.com",
  paypal_client_id: "EJZOmFcZFQFhxOkoMxxux7aVNVUjpWCtko_AaFKyqENYWQf5FbgcWz08zjqs9pGUElp7vfGfhf1LoJbz", // Real local client_id from paypal.json
  payment_method: "paypal",
  total: 25.99, // Simple: 19.99 + 6.00 = 25.99
  line_items: [
    {
      sku: "TEST001",
      name: "Test Product",
      quantity: 1,
      price: "19.99"  // Clean price
    }
  ],
  shipping_lines: [
    {
      method_id: "flat_rate",
      method_title: "Flat Rate",
      total: "6.00"   // Clean shipping
    }
  ],
  meta_data: [
    { key: "ip", value: "127.0.0.1" },
    { key: "UA", value: "Mozilla/5.0 (Test Browser)" },
    { key: "invoice_id", value: "test_invoice_123" },
    { key: "funding_source", value: "paypal" }
  ],
  billing: {
    first_name: "Test",
    last_name: "User",
    email: "<EMAIL>"
  },
  shipping: {
    first_name: "Test",
    last_name: "User"
  },
  currency: "USD",
  free_shipping: false
};

async function testV2CreateDraft() {
  console.log('🧪 Testing V2 Create Draft Order with Real PayPal...');
  
  // Debug: Log test data calculation
  const lineItemsTotal = testDraftOrder.line_items.reduce((sum, item) => 
    sum + (parseFloat(item.price) * parseInt(item.quantity)), 0
  );
  const shippingTotal = testDraftOrder.shipping_lines.reduce((sum, line) => 
    sum + parseFloat(line.total), 0
  );
  const expectedTotal = lineItemsTotal + shippingTotal;
  
  console.log('🔍 Debug Test Data Calculation:');
  console.log(`   Line Items Total: ${lineItemsTotal}`);
  console.log(`   Shipping Total: ${shippingTotal}`);
  console.log(`   Expected Total: ${expectedTotal}`);
  console.log(`   Provided Total: ${testDraftOrder.total}`);
  console.log(`   Match: ${expectedTotal === testDraftOrder.total ? '✅' : '❌'}`);
  
  try {
    const response = await axios.post(`${API_BASE}/api/v2/payments/paypal/create`, testDraftOrder, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 15000 // Longer timeout for real PayPal API
    });
    
    console.log('✅ V2 Create Draft - SUCCESS');
    console.log('Response:', {
      status: response.status,
      data: response.data
    });
    
    // Verify PayPal order ID format
    if (response.data.id && response.data.id.length > 10) {
      console.log('🎯 Real PayPal Order ID received:', response.data.id);
    }
    
    return response.data;
    
  } catch (error) {
    console.log('❌ V2 Create Draft - FAILED');
    if (error.response) {
      console.log('Error Response:', {
        status: error.response.status,
        data: error.response.data
      });
    } else {
      console.log('Error:', error.message);
    }
    throw error;
  }
}

async function testV2Duplicate() {
  console.log('\n🧪 Testing V2 Duplicate Prevention...');
  
  try {
    // First call
    const response1 = await axios.post(`${API_BASE}/api/v2/payments/paypal/create`, testDraftOrder);
    console.log('✅ First call - SUCCESS:', response1.data);
    
    // Immediate second call (should hit cache or create another draft)
    const response2 = await axios.post(`${API_BASE}/api/v2/payments/paypal/create`, testDraftOrder);
    console.log('✅ Second call - SUCCESS:', response2.data);
    
    if (response2.data.cached) {
      console.log('🎯 Draft cache is working!');
      
      // Third call should also hit cache
      const response3 = await axios.post(`${API_BASE}/api/v2/payments/paypal/create`, testDraftOrder);
      if (response3.data.cached) {
        console.log('🎯 Multiple cache hits working!');
      }
    } else {
      console.log('⚠️  Cache miss - might be first run or cache disabled');
    }
    
  } catch (error) {
    console.log('❌ V2 Duplicate Test - FAILED');
    if (error.response) {
      console.log('Error Response:', {
        status: error.response.status,
        data: error.response.data
      });
    } else {
      console.log('Error:', error.message);
    }
  }
}

async function testMultipleDomains() {
  console.log('\n🧪 Testing Multiple Domains...');
  
  const domains = ['domain1.com', 'domain2.com'];
  
  for (const domain of domains) {
    try {
      const testOrder = {
        ...testDraftOrder,
        domain: domain,
        meta_data: [
          { key: "ip", value: "*************" }, // Different IP for different test
          { key: "UA", value: "Mozilla/5.0 (Domain Test Browser)" },
          { key: "invoice_id", value: `test_invoice_${domain}_${Date.now()}` },
          { key: "funding_source", value: "paypal" }
        ]
      };
      
      const response = await axios.post(`${API_BASE}/api/v2/payments/paypal/create`, testOrder);
      console.log(`✅ Domain ${domain} - SUCCESS:`, {
        id: response.data.id,
        cached: response.data.cached
      });
      
    } catch (error) {
      console.log(`❌ Domain ${domain} - FAILED:`, error.response?.data || error.message);
    }
  }
}

async function testDifferentProducts() {
  console.log('\n🧪 Testing Different Products (should create different cache keys)...');
  
  const product1Order = {
    ...testDraftOrder,
    line_items: [
      {
        sku: "PRODUCT_A",
        name: "Product A",
        quantity: 1,
        price: "19.99"
      }
    ],
    total: 29.98,
    meta_data: [
      { key: "ip", value: "127.0.0.1" },
      { key: "UA", value: "Mozilla/5.0 (Product Test)" },
      { key: "invoice_id", value: `product_a_${Date.now()}` },
      { key: "funding_source", value: "paypal" }
    ]
  };
  
  const product2Order = {
    ...testDraftOrder,
    line_items: [
      {
        sku: "PRODUCT_B", 
        name: "Product B",
        quantity: 2,
        price: "15.99"
      }
    ],
    total: 41.97,
    meta_data: [
      { key: "ip", value: "127.0.0.1" }, // Same IP
      { key: "UA", value: "Mozilla/5.0 (Product Test)" }, // Same UA
      { key: "invoice_id", value: `product_b_${Date.now()}` },
      { key: "funding_source", value: "paypal" }
    ]
  };
  
  try {
    const response1 = await axios.post(`${API_BASE}/api/v2/payments/paypal/create`, product1Order);
    console.log('✅ Product A - SUCCESS:', response1.data.id);
    
    const response2 = await axios.post(`${API_BASE}/api/v2/payments/paypal/create`, product2Order);
    console.log('✅ Product B - SUCCESS:', response2.data.id);
    
    if (response1.data.id !== response2.data.id) {
      console.log('🎯 Different products create different orders - Cache key working correctly!');
    }
    
  } catch (error) {
    console.log('❌ Different Products Test - FAILED:', error.response?.data || error.message);
  }
}

async function testValidation() {
  console.log('\n🧪 Testing V2 Validation...');
  
  const invalidRequests = [
    {
      name: 'Missing line_items',
      data: { ...testDraftOrder, line_items: undefined }
    },
    {
      name: 'Invalid line_items (not array)',
      data: { ...testDraftOrder, line_items: "invalid" }
    },
    {
      name: 'Empty line_items',
      data: { ...testDraftOrder, line_items: [] }
    },
    {
      name: 'Missing meta_data',
      data: { ...testDraftOrder, meta_data: undefined }
    },
    {
      name: 'Missing ip in meta_data',
      data: { 
        ...testDraftOrder, 
        meta_data: testDraftOrder.meta_data.filter(item => item.key !== 'ip')
      }
    },
    {
      name: 'Missing UA in meta_data',
      data: { 
        ...testDraftOrder, 
        meta_data: testDraftOrder.meta_data.filter(item => item.key !== 'UA')
      }
    }
  ];
  
  for (const testCase of invalidRequests) {
    try {
      const response = await axios.post(`${API_BASE}/api/v2/payments/paypal/create`, testCase.data);
      console.log(`❌ ${testCase.name} - Should have failed but got:`, response.data);
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log(`✅ ${testCase.name} - Correctly rejected:`, error.response.data.error);
      } else {
        console.log(`❌ ${testCase.name} - Unexpected error:`, error.message);
      }
    }
  }
}

async function runTests() {
  console.log('🚀 Starting V2 API Tests with Real PayPal Environment...\n');
  
  try {
    // Test basic functionality with real PayPal
    await testV2CreateDraft();
    
    // Test duplicate prevention/caching
    await testV2Duplicate();
    
    // Test multiple domains
    await testMultipleDomains();
    
    // Test different products (cache key differentiation)
    await testDifferentProducts();
    
    // Test validation
    await testValidation();
    
    console.log('\n✅ All V2 API tests completed successfully!');
    console.log('🎯 Summary:');
    console.log('  ✅ Real PayPal integration working');
    console.log('  ✅ Cache system operational');
    console.log('  ✅ Multiple domains supported');
    console.log('  ✅ Cache key differentiation working');
    console.log('  ✅ Validation system robust');
    
  } catch (error) {
    console.log('\n❌ Test suite failed:', error.message);
    console.log('\n🔧 Debugging tips:');
    console.log('  1. Make sure server is running on port 3009');
    console.log('  2. Check Redis is connected');
    console.log('  3. Verify PayPal credentials in paypal.json');
    process.exit(1);
  }
}

// Check if API server is running and verify environment
async function checkServer() {
  try {
    const healthResponse = await axios.get(`${API_BASE}/api/v2/health`, { timeout: 5000 });
    console.log(`✅ Server is running at ${API_BASE}`);
    console.log('🏥 Health check:', healthResponse.data);
    return true;
  } catch (error) {
    console.log(`❌ Server not available at ${API_BASE}`);
    console.log('Please make sure the server is running with: npm run dev');
    return false;
  }
}

// Verify PayPal environment
async function verifyPayPalEnvironment() {
  console.log('\n🔍 Verifying PayPal Environment...');
  
  try {
    // Create a simple test order with free shipping to avoid calculation complexity
    const testOrder = {
      domain: "environment-test.com",
      paypal_client_id: "EJZOmFcZFQFhxOkoMxxux7aVNVUjpWCtko_AaFKyqENYWQf5FbgcWz08zjqs9pGUElp7vfGfhf1LoJbz",
      payment_method: "paypal",
      total: 10.00, // Simple round number
      line_items: [
        {
          sku: "ENV_TEST",
          name: "Environment Test Product",
          quantity: 1,
          price: "10.00"  // Exact match with total (no shipping)
        }
      ],
      shipping_lines: [], // Empty for free shipping
      meta_data: [
        { key: "ip", value: "127.0.0.1" },
        { key: "UA", value: "Mozilla/5.0 (Environment Test)" },
        { key: "invoice_id", value: `env_test_${Date.now()}` },
        { key: "funding_source", value: "paypal" }
      ],
      billing: {
        first_name: "Test",
        last_name: "User",
        email: "<EMAIL>"
      },
      shipping: {
        first_name: "Test",
        last_name: "User"
      },
      currency: "USD",
      free_shipping: true  // This should make shipping = 0
    };
    
    const response = await axios.post(`${API_BASE}/api/v2/payments/paypal/create`, testOrder);
    
    if (response.data.id) {
      // PayPal sandbox order IDs typically start with specific patterns
      const orderId = response.data.id;
      
      console.log('✅ PayPal Environment Check:');
      console.log(`   🆔 Order ID: ${orderId}`);
      
      if (orderId.startsWith('8') || orderId.startsWith('9') || orderId.length >= 17) {
        console.log('   🧪 Environment: SANDBOX (test mode)');
        console.log('   ✅ Safe for testing - no real money transactions');
      } else {
        console.log('   ⚠️  Environment: Might be PRODUCTION');
        console.log('   🚨 WARNING: Be careful with real money transactions!');
      }
      
      return { environment: 'sandbox', orderId };
    }
    
  } catch (error) {
    console.log('❌ PayPal Environment Check Failed:');
    if (error.response) {
      console.log('   Error:', error.response.data);
    } else {
      console.log('   Error:', error.message);
    }
    throw error;
  }
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
  const serverAvailable = await checkServer();
  if (serverAvailable) {
    // Verify PayPal environment first
    await verifyPayPalEnvironment();
    
    // Then run full test suite
    await runTests();
  } else {
    process.exit(1);
  }
}

export { 
  testV2CreateDraft, 
  testV2Duplicate, 
  testMultipleDomains,
  testDifferentProducts,
  testValidation,
  verifyPayPalEnvironment 
}; 