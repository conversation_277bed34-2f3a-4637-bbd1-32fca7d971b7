# Cache System Refactoring

## Overview
The cache system has been refactored to improve code organization and separation of concerns. The main changes include:

1. **Separated Redis clearing logic** from external site revalidation
2. **Created dedicated controller functions** for better organization
3. **Organized routes** in a separate file
4. **Improved error handling** and response consistency

## File Structure

```
src/
├── config/
│   └── cache.js                      # Core cache functions (Redis/LRU only)
├── controllers/
│   └── cacheController.js            # Cache route controllers (new)
├── routes/
│   └── cacheRoutes.js                # Cache route definitions (new)
├── utils/
│   └── externalSiteRevalidation.js   # External site revalidation utility (new)
└── cacheServer.js                    # Main server file (simplified)
```

## Key Changes

### 1. Cache Configuration (`src/config/cache.js`)

**Philosophy:** `clearCacheByTag` now only handles Redis/LRU cache clearing. External site revalidation is completely separated into a dedicated utility file.

**Before:**
```javascript
const clearCacheByTag = async (tag) => {
  // External revalidation + Redis clearing mixed together
  try {
    await axios.get("https://1siteclone.com/api/revalidate?tag=all");
    console.log("Cache revalidated successfully");
  } catch (error) {
    console.error("Error revalidating cache:", error);
  }

  if (useRedis) {
    const keys = await redisClient.smembers(tag);
    if (keys.length > 0) {
      await redisClient.del(keys);
      await redisClient.del(tag);
    }
  } else {
    const keys = lruCache.get(tag) || [];
    keys.forEach((key) => lruCache.delete(key));
    lruCache.delete(tag);
  }
};
```

**After:**

**Cache Configuration (`src/config/cache.js`):**
```javascript
// Main function - only clears Redis/LRU cache
const clearCacheByTag = async (tag) => {
  if (useRedis) {
    const keys = await redisClient.smembers(tag);
    if (keys.length > 0) {
      await redisClient.del(keys);
      await redisClient.del(tag);
      return keys.length;
    }
    return 0;
  } else {
    const keys = lruCache.get(tag) || [];
    keys.forEach((key) => lruCache.delete(key));
    lruCache.delete(tag);
    return keys.length;
  }
};
```

**External Site Revalidation Utility (`src/utils/externalSiteRevalidation.js`):**
```javascript
import axios from "axios";

export const revalidateExternalSite = async () => {
  try {
    await axios.get("https://1siteclone.com/api/revalidate?tag=all");
    console.log("Cache revalidated successfully");
  } catch (error) {
    console.error("Error revalidating cache:", error);
    throw error;
  }
};
```

**Controller handles coordination:**
```javascript
// Clear cache by tag controller (includes external revalidation + Redis clearing)
export const clearCacheByTagController = async (req, res) => {
  const { tag } = req.query;

  if (!tag) {
    return res.status(400).send("Tag is required");
  }

  try {
    // First, revalidate external site
    await revalidateExternalSite();

    // Then clear Redis cache
    const clearedCount = await clearCacheByTag(tag);

    res.json({
      message: `Cache cleared for tag: ${tag}`,
      clearedCount: clearedCount,
      externalRevalidated: true
    });
  } catch (error) {
    logger.error("Error clearing cache", {
      message: error.message,
      stack: error.stack,
      tag: tag
    });
    res.status(500).send("Internal Server Error");
  }
};
```

### 2. External Site Revalidation Utility (`src/utils/externalSiteRevalidation.js`)

**Benefits:**
- Separated external API concerns from cache logic
- Single responsibility: only handles external site revalidation
- Easy to test and mock
- Can be reused in other parts of the application

### 3. Controller Layer (`src/controllers/cacheController.js`)

**Benefits:**
- Centralized error handling
- Consistent response format
- Better logging with context
- Separation of business logic from routing

**Example:**
```javascript
export const clearCacheByTagController = async (req, res) => {
  const { tag } = req.query;

  if (!tag) {
    return res.status(400).send("Tag is required");
  }

  try {
    const clearedCount = await clearCacheByTag(tag);
    res.json({
      message: `Cache cleared for tag: ${tag}`,
      clearedCount: clearedCount
    });
  } catch (error) {
    logger.error("Error clearing cache", {
      message: error.message,
      stack: error.stack,
      tag: tag
    });
    res.status(500).send("Internal Server Error");
  }
};
```

### 4. Route Organization (`src/routes/cacheRoutes.js`)

All cache-related routes are now organized in a single file:
- `/clear-cache` - Clear cache by tag (includes external revalidation + Redis clearing)
- `/clear-redis-cache` - Clear only Redis cache by tag (without external revalidation)
- `/revalidate-external` - Revalidate external site only
- `/get-cache` - Get cache by tag
- `/reduce-cache-ttl` - Reduce cache TTL
- `/clear-cache-priority` - Clear cache by priority
- `/flush-all-cache` - Flush all cache
- `/clear-old-version-cache` - Clear old version cache
- `/clear-nextjs-cache` - Clear NextJS cache
- `/cache-stats` - Cache statistics

## New Endpoints

### Clear Redis Cache Only
```
GET /clear-redis-cache?tag=<tag>
```
This endpoint clears only the Redis cache without triggering external site revalidation.

### Revalidate External Site Only
```
GET /revalidate-external
```
This endpoint only triggers the external site revalidation without clearing Redis cache.

## Benefits

1. **Better Separation of Concerns**:
   - `clearCacheByTag` function only handles Redis/LRU cache clearing
   - External site revalidation is completely separated into its own utility
   - HTTP concerns are handled at the controller level
   - Each module has a single, clear responsibility

2. **Improved Flexibility**: You can now clear Redis cache or revalidate external site independently

3. **Better Error Handling**: Each operation can be handled separately with specific error messages

4. **Cleaner Code Organization**: Controllers, routes, and business logic are properly separated

5. **Enhanced Logging**: More detailed logging with context information

6. **Consistent Response Format**: All endpoints now return JSON responses with consistent structure

7. **Single Responsibility**: Each function has a single, clear responsibility

## Usage Examples

### Clear both external cache and Redis cache:
```bash
curl "http://localhost:3006/clear-cache?tag=products"
```

### Clear only Redis cache:
```bash
curl "http://localhost:3006/clear-redis-cache?tag=products"
```

### Revalidate only external site:
```bash
curl "http://localhost:3006/revalidate-external"
```

## Migration Notes

- All existing endpoints continue to work as before
- The main `/clear-cache` endpoint behavior remains unchanged
- New endpoints provide additional flexibility for specific use cases
- Response format has been enhanced to include more information (e.g., `clearedCount`)
