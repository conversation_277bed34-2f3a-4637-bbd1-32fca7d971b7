# PayPal Configuration Database Schema (Đ<PERSON>n giản hóa)

## 📊 Database Design

### 1. PayPalConfigs Table (<PERSON>ám s<PERSON>t hiện tại)

```sql
CREATE TABLE PayPalConfigs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  
  -- Identification (giống paypal.json)
  port VARCHAR(50) NOT NULL UNIQUE COMMENT 'Port identifier (e.g., "002", "local", "dth")',
  
  -- Primary PayPal Account
  client_id VARCHAR(255) NOT NULL COMMENT 'PayPal Client ID',
  secret_key VARCHAR(500) NOT NULL COMMENT 'Encrypted PayPal Secret Key',
  
  -- Secondary PayPal Account (optional, giống hiện tại)
  client_id_2 VARCHAR(255) NULL COMMENT 'Secondary PayPal Client ID',
  secret_key_2 VARCHAR(500) NULL COMMENT 'Encrypted Secondary PayPal Secret Key',
  
  -- Configuration Settings (giống paypal.json)
  email VARCHAR(255) NOT NULL COMMENT 'PayPal account email',
  hidden_product_name BOOLEAN DEFAULT FALSE COMMENT 'Whether to hide product names in PayPal',
  
  -- Status (chỉ deactivate, không update)
  is_active BOOLEAN DEFAULT TRUE COMMENT 'Whether this config is active',
  
  -- Metadata
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(100) NULL COMMENT 'User who created this config',
  
  -- Indexes for performance
  INDEX idx_port (port),
  INDEX idx_client_id (client_id),
  INDEX idx_client_id_2 (client_id_2),
  INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## ❌ Không cần Audit Tables

Theo yêu cầu đơn giản hóa:
- Không cần PayPalConfigAudits table
- Không cần PayPalConfigVersions table
- Chỉ cần deactivate, không cần update nên không cần audit trail

## 🔄 Migration Scripts (Đơn giản hóa)

### 1. Create Table Migration

```javascript
// migrations/**************-create-paypal-configs.cjs
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create PayPalConfigs table
    await queryInterface.createTable('PayPalConfigs', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      port: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true
      },
      client_id: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      secret_key: {
        type: Sequelize.STRING(500),
        allowNull: false
      },
      client_id_2: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      secret_key_2: {
        type: Sequelize.STRING(500),
        allowNull: true
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      hidden_product_name: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      created_by: {
        type: Sequelize.STRING(100),
        allowNull: true
      }
    });

    // Add indexes
    await queryInterface.addIndex('PayPalConfigs', ['port'], { name: 'idx_port' });
    await queryInterface.addIndex('PayPalConfigs', ['client_id'], { name: 'idx_client_id' });
    await queryInterface.addIndex('PayPalConfigs', ['client_id_2'], { name: 'idx_client_id_2' });
    await queryInterface.addIndex('PayPalConfigs', ['is_active'], { name: 'idx_active' });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('PayPalConfigs');
  }
};
```

### 2. Data Migration Script

```javascript
// migrations/20241201000002-migrate-paypal-json-data.cjs
'use strict';

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

module.exports = {
  async up(queryInterface, Sequelize) {
    // Read the existing paypal.json file
    const paypalJsonPath = path.join(__dirname, '../config/paypal.json');
    
    if (!fs.existsSync(paypalJsonPath)) {
      console.log('paypal.json not found, skipping data migration');
      return;
    }

    const paypalData = JSON.parse(fs.readFileSync(paypalJsonPath, 'utf8'));
    const encryptionKey = process.env.PAYPAL_ENCRYPTION_KEY;
    
    if (!encryptionKey) {
      throw new Error('PAYPAL_ENCRYPTION_KEY environment variable is required for migration');
    }

    // Encryption function
    function encrypt(text) {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher('aes-256-gcm', encryptionKey);
      
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag();
      
      return JSON.stringify({
        iv: iv.toString('hex'),
        data: encrypted,
        authTag: authTag.toString('hex')
      });
    }

    // Migrate each config (bám sát structure hiện tại)
    for (const [port, config] of Object.entries(paypalData)) {
      try {
        await queryInterface.bulkInsert('PayPalConfigs', [{
          port,
          client_id: config.client_id,
          secret_key: encrypt(config.secret_key),
          client_id_2: config.client_id_2 || null,
          secret_key_2: config.secret_key_2 ? encrypt(config.secret_key_2) : null,
          email: config.email,
          hidden_product_name: config.hidden_product_name || false,
          is_active: true,
          created_by: 'migration_script',
          created_at: new Date()
        }]);

        console.log(`✅ Migrated config for port: ${port}`);
      } catch (error) {
        console.error(`❌ Error migrating port ${port}:`, error.message);
      }
    }

    console.log('PayPal configuration migration completed');
  },

  async down(queryInterface, Sequelize) {
    // Remove all migrated data
    await queryInterface.bulkDelete('PayPalConfigs', {
      created_by: 'migration_script'
    });
  }
};
```

## 🔍 Data Validation Queries (Đơn giản hóa)

### Check Migration Success
```sql
-- Count total configs
SELECT COUNT(*) as total_configs FROM PayPalConfigs;

-- Check active vs inactive configs
SELECT is_active, COUNT(*) as count 
FROM PayPalConfigs 
GROUP BY is_active;

-- Verify all ports are unique
SELECT port, COUNT(*) as count 
FROM PayPalConfigs 
GROUP BY port 
HAVING count > 1;

-- Check for missing required fields
SELECT id, port, email 
FROM PayPalConfigs 
WHERE client_id IS NULL 
   OR secret_key IS NULL 
   OR email IS NULL;

-- List all ports (for admin interface)
SELECT port, email, hidden_product_name, is_active, created_at
FROM PayPalConfigs 
ORDER BY port;
```

### Simple Monitoring Queries
```sql
-- Count configs by status
SELECT 
  SUM(CASE WHEN is_active = true THEN 1 ELSE 0 END) as active_configs,
  SUM(CASE WHEN is_active = false THEN 1 ELSE 0 END) as inactive_configs,
  COUNT(*) as total_configs
FROM PayPalConfigs;

-- Recently added configs
SELECT port, email, created_by, created_at
FROM PayPalConfigs 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY created_at DESC;
```

## 🏗 Sequelize Model

```javascript
// src/models/payPalConfig.js
import { DataTypes } from "sequelize";
import sequelize from "../config/db.js";

const PayPalConfig = sequelize.define(
  "PayPalConfig",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    port: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
    },
    client_id: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    secret_key: {
      type: DataTypes.STRING(500),
      allowNull: false,
    },
    client_id_2: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    secret_key_2: {
      type: DataTypes.STRING(500),
      allowNull: true,
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    hidden_product_name: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    created_by: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
  },
  {
    indexes: [
      { fields: ["port"] },
      { fields: ["client_id"] },
      { fields: ["client_id_2"] },
      { fields: ["is_active"] },
    ],
    tableName: "PayPalConfigs",
  }
);

export default PayPalConfig;
```

---

*Schema này được thiết kế để đảm bảo hiệu suất, bảo mật và khả năng mở rộng cho hệ thống PayPal configuration.*
