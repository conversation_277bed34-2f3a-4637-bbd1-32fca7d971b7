# PayPal Configuration: Simple Implementation Plan

## 🎯 Yêu cầu đơn giản hóa

### ✅ Những gì cần làm:
- <PERSON>y<PERSON><PERSON> từ `paypal.json` sang database + cache
- Chỉ có **deactivate**, không có update
- Không cần PayPal audit logs
- Không cần `currency_code` field
- Bám sát structure hiện tại
- List API show port + add port functionality
- Không cần refresh cache (vì không update)

### ❌ Những gì không cần:
- PayPalConfigAudits table
- Update functionality
- Currency code field
- Complex audit trail
- Cache refresh APIs

## 📊 Database Schema Đơn giản

```sql
CREATE TABLE PayPalConfigs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  port VARCHAR(50) NOT NULL UNIQUE,
  
  -- PayPal credentials (giống paypal.json)
  client_id VARCHAR(255) NOT NULL,
  secret_key VARCHAR(500) NOT NULL, -- Encrypted
  client_id_2 VARCHAR(255) NULL,
  secret_key_2 VARCHAR(500) NULL, -- Encrypted
  
  -- Settings (giống paypal.json)
  email VARCHAR(255) NOT NULL,
  hidden_product_name BOOLEAN DEFAULT FALSE,
  
  -- Simple status
  is_active BOOLEAN DEFAULT TRUE,
  
  -- Basic metadata
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(100) NULL,
  
  -- Indexes
  INDEX idx_port (port),
  INDEX idx_client_id (client_id),
  INDEX idx_active (is_active)
);
```

## 🔧 Service Layer Đơn giản

### PayPalConfigService
```javascript
class PayPalConfigService {
  // Read operations với caching
  async getConfigByPort(port)
  async getConfigByClientId(clientId)
  
  // Admin operations
  async listConfigs()           // Show all ports
  async addConfig(configData)   // Add new port
  async deactivateConfig(port)  // Deactivate only
  
  // Utility
  encrypt(text)
  decrypt(encryptedText)
}
```

## 🌐 API Endpoints Đơn giản

```javascript
// Admin APIs
GET    /api/admin/paypal-configs          // List all configs (show port)
POST   /api/admin/paypal-configs          // Add new config
PUT    /api/admin/paypal-configs/:id/deactivate // Deactivate config

// Internal APIs
GET    /api/internal/paypal-config/port/:port        // Get by port
GET    /api/internal/paypal-config/client-id/:id     // Get by client_id
```

## 📋 Implementation Steps

### Step 1: Database Setup (1 ngày)
```bash
# 1. Tạo migration file
npx sequelize migration:generate --name create-paypal-configs

# 2. Tạo model
touch src/models/payPalConfig.js

# 3. Run migration
npx sequelize db:migrate
```

### Step 2: Service Layer (1 ngày)
```bash
# 1. Tạo service
touch src/services/payPalConfigService.js

# 2. Implement caching + encryption
# 3. Add basic CRUD operations
```

### Step 3: Migration Script (1 ngày)
```bash
# 1. Tạo migration script
touch scripts/migratePayPalConfig.js

# 2. Test với data thật
# 3. Verify integrity
```

### Step 4: Integration (1 ngày)
```bash
# 1. Update PayPalService
# 2. Update scripts sử dụng paypal.json
# 3. Testing
```

### Step 5: Admin APIs (1 ngày)
```bash
# 1. Tạo admin controller
# 2. Add routes
# 3. Simple frontend (optional)
```

## 🧪 Testing Plan

### Unit Tests
```javascript
// Test encryption/decryption
// Test caching logic
// Test CRUD operations
```

### Integration Tests
```javascript
// Test PayPalService integration
// Test API endpoints
// Test migration script
```

## 🚀 Deployment Plan

### Phase 1: Setup
1. Deploy database changes
2. Run migration script
3. Verify data integrity

### Phase 2: Switch
1. Update PayPalService to use database
2. Keep paypal.json as fallback
3. Monitor performance

### Phase 3: Cleanup
1. Remove paypal.json dependency
2. Remove fallback code
3. Documentation update

## 📊 Expected Benefits

### Performance
- **Config load time**: 6-12ms → 0.2ms (98% improvement)
- **Memory usage**: 10MB → 2.5MB (75% reduction)
- **Cache hit rate**: 0% → 95%+

### Security
- **Encryption**: Plain text → AES-256-GCM
- **Access control**: File system → Database permissions
- **Audit**: None → Basic logging

### Management
- **Updates**: File edit + restart → API call
- **Monitoring**: None → Database queries
- **Backup**: Manual → Automated

## 🔒 Security Implementation

### Encryption
```javascript
// AES-256-GCM encryption for credentials
const encryptionKey = process.env.PAYPAL_ENCRYPTION_KEY; // 32 bytes
```

### Environment Variables
```bash
# Required
PAYPAL_ENCRYPTION_KEY=your-32-byte-encryption-key

# Optional (for local development)
PAYPAL_CLIENT_ID=local-client-id
PAYPAL_SECRET=local-secret-key
```

## 📈 Monitoring

### Key Metrics
- Config load times
- Cache hit/miss rates
- Database query performance
- Active vs inactive configs

### Simple Queries
```sql
-- Monitor performance
SELECT COUNT(*) FROM PayPalConfigs WHERE is_active = true;

-- Check recent additions
SELECT port, created_at FROM PayPalConfigs 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY);
```

## ✅ Success Criteria

### Technical
- [ ] All 80+ configs migrated successfully
- [ ] Config load time < 1ms (95th percentile)
- [ ] Cache hit rate > 95%
- [ ] Zero data loss during migration

### Functional
- [ ] List API shows all ports
- [ ] Add new config works
- [ ] Deactivate config works
- [ ] PayPalService integration works
- [ ] All existing scripts work

### Operational
- [ ] Zero downtime deployment
- [ ] Rollback plan tested
- [ ] Documentation updated
- [ ] Team trained on new system

## 🎯 Timeline: 5 ngày

| Day | Task | Deliverable |
|-----|------|-------------|
| 1 | Database setup + model | Working database schema |
| 2 | Service layer + caching | PayPalConfigService complete |
| 3 | Migration script | Data migration working |
| 4 | Integration + testing | PayPalService updated |
| 5 | Admin APIs + deployment | Production ready |

---

**Kết luận**: Implementation đơn giản này sẽ giải quyết tất cả vấn đề hiện tại với effort tối thiểu và risk thấp. Focus vào core functionality: read performance, security, và basic management.
