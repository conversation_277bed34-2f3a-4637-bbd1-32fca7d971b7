# PayPal Configuration Migration: From File to Database & Cache

## 📋 Tổng quan

Hiện tại hệ thống đang sử dụng file `paypal.json` để lưu trữ cấu hình PayPal cho các domain khác nhau. Điều này gây ra nhiều vấn đề về bảo mật, quản lý và hiệu suất. Tài liệu này phân tích vấn đề hiện tại và đề xuất kiến trúc mới.

## 🚨 Vấn đề hiện tại

### 1. **Vấn đề bảo mật**
- **Credentials lộ ra**: Client ID và Secret Key được lưu trữ dưới dạng plain text trong file JSON
- **Version control risk**: Thông tin nhạy cảm có thể bị commit vào Git repository
- **File system access**: Bất kỳ ai có quyền đọc file đều có thể truy cập credentials

### 2. **Vấn đề quản lý**
- **<PERSON><PERSON><PERSON> cập nhật**: Phải restart service để load cấu hình mới
- **Không có audit trail**: Không theo dõi được ai thay đổi gì, khi nào
- **Backup phức tạp**: Phải backup file riêng biệt
- **Scaling issues**: Khó đồng bộ giữa nhiều instances

### 3. **Vấn đề hiệu suất**
- **File I/O overhead**: Đọc file mỗi lần cần cấu hình
- **Memory usage**: Load toàn bộ config vào memory
- **No caching strategy**: Không có chiến lược cache hiệu quả

### 4. **Cấu trúc hiện tại**
```json
{
  "local": {
    "port": "local",
    "client_id": "EJZOmFcZFQFhxOkoMxxux7aVNVUjpWCtko_AaFKyqENYWQf5FbgcWz08zjqs9pGUElp7vfGfhf1LoJbz",
    "secret_key": "AbGDiSDqoEj4eAGaTajmisiQGpbG1D38F9hv5W3Vu9_GeQpx8wMoThiSm8huWkE1SSzuZxDe9AeTn6HJ",
    "email": "local",
    "hidden_product_name": true
  },
  "002": {
    "port": "002",
    "client_id": "AYik9q9KjVgRJNhYWSrGdC9GYG1cmdaKFCQ6NwMimVUu6hB-QCI58j1gd9RYPm8E4xTvYmlgrFZIRfe2",
    "secret_key": "EIu73QYFWiKGr2alZVCudBYsj-dBup_eCU1HjQUEythBqyOjO9lVZ5S7_Ru4LGKNV0HjJeLDPAsb_MwQ",
    "client_id_2": "ASOklDzEKuzfYjC_76rioSUQw2cTNVYr1Xeqahe8elvhGvTF7paKtN_tUB47o8VJNHT33B32RXu03awK",
    "secret_key_2": "EOyw8ueoe0OQQy0RTNuVYFvKWKlZRYA8CBUFTAfAlLYiRrEOtYTleuoXkdsLbf_aPpoZthw0-H62bXAK",
    "email": "<EMAIL>",
    "hidden_product_name": false
  }
  // ... 80+ domains khác
}
```

## 🎯 Kiến trúc mới đề xuất

### 1. **Database Schema (Đơn giản hóa)**

#### PayPalConfig Table
```sql
CREATE TABLE PayPalConfigs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  port VARCHAR(50) NOT NULL UNIQUE,
  
  -- Primary PayPal Account
  client_id VARCHAR(255) NOT NULL,
  secret_key VARCHAR(500) NOT NULL, -- Encrypted
  
  -- Secondary PayPal Account (optional)
  client_id_2 VARCHAR(255) NULL,
  secret_key_2 VARCHAR(500) NULL, -- Encrypted
  
  -- Configuration (giữ nguyên như hiện tại)
  email VARCHAR(255) NOT NULL,
  hidden_product_name BOOLEAN DEFAULT FALSE,
  
  -- Status (chỉ deactivate, không update)
  is_active BOOLEAN DEFAULT TRUE,
  
  -- Metadata
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(100) NULL,
  
  -- Indexes
  INDEX idx_port (port),
  INDEX idx_client_id (client_id),
  INDEX idx_active (is_active)
);
```

### 2. **Cache Strategy (Đơn giản hóa)**

#### Multi-layer Caching
```javascript
// Layer 1: Application Memory Cache (LRU)
const configMemoryCache = new LRUCache({
  max: 1000,
  ttl: 5 * 60 * 1000 // 5 minutes
});

// Layer 2: Redis Cache
const CACHE_KEYS = {
  CONFIG_BY_PORT: 'paypal:config:port:',
  CONFIG_BY_CLIENT_ID: 'paypal:config:client_id:',
  ALL_ACTIVE_CONFIGS: 'paypal:configs:active'
};

// Layer 3: Database (fallback)
```

#### Cache Strategy (Không cần invalidation vì không update)
```javascript
// Chỉ cache read operations
// Khi deactivate: clear cache cho config đó
// Khi add new: không cần clear cache (tự động miss và load)
```

### 3. **Encryption Strategy**

#### Credential Encryption
```javascript
// Sử dụng AES-256-GCM encryption
const ENCRYPTION_KEY = process.env.PAYPAL_ENCRYPTION_KEY; // 32 bytes
const IV_LENGTH = 16; // For AES, this is always 16

function encryptCredential(text) {
  const iv = crypto.randomBytes(IV_LENGTH);
  const cipher = crypto.createCipher('aes-256-gcm', ENCRYPTION_KEY);
  cipher.setAAD(Buffer.from('paypal-credential'));
  
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  const authTag = cipher.getAuthTag();
  
  return {
    iv: iv.toString('hex'),
    encryptedData: encrypted,
    authTag: authTag.toString('hex')
  };
}
```

## 🔄 Migration Plan (Đơn giản hóa)

### Phase 1: Database Setup
1. Tạo PayPalConfigs table (không cần audit table)
2. Migrate data từ paypal.json
3. Encrypt credentials
4. Verify data integrity

### Phase 2: Service Layer
1. Tạo PayPalConfigService (chỉ read operations)
2. Implement caching layer
3. Add encryption/decryption
4. Create simple admin APIs (list + add + deactivate)

### Phase 3: Integration
1. Update PayPalService để sử dụng database
2. Backward compatibility
3. Testing
4. Gradual rollout

### Phase 4: Cleanup
1. Remove file-based config
2. Update documentation

## 📊 Performance Benefits

### Before (File-based)
- **Config load time**: 5-10ms per request
- **Memory usage**: ~2MB for all configs
- **Scalability**: Limited by file I/O
- **Cache hit rate**: 0% (no caching)

### After (Database + Cache)
- **Config load time**: 0.1ms (memory cache) / 1ms (Redis) / 5ms (DB)
- **Memory usage**: ~500KB (only active configs)
- **Scalability**: Horizontal scaling ready
- **Cache hit rate**: 95%+ expected

## 🔒 Security Improvements

### Current Security Issues
- ❌ Plain text credentials
- ❌ No access control
- ❌ No audit trail
- ❌ Version control exposure

### New Security Features
- ✅ AES-256-GCM encryption
- ✅ Role-based access control
- ✅ Complete audit trail
- ✅ Credential rotation support
- ✅ Environment separation
- ✅ IP-based access restrictions

## 🛠 Implementation Details

### Service Architecture
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   PayPal API    │    │  Config Service  │    │   Admin Panel   │
│   Integration   │◄──►│                  │◄──►│                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   Cache Layer    │
                       │  Memory + Redis  │
                       └──────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │    Database      │
                       │ PayPalConfigs +  │
                       │ Audit Tables     │
                       └──────────────────┘
```

### API Endpoints (Đơn giản hóa)
```javascript
// Admin APIs
GET    /api/admin/paypal-configs          // List all configs (show port)
POST   /api/admin/paypal-configs          // Add new config
PUT    /api/admin/paypal-configs/:id/deactivate // Deactivate config

// Internal APIs (cho PayPalService sử dụng)
GET    /api/internal/paypal-config/port/:port        // Get by port
GET    /api/internal/paypal-config/client-id/:id     // Get by client_id
```

## 📈 Monitoring & Alerting

### Metrics to Track
- Config cache hit/miss rates
- Database query performance
- Encryption/decryption times
- Failed authentication attempts
- Config change frequency

### Alerts
- Cache hit rate < 90%
- Database response time > 100ms
- Failed config lookups
- Unauthorized access attempts
- Credential rotation due dates

---

*Xem [Implementation Plan](./implementation-plan.md) để có hướng dẫn chi tiết từng bước.*
