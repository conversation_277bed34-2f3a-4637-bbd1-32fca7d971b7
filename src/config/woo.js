import axios from "axios";
import dotenv from "dotenv";

dotenv.config();

const consumerKey = "ck_9eb7bb32ef2d39df2931d106ecb5bfbcc97dce30";
const consumerSecret = "cs_bb58f4304c9d3f8ed9b109b9ed5eaf3196565b84";

const getAuthHeader = () => {
  const token = Buffer.from(
    `${consumerKey}:${consumerSecret}`,
    "utf8",
  ).toString("base64");
  return `Basic ${token}`;
};
const getDomain = (domain) => `https://admin.${domain}/wp-json/wc/v3`;

export const createOrder = async (orderData, baseUrl) => {
  try {
    const response = await axios.post(
      `${getDomain(baseUrl)}/orders`,
      orderData,
      {
        headers: {
          Authorization: getAuthHeader(),
          "Content-Type": "application/json",
        },
      },
    );
    const newOrder = response.data;

    const note = `PayPal transaction ID: ${orderData.transaction_id}`;
    await addOrderNote(newOrder.id, note, baseUrl);

    return newOrder;
  } catch (error) {
    console.error(
      "Error creating order:",
      error.response ? error.response.data : error.message,
    );
    throw error;
  }
};

export const addOrderNote = async (orderId, note, baseUrl) => {
  try {
    const response = await axios.post(
      `${getDomain(baseUrl)}/orders/${orderId}/notes`,
      {
        note,
      },
      {
        headers: {
          Authorization: getAuthHeader(),
          "Content-Type": "application/json",
        },
      },
    );
    return response.data;
  } catch (error) {
    console.error(
      "Error adding order note:",
      error.response ? error.response.data : error.message,
    );
    throw error;
  }
};
