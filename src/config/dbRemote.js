import { Sequelize } from "sequelize";
import dotenv from "dotenv";

dotenv.config();

const remoteMySQLConfig = {
  database: process.env.REMOTE_DB_NAME,
  username: process.env.REMOTE_DB_USER,
  password: process.env.REMOTE_DB_PASSWORD,
  host: "localhost",
  dialect: "mysql",
  timezone: "-07:00",
  logging: false,
};

const sequelizeRemote = new Sequelize(
  remoteMySQLConfig.database,
  remoteMySQLConfig.username,
  remoteMySQLConfig.password,
  {
    host: "localhost",
    dialect: remoteMySQLConfig.dialect,
    timezone: remoteMySQLConfig.timezone,
    port: 3308,
  },
);

export default sequelizeRemote;
