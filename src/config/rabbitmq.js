import "dotenv/config";
import amqp from "amqplib";

const RABBITMQ_URL = "********************************************************";

let connection = null;
let channel = null;

// Hàm để lấy tên queue dựa trên môi trường
function getQueueName(baseName) {
  const prefix = process.env.NODE_ENV === "local" ? "local_" : "";
  return `${prefix}${baseName}`;
}
// Đ<PERSON>nh nghĩa các queue
const QUEUES = {
  EMAIL: getQueueName("email_queue"),
  ORDER: getQueueName("order_queue"),
  NOTIFICATION: getQueueName("notification_queue"),
  TRUESTORE: getQueueName("truestore_queue"),
};

async function connectQueue() {
  if (!connection) {
    connection = await amqp.connect(RABBITMQ_URL);
    channel = await connection.createChannel();

    // Setup error handlers
    connection.on("error", (err) => {
      console.error("RabbitMQ connection error", err);
      connection = null;
      channel = null;
    });

    connection.on("close", () => {
      console.log("RabbitMQ connection closed");
      connection = null;
      channel = null;
    });
  }

  if (!channel) {
    channel = await connection.createChannel();
  }

  return { connection, channel };
}

export async function sendToQueue(queueKey, type, data) {
  if (!QUEUES[queueKey]) {
    throw new Error(`Invalid queue key: ${queueKey}`);
  }

  if (!channel) {
    await connectQueue();
  }

  const message = JSON.stringify({ type, data });
  return channel.sendToQueue(QUEUES[queueKey], Buffer.from(message), {
    persistent: true,
  });
}

// Optionally, add a function to close the connection when needed
export async function closeConnection() {
  if (channel) {
    await channel.close();
  }
  if (connection) {
    await connection.close();
  }
  channel = null;
  connection = null;
}

// Ensure the connection is closed when the process exits
process.on("exit", () => {
  closeConnection();
});

export { QUEUES };
