import { Sequelize } from "sequelize";
import dotenv from "dotenv";

dotenv.config();

const sequelizeRemote2 = new Sequelize(
  process.env.DB2_NAME,
  process.env.DB2_USER,
  process.env.DB2_PASSWORD,
  {
    host: process.env.DB2_HOST,
    dialect: "mysql",
    timezone: "-07:00",
    dialectOptions: {
      dateStrings: true,
      typeCast(field, next) {
        // for reading from database
        if (field.type === "DATETIME") {
          return field.string();
        }
        return next();
      },
    },
    logging: false,
  },
);

export default sequelizeRemote2;
