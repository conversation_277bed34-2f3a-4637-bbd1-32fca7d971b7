import fs from "fs";
import axios from "axios";
import path from "path";
import { fileURLToPath } from "url";
import Order from "../models/order.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const consumerKey = "ck_9eb7bb32ef2d39df2931d106ecb5bfbcc97dce30";
const consumerSecret = "cs_bb58f4304c9d3f8ed9b109b9ed5eaf3196565b84";

const getAuthHeader = () => {
  const token = Buffer.from(
    `${consumerKey}:${consumerSecret}`,
    "utf8",
  ).toString("base64");
  return `Basic ${token}`;
};
const getDomain = (domain) => `https://admin.${domain}/wp-json/wc/v3`;

const fetchAllOrders = async (
  domain,
  startDate,
  ordersNodejs,
  perPage = 20,
) => {
  const transactionIds = ordersNodejs.map(
    (order) => order.orderData.transaction_id,
  );

  const startDateiso = new Date(startDate).toISOString().replace(".000Z", "");
  let page = 1;
  let allOrders = [];
  let moreOrdersAvailable = true;

  while (moreOrdersAvailable) {
    try {
      const url = `${getDomain(
        domain,
      )}/orders?after=${startDateiso}&per_page=${perPage}&page=${page}&orderby=date&order=desc`;
      await sleep(500);
      console.log(`Fetch order ${domain} từ ${startDate} page ${page}`);
      const response = await axios.get(url, {
        headers: {
          Authorization: getAuthHeader(),
        },
      });

      const orders = response.data;
      const filteredOrders = orders.filter(
        (order) =>
          transactionIds.includes(order.transaction_id) &&
          order.line_items.length,
      );
      allOrders = allOrders.concat(filteredOrders);
      console.log(
        `Đã tìm được order ${allOrders.length} so với ${transactionIds.length}`,
      );
      if (
        orders.length < perPage ||
        allOrders.length === transactionIds.length
      ) {
        moreOrdersAvailable = false;
      } else {
        page++;
      }
    } catch (error) {
      console.error("Error fetching orders:", error);
      throw error;
    }
  }

  ordersNodejs.forEach((order) => {
    const matchedOrder = allOrders.find(
      (o) => o.transaction_id === order.orderData.transaction_id,
    );
    if (matchedOrder && matchedOrder.line_items.length) {
      order.orderData.line_items = matchedOrder.line_items.map((i2) => ({
        product_id: i2.product_id,
        variation_id: i2.variation_id,
        quantity: i2.quantity,
      }));
      console.log(order.orderData.line_items);
    }
  });

  return {
    ordersNodejs,
    allOrders,
  };
};

const updateOrderLineItems = async (orders) => {
  for (const order of orders) {
    console.log(`Đang update ${order.transaction_id}`);

    await Order.update(
      { orderData: order.orderData },
      { where: { transaction_id: order.transaction_id } },
    );
  }
  return true;
};

async function findAndCountAll() {
  const allOrders = await Order.findAll({
    order: [["createdAt", "DESC"]],
  });

  const ordersWithoutLineItems = allOrders.filter(
    (order) => order.orderData.line_items.length === 0,
  );

  const domainCounts = {};

  ordersWithoutLineItems.forEach((order) => {
    const { domain } = order;
    const createdAt = new Date(order.createdAt);

    if (!domainCounts[domain]) {
      domainCounts[domain] = {
        count: 0,
        minCreatedAt: createdAt,
        maxCreatedAt: createdAt,
        ordersNodejs: [],
      };
    }

    domainCounts[domain].count++;
    if (createdAt < domainCounts[domain].minCreatedAt) {
      domainCounts[domain].minCreatedAt = createdAt;
    }
    if (createdAt > domainCounts[domain].maxCreatedAt) {
      domainCounts[domain].maxCreatedAt = createdAt;
    }
    domainCounts[domain].ordersNodejs.push(order);
  });
  for (const [key, value] of Object.entries(domainCounts)) {
    const fetchedOrders = await fetchAllOrders(
      key,
      value.minCreatedAt,
      value.ordersNodejs,
    );
    domainCounts[key].orders = fetchedOrders;
    await updateOrderLineItems(fetchedOrders.ordersNodejs);
  }
  console.log(
    `Có ${ordersWithoutLineItems.length} order bị thiếu trên tổng số ${allOrders.length}`,
  );

  const filesDir = path.join(__dirname, "./files");
  if (!fs.existsSync(filesDir)) {
    fs.mkdirSync(filesDir);
  }

  fs.writeFileSync(
    path.join(filesDir, "orders_without_line_items.json"),
    JSON.stringify(ordersWithoutLineItems, null, 2),
  );

  fs.writeFileSync(
    path.join(filesDir, "domain_counts.json"),
    JSON.stringify(domainCounts, null, 2),
  );
}
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
findAndCountAll();
