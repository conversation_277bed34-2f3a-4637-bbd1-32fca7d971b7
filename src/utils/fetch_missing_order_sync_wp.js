import { Op, Sequelize } from "sequelize";
import { subMinutes } from "date-fns";
import path from "path";
import { fileURLToPath } from "url";
import fs from "fs";
import axios from "axios";
import Order from "../models/order.js";
import { convertToClientTimezone } from "../utils.js";
import HistoryOrder from "../models/historyOrderRemote.js";
import DomainConfig from "../models/domainConfig.js";
import logger from "./logger.js";
import OrderSync from "../models/orderSync.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const startdate = "04-07-2024";
const enddate = "18-07-2024";
// const domain = "oarlogla.com";

const username = "devtruestore";
const password = "pas72ns2ws5ord";

const consumerKey = "ck_9eb7bb32ef2d39df2931d106ecb5bfbcc97dce30";
const consumerSecret = "cs_bb58f4304c9d3f8ed9b109b9ed5eaf3196565b84";

const getAuthHeader = () => {
  const token = Buffer.from(
    `${consumerKey}:${consumerSecret}`,
    "utf8",
  ).toString("base64");
  return `Basic ${token}`;
};
const getDomain = (domain) => `https://admin.${domain}/wp-json/wc/v3`;

async function createOrderFailed(order) {
  try {
    // logger.error(`Error creating `);
    await sleep(1000);
    const url = `${getDomain(order.domain)}/orders`;
    order.orderData.set_paid = false;
    order.orderData.status = "failed";
    logger.error(
      `Đang tạo order $${order.domain} với transaction ${order.orderData.transaction_id} thời gian ${order.createdAt}`,
    );
    order.orderData.line_items = order.orderData.line_items.map((i) => ({
      quantity: i.quantity,
      product_id: i.product_id,
      variation_id: i.variation_id,
    }));
    const response = await axios.post(url, order.orderData, {
      headers: {
        Authorization: getAuthHeader(),
        "Content-Type": "application/json", // Đảm bảo Content-Type là JSON
      },
    });
    const orderWp = response.data;
    return OrderSync.create({
      domain: order.domain,
      transaction_id: order.orderData.transaction_id,
      orderId: orderWp.id,
      orderData: orderWp,
      orderCreatedAt: order.createdAt,
    });
  } catch (error) {
    logger.error(error);
    console.log(error);
  }
}

async function compareOrdersHistory() {
  try {
    const { startDate, endDate } = convertToClientTimezone(startdate, enddate);

    const orders = await Order.findAll({
      where: {
        createdAt: {
          [Op.between]: [startDate, subMinutes(endDate, 33)],
        },
      },
      attributes: ["transaction_id", "domain", "id", "orderData", "createdAt"],
    });

    const historyOrders = await HistoryOrder.findAll({
      where: {
        DateOrder: {
          [Op.between]: [startDate, endDate],
        },
        TotalPrice: {
          [Sequelize.Op.gt]: 0,
        },
      },
      include: [
        {
          model: DomainConfig,
          where: {
            port: {
              [Op.ne]: "cardshield",
            },
          },
        },
      ],
      attributes: [
        "TransactionIds",
        "Domain",
        "id",
        "OrderID",
        "Date",
        "TotalPrice",
        "LineitemQuantity",
        "ItemName",
        "ShippingName",
        "ShippingAddress1",
        "ShippingAddress2",
        "ShippingCompany",
        "ShippingCity",
        "ShippingZip",
        "ShippingProvince",
        "ShippingCountry",
        "ShippingPhone",
        "PaymentAccount",
        "CustomerEmail",
        "Status",
        "ShipPrice",
        "Sku",
        "LinkProduct",
        "DateOrder",
        "Noti",
      ],
    });

    const orderTransactionIds = orders.map((order) => order.transaction_id);
    const historyOrderTransactionIds = historyOrders.map(
      (historyOrder) => historyOrder.TransactionIds,
    );

    const missingTransactions = orderTransactionIds.filter(
      (transactionId) => !historyOrderTransactionIds.includes(transactionId),
    );

    const missingOrders = orders
      .filter((order) => missingTransactions.includes(order.transaction_id))
      .filter((order) => {
        const orderDate = new Date(order.createdAt);
        return orderDate.getDate() !== 9;
      });

    const missingDomains = [];
    missingOrders.forEach((order) => {
      if (!missingDomains.includes(order.domain)) {
        missingDomains.push(order.domain);
      }
    });

    const domainConfigs = await getDomainConfigs(missingDomains);

    const domainWpMap = new Map(
      domainConfigs.map((config) => [
        config.domain,
        `orders-${config.port}.com`,
      ]),
    );
    console.log(domainWpMap);

    let missingOrdersWithWp = missingOrders.map((order) => ({
      ...order.dataValues,
      wp: domainWpMap.get(order.domain) || null,
    }));

    console.log(`Có ${missingOrdersWithWp.length} đơn hàng bị thiếu`);
    missingOrdersWithWp = missingOrdersWithWp.filter(
      (order) =>
        order.orderData.line_items && order.orderData.line_items.length > 0,
    );
    console.log(
      `Còn lại ${missingOrdersWithWp.length} đơn hàng bị thiếu và có line items`,
    );
    for (const order of missingOrdersWithWp) {
      await createOrderFailed(order);
    }

    const filesDir = path.join(__dirname, "./files");
    if (!fs.existsSync(filesDir)) {
      fs.mkdirSync(filesDir);
    }

    fs.writeFileSync(
      path.join(filesDir, "missingOrdersWithWp.json"),
      JSON.stringify(missingOrdersWithWp, null, 2),
    );

    return true;
  } catch (error) {
    console.log(error);
    return false;
  }
}
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

async function getDomainConfigs(missingDomains) {
  try {
    const domainConfigs = await DomainConfig.findAll({
      where: {
        domain: {
          [Op.in]: missingDomains,
        },
      },
    });

    return domainConfigs;
  } catch (error) {
    console.error("Error fetching domain configs:", error);
    throw error;
  }
}

compareOrdersHistory();
