import currency from "currency.js"; // Import thư viện currency.js n<PERSON>u cần thiết
import DomainReport from "../models/domainReport.js";
import Order from "../models/order.js";
import SaleReport from "../models/saleReport.js";

const updateReports = async () => {
  // L<PERSON>y t<PERSON>t cả các DomainReports
  const domainReports = await DomainReport.findAll();
  for (const report of domainReports) {
    const { orderIds } = report;
    const orders = await Order.findAll({ where: { id: orderIds } });

    const updatedStats = [
      {
        total_sales: 0,
        total_orders: 0,
        total_shipping: 0,
        items: [],
      },
    ];

    for (const order of orders) {
      updatedStats[0].total_orders += 1;
      updatedStats[0].total_sales = currency(updatedStats[0].total_sales).add(
        order.orderData.total,
      ).value;
      updatedStats[0].total_shipping = currency(
        updatedStats[0].total_shipping,
      ).add(order.orderData.shipping_total).value;

      const productSale = order.orderData.line_items[0];
      const productIndex = updatedStats[0].items.findIndex(
        (p) => p.product_id === productSale.product_id,
      );

      if (productIndex === -1) {
        updatedStats[0].items.push({
          product_id: productSale.product_id,
          quantity: 1,
          revenue: order.orderData.total,
          product_url: productSale.product_link,
          gross: currency(order.orderData.total).subtract(
            order.orderData.shipping_total,
          ).value,
        });
      } else {
        updatedStats[0].items[productIndex].quantity += 1;
        updatedStats[0].items[productIndex].gross = currency(
          updatedStats[0].items[productIndex].gross,
        )
          .add(order.orderData.total)
          .subtract(order.orderData.shipping_total).value;
        updatedStats[0].items[productIndex].revenue = currency(
          updatedStats[0].items[productIndex].revenue,
        ).add(order.orderData.total).value;
      }
    }

    report.set("Stats", updatedStats);
    await report.save();
  }

  // Tương tự cho SaleReports
  const saleReports = await SaleReport.findAll();
  for (const report of saleReports) {
    const { orderIds } = report;
    const orders = await Order.findAll({ where: { id: orderIds } });

    let ordersCount = 0;
    let totalMoney = 0;
    const updatedStats = {};

    for (const order of orders) {
      ordersCount += 1;
      totalMoney = currency(totalMoney).add(order.orderData.total).value;

      const productSale = order.orderData.line_items[0];
      if (!updatedStats[productSale.product_id]) {
        updatedStats[productSale.product_id] = {
          quantity: 0,
          revenue: 0,
          name: productSale.name,
          link: productSale.product_link,
          thumbnail: productSale.image,
        };
      }
      updatedStats[productSale.product_id].quantity += 1;
      updatedStats[productSale.product_id].revenue = currency(
        updatedStats[productSale.product_id].revenue,
      ).add(order.orderData.total).value;
    }

    report.orders = ordersCount;
    report.money = totalMoney;
    report.set("Stats", updatedStats);
    await report.save();
  }
};

updateReports()
  .then(() => {
    console.log("Reports have been updated.");
  })
  .catch((error) => {
    console.error("Error updating reports:", error);
  });
