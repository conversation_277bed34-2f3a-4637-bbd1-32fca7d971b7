import winston from "winston";
import "winston-daily-rotate-file";
import path from "path";
import fs from "fs";

const logDir = "logs";

// Ki<PERSON><PERSON> tra và tạo thư mục logs nếu chưa tồn tại
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

// Transport cho log thông thường
const dailyRotateFileTransport = new winston.transports.DailyRotateFile({
  filename: path.join(logDir, "application-%DATE%.log"),
  datePattern: "YYYY-MM-DD",
  zippedArchive: true,
  maxSize: "20m",
  maxFiles: "60d",
  level: "info", // Ghi log từ mức info trở lên
});

// Transport cho log lỗi
const errorFileTransport = new winston.transports.DailyRotateFile({
  filename: path.join(logDir, "error-%DATE%.log"),
  datePattern: "YYYY-MM-DD",
  zippedArchive: true,
  maxSize: "20m",
  maxFiles: "90d",
  level: "error", // Chỉ ghi log lỗi
});

const logger = winston.createLogger({
  level: "silly", // Vẫn giữ mức log thấp nhất để có thể ghi tất cả các loại log
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.splat(),
    winston.format.json(),
  ),
  transports: [
    dailyRotateFileTransport,
    errorFileTransport,
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple(),
      ),
      level: "silly", // Ghi tất cả các mức log ra console
    }),
  ],
});

// Thêm hàm helper để ghi log với thông tin chi tiết
logger.logWithDetails = (level, message, details) => {
  logger.log(level, message, {
    ...details,
    timestamp: new Date().toISOString(),
  });
};

export default logger;
