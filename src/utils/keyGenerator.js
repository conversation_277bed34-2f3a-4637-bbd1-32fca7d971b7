import crypto from 'crypto';

/**
 * Get meta data value by key
 */
function getMetaDataValue(metaData, key) {
  const item = metaData.find(item => item.key === key);
  return item ? item.value : null;
}

/**
 * Generate unique user key for cache
 * Format: domain:ip:ua_hash
 */
function generateUniqueUserKey(domain, metaData) {
  const ip = getMetaDataValue(metaData, 'ip');
  const userAgent = getMetaDataValue(metaData, 'UA');
  
  if (!ip || !userAgent) {
    throw new Error('Missing required meta_data: ip or UA');
  }
  
  // Hash UA để rút gọn (SHA-256 → 8 chars đầu)
  const uaHash = crypto.createHash('sha256')
    .update(userAgent)
    .digest('hex')
    .substring(0, 8);
    
  return `${domain}:${ip}:${uaHash}`;
}

/**
 * Generate order data hash (MD5 của business-critical fields ONLY)
 * 
 * IMPORTANT: CHỈ include fields ảnh hưởng đến business logic:
 * ✅ line_items (products, quantities, prices, meta_data)
 * ✅ shipping_lines (method, total)
 * 
 * ❌ KHÔNG include random/session fields:
 * ❌ invoice_id (FE random generated mỗi lần)
 * ❌ timestamps, session IDs, random tokens
 * 
 * Nếu include random fields → Cache không bao giờ hit!
 */
function generateOrderDataHash(lineItems, shippingLines) {
  // ONLY include business-critical fields
  const orderData = {
    line_items: lineItems,
    shipping_lines: shippingLines || []
  };
  
  // Sort để đảm bảo consistent hash
  if (orderData.line_items) {
    orderData.line_items = orderData.line_items
      .map(item => ({
        sku: item.sku || item.name || '',
        name: item.name || '',
        quantity: item.quantity || 0,
        price: item.price || 0,
        meta_data: item.meta_data || []
      }))
      .sort((a, b) => (a.sku || a.name).localeCompare(b.sku || b.name));
  }
  
  if (orderData.shipping_lines) {
    orderData.shipping_lines = orderData.shipping_lines
      .map(line => ({
        method_title: line.method_title || '',
        total: line.total || '0'
      }))
      .sort((a, b) => a.method_title.localeCompare(b.method_title));
  }
  
  // MD5 hash của JSON string (FULL 32 chars for zero collision risk)
  return crypto.createHash('md5')
    .update(JSON.stringify(orderData))
    .digest('hex'); // Full 32-char hash eliminates collision possibility
}

/**
 * Generate draft order cache key
 * Format: draft_order:domain:ip:ua_hash:order_data_hash
 *
 * PURPOSE:
 * - Cache PayPal order ID để tránh tạo duplicate draft orders
 * - Được set khi tạo draft order thành công trong createDraftOrderForPayPalV2
 * - Được check trước khi tạo draft order mới (performance optimization)
 * - TTL: DRAFT_ORDER_TTL (1800s = 30 minutes)
 * - CRITICAL: Kiểm tra order status trong DB trước khi return cached draft
 */
function generateDraftCacheKey(domain, metaData, lineItems, shippingLines) {
  const uniqueUserKey = generateUniqueUserKey(domain, metaData);
  const orderDataHash = generateOrderDataHash(lineItems, shippingLines);

  return `draft_order:${uniqueUserKey}:${orderDataHash}`;
}

/**
 * Generate success order cache key
 * Format: success_order:domain:ip:ua_hash:order_data_hash
 *
 * PURPOSE:
 * - Ngăn chặn duplicate orders trong DUPLICATE_CHECK_WINDOW (300s = 5 minutes)
 * - Được set khi order capture thành công trong handleCapturePayPalV2
 * - Được check đầu tiên trong createDraftOrderForPayPalV2 (early prevention)
 * - TTL: SUCCESS_ORDER_TTL (7200s = 2 hours)
 */
function generateSuccessCacheKey(domain, metaData, lineItems, shippingLines) {
  const uniqueUserKey = generateUniqueUserKey(domain, metaData);
  const orderDataHash = generateOrderDataHash(lineItems, shippingLines);

  return `success_order:${uniqueUserKey}:${orderDataHash}`;
}

/**
 * INVOICE-BASED SAFETY LAYER
 *
 * Generate captured invoice cache key để track invoices đã được capture
 * Format: captured_invoice:domain:invoice_id
 *
 * PURPOSE:
 * - Ngăn chặn việc tạo draft order mới với invoice_id đã được capture
 * - Được set khi order capture thành công trong handleCapturePayPalV2
 * - Được check trong createDraftOrderForPayPalV2 trước khi tạo draft mới
 * - TTL: SUCCESS_ORDER_TTL (7200s = 2 hours)
 */
function generateCapturedInvoiceCacheKey(domain, invoiceId) {
  return `captured_invoice:${domain}:${invoiceId}`;
}

export {
  getMetaDataValue,
  generateUniqueUserKey,
  generateOrderDataHash,
  generateDraftCacheKey,
  generateSuccessCacheKey,
  generateCapturedInvoiceCacheKey
};