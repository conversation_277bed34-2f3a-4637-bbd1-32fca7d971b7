const fs = require("fs").promises;
const path = require("path");
const Handlebars = require("handlebars");

// Cache để lưu trữ các template đã compiled
const templateCache = {};

// Cache để lưu trữ các partial đã registered
const partialCache = {};

// Đường dẫn đến thư mục templates
const TEMPLATE_DIR = path.join(__dirname, "..", "templates");
const EMAIL_TEMPLATE_DIR = path.join(TEMPLATE_DIR, "emails");
const PARTIAL_DIR = path.join(TEMPLATE_DIR, "partials");

/**
 * Đ<PERSON><PERSON> và compile một template
 * @param {string} templateName - Tên của template file (không bao gồm đuôi .html)
 * @returns {Promise<Function>} - Hàm template đã được compiled
 */
async function getCompiledTemplate(templateName) {
  if (templateCache[templateName]) {
    return templateCache[templateName];
  }

  const templatePath = path.join(EMAIL_TEMPLATE_DIR, `${templateName}.html`);
  const templateContent = await fs.readFile(templatePath, "utf-8");
  const compiledTemplate = Handlebars.compile(templateContent);
  templateCache[templateName] = compiledTemplate;

  return compiledTemplate;
}

/**
 * Đọc và đăng ký một partial
 * @param {string} partialName - Tên của partial file (không bao gồm đuôi .html)
 */
async function registerPartial(partialName) {
  if (partialCache[partialName]) {
    return;
  }

  const partialPath = path.join(PARTIAL_DIR, `${partialName}.html`);
  const partialContent = await fs.readFile(partialPath, "utf-8");
  Handlebars.registerPartial(partialName, partialContent);
  partialCache[partialName] = true;
}

/**
 * Render một template với dữ liệu được cung cấp
 * @param {string} templateName - Tên của template
 * @param {Object} data - Dữ liệu để render template
 * @returns {Promise<string>} - HTML đã được render
 */
async function render(templateName, data) {
  try {
    // Đăng ký tất cả các partials trong thư mục partials
    const partialFiles = await fs.readdir(PARTIAL_DIR);
    for (const file of partialFiles) {
      const partialName = path.basename(file, ".html");
      await registerPartial(partialName);
    }

    // Lấy và render template
    const compiledTemplate = await getCompiledTemplate(templateName);
    return compiledTemplate(data);
  } catch (error) {
    console.error(`Error rendering template ${templateName}:`, error);
    throw error;
  }
}

/**
 * Xóa cache của một template cụ thể
 * @param {string} templateName - Tên của template cần xóa khỏi cache
 */
function clearTemplateCache(templateName) {
  delete templateCache[templateName];
}

/**
 * Xóa toàn bộ cache
 */
function clearAllCache() {
  Object.keys(templateCache).forEach((key) => delete templateCache[key]);
  Object.keys(partialCache).forEach((key) => {
    delete partialCache[key];
    Handlebars.unregisterPartial(key);
  });
}

// Đăng ký một helper để format tiền tệ
Handlebars.registerHelper("currency", (value) =>
  new Intl.NumberFormat("en-US", { style: "currency", currency: "USD" }).format(
    value,
  ),
);

module.exports = {
  render,
  clearTemplateCache,
  clearAllCache,
};
