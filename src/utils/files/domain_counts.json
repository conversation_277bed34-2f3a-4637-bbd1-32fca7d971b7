{"sugranie.com": {"count": 19, "minCreatedAt": "2024-07-08T22:31:02.000Z", "maxCreatedAt": "2024-07-08T22:42:16.000Z", "ordersNodejs": [{"id": 2487, "domain": "sugranie.com", "transaction_id": "49C76083CA512605V", "orderData": {"billing": {"city": "Bensalem", "email": "<EMAIL>", "phone": "", "state": "PA", "country": "US", "postcode": "19020", "address_1": "2290 Galloway Road, Apt. B7", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": true, "shipping": {"city": "Bensalem", "state": "PA", "country": "US", "postcode": "19020", "address_1": "2290 Galloway Road, Apt. B7", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,7;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620452201]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHcmDMOnTxJaIYpSxKhq-n-odVNXQLDeSRDQQlDFXU_Ba0i1_1g1Jstr-dA_aem_-NXHQFJFtGTczQfo9hnFEA&utm_source=facebook&utm_medium=paid&campaign_id=120210821690160238&ad_id=120210821690210238"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "sugranie-zjy3x-17011"}, {"key": "_ppcp_paypal_order_id", "value": "49C76083CA512605V"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "49C76083CA512605V", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:38:27", "createdAt": "2024-07-09 05:42:16", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2484, "domain": "sugranie.com", "transaction_id": "3UR725811V808931N", "orderData": {"billing": {"city": "<PERSON>", "email": "<EMAIL>", "phone": "", "state": "NC", "country": "US", "postcode": "28524", "address_1": "272 Croaker St", "address_2": "", "last_name": "Styron", "first_name": "<PERSON>"}, "set_paid": true, "shipping": {"city": "<PERSON>", "state": "NC", "country": "US", "postcode": "28524", "address_1": "272 Croaker St", "address_2": "", "last_name": "Styron", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 13; SM-G781V Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.134 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/471.*********;]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHfmx0PL5ZMV_oTTMM3p49vS7jv7Zhzr7padof66udVaV1kiX4RG-X-LvZw_aem_up57ageE_XpBu_tP-wZ8fg&utm_source=facebook&utm_medium=paid&campaign_id=120209665041930485&ad_id=120209665042830485&utm_id=120209665041680485&utm_content=120209665042830485&utm_term=120209665041930485&utm_campaign=120209665041680485"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120209665041680485DHV120209665042830485DHV120209665041930485"}, {"key": "invoice_id", "value": "sugranie-6ud3r-98740"}, {"key": "_ppcp_paypal_order_id", "value": "3UR725811V808931N"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "3UR725811V808931N", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:38:12", "createdAt": "2024-07-09 05:41:38", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2482, "domain": "sugranie.com", "transaction_id": "5DM14140F1235274U", "orderData": {"billing": {"city": "Muskogee", "email": "<EMAIL>", "phone": "", "state": "OK", "country": "US", "postcode": "74403", "address_1": "3001 Hilltop Avenue ", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "set_paid": true, "shipping": {"city": "Muskogee", "state": "OK", "country": "US", "postcode": "74403", "address_1": "3001 Hilltop Avenue ", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,7;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/619949523]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHc4hu6ZYSzrtClQ7g7PrpzOw4s7viZnSrCF7XWbcwKE9ysAgXwPYT_U9qQ_aem_CmG6Dud_fr6nkLnIABNvdA&utm_source=facebook&utm_medium=paid&campaign_id=120209078577700519&ad_id=120209078577880519&utm_id=120209078577530519&utm_content=120209078577880519&utm_term=120209078577700519&utm_campaign=120209078577530519"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120209078577530519DHV120209078577880519DHV120209078577700519"}, {"key": "invoice_id", "value": "sugranie-iz9ws-33705"}, {"key": "_ppcp_paypal_order_id", "value": "5DM14140F1235274U"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "5DM14140F1235274U", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:37:56", "createdAt": "2024-07-09 05:41:08", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2480, "domain": "sugranie.com", "transaction_id": "8VG87564UR8665202", "orderData": {"billing": {"city": "<PERSON>", "email": "shauna<PERSON><PERSON><PERSON>@me.com", "phone": "", "state": "OK", "country": "US", "postcode": "73013", "address_1": "2007 Sumac Circle", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "set_paid": true, "shipping": {"city": "<PERSON>", "state": "OK", "country": "US", "postcode": "73013", "address_1": "2007 Sumac Circle", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone13,4;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620244209]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHUCM2tP5N--97veD6NAZG8r_GRv5xhT-5rgNdJq6t0mjQBk2fsfvEARV3w_aem_opOzSxW7DRmHh0pQ8hS0_Q&utm_source=facebook&utm_medium=paid&campaign_id=120213926284330091&ad_id=120213926284340091&utm_id=120213926284300091&utm_content=120213926284340091&utm_term=120213926284330091&utm_campaign=120213926284300091"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120213926284300091DHV120213926284340091DHV120213926284330091"}, {"key": "invoice_id", "value": "sugranie-tabnx-34997"}, {"key": "_ppcp_paypal_order_id", "value": "8VG87564UR8665202"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "8VG87564UR8665202", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:37:45", "createdAt": "2024-07-09 05:40:29", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2474, "domain": "sugranie.com", "transaction_id": "9CL16574YN618721C", "orderData": {"billing": {"city": "Atlanta", "email": "<EMAIL>", "phone": "", "state": "GA", "country": "US", "postcode": "30331", "address_1": "3450 Old Fairburn Rd SW", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "set_paid": true, "shipping": {"city": "Atlanta", "state": "GA", "country": "US", "postcode": "30331", "address_1": "3450 Old Fairburn Rd SW", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21E236 [FBAN/FBIOS;FBAV/461.**********;FBBV/591474467;FBDV/iPhone14,5;FBMD/iPhone;FBSN/iOS;FBSV/17.4.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/598035438]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHaugB8U5eAfTJh3CvGbc4iMSBZUXG2IXx3bjfyCPhzY1jcRpJRL3lU_CXw_aem_564EIgDkBFPH-8UPln_eGw&utm_source=facebook&utm_medium=paid&campaign_id=120210847939210722&ad_id=120210847939230722"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "sugranie-px855-64054"}, {"key": "_ppcp_paypal_order_id", "value": "9CL16574YN618721C"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "9CL16574YN618721C", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:37:06", "createdAt": "2024-07-09 05:39:30", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2473, "domain": "sugranie.com", "transaction_id": "4GK49855SS249260E", "orderData": {"billing": {"city": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "", "state": "MT", "country": "US", "postcode": "59101-4841", "address_1": "4615 Bruce Ave", "address_2": "", "last_name": "EVANGELINE", "first_name": "VALERIE"}, "set_paid": true, "shipping": {"city": "<PERSON><PERSON>", "state": "MT", "country": "US", "postcode": "59101-4841", "address_1": "4615 Bruce Ave", "address_2": "", "last_name": "EVANGELINE", "first_name": "VALERIE"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 14; SM-S918U Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.162 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/471.*********;] [FB_IAB/FB4A;FBAV/471.*********;]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHRySG421YvZBffhCCbuka_oSlN6HHuIx6_FzldjisqTT5zcwOe6qsYH0LA_aem_LDssh-MNRnQexLAe-Rgetg&utm_source=facebook&utm_medium=paid&campaign_id=120210586406620487&ad_id=120210586406690487"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "sugranie-k4qis-32574"}, {"key": "_ppcp_paypal_order_id", "value": "4GK49855SS249260E"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "4GK49855SS249260E", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:37:01", "createdAt": "2024-07-09 05:39:10", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2471, "domain": "sugranie.com", "transaction_id": "2JM88309S2735182P", "orderData": {"billing": {"city": "Clyde", "email": "<EMAIL>", "phone": "", "state": "NC", "country": "US", "postcode": "28721", "address_1": "192 <PERSON><PERSON> Dr", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "Juan<PERSON>"}, "set_paid": true, "shipping": {"city": "Clyde", "state": "NC", "country": "US", "postcode": "28721", "address_1": "192 <PERSON><PERSON> Dr", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "Juan<PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,8;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620304316]"}, {"key": "invoice_id", "value": "sugranie-rqae4-60311"}, {"key": "_ppcp_paypal_order_id", "value": "2JM88309S2735182P"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "2JM88309S2735182P", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:36:51", "createdAt": "2024-07-09 05:38:55", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2469, "domain": "sugranie.com", "transaction_id": "2LE56742LC617114Y", "orderData": {"billing": {"city": "SANFORD", "email": "<EMAIL>", "phone": "", "state": "NC", "country": "US", "postcode": "27332", "address_1": "693 Chelsea Dr", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": true, "shipping": {"city": "SANFORD", "state": "NC", "country": "US", "postcode": "27332", "address_1": "693 Chelsea Dr", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPad; CPU OS 16_7_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/20H330 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPad6,11;FBMD/iPad;FBSN/iPadOS;FBSV/16.7.7;FBSS/2;FBID/tablet;FBLC/en_US;FBOP/5;FBRV/620343664]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHWGH0UNNjYHmQfvNdM8iu0PETWQGA6Jc_imMnzKs4IfPw9ByIFiC8c2KHQ_aem_tbLCimWmcL-F3WviA3v2ug&utm_source=facebook&utm_medium=paid&campaign_id=120211088993220740&ad_id=120211088993380740&utm_id=120211088993190740&utm_content=120211088993380740&utm_term=120211088993220740&utm_campaign=120211088993190740"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120211088993190740DHV120211088993380740DHV120211088993220740"}, {"key": "invoice_id", "value": "sugranie-64pu6-79904"}, {"key": "_ppcp_paypal_order_id", "value": "2LE56742LC617114Y"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "2LE56742LC617114Y", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:36:40", "createdAt": "2024-07-09 05:38:25", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2467, "domain": "sugranie.com", "transaction_id": "6Y833457K4227912S", "orderData": {"billing": {"city": "Southgate", "email": "<PERSON><EMAIL>", "phone": "", "state": "MI", "country": "US", "postcode": "48195", "address_1": "14289 Flanders Ave", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON>"}, "set_paid": true, "shipping": {"city": "Southgate", "state": "MI", "country": "US", "postcode": "48195", "address_1": "14289 Flanders Ave", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 14; SM-S918U1 Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.162 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/471.*********;]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHTcAeQOweIr5flYD1B6ai_eu9_bhF0O65KKscrQyTo-NSlKTeG1GZgehnQ_aem_YAyH_2jnk-RTQzXp016tvg&utm_source=facebook&utm_medium=paid&campaign_id=120212385868440215&ad_id=120212385868840215"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "sugranie-4lcje-26221"}, {"key": "_ppcp_paypal_order_id", "value": "6Y833457K4227912S"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "6Y833457K4227912S", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:36:30", "createdAt": "2024-07-09 05:38:11", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2466, "domain": "sugranie.com", "transaction_id": "9MP32315NJ730882J", "orderData": {"billing": {"city": "St Petersburg", "email": "<EMAIL>", "phone": "", "state": "FL", "country": "US", "postcode": "33710", "address_1": "1941 Country Club Rd N", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "set_paid": true, "shipping": {"city": "St Petersburg", "state": "FL", "country": "US", "postcode": "33710", "address_1": "1941 Country Club Rd N", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,5;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620452201]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHXMCAuSFZSRP2Sr2XMGMQevhml3lS60U5U9KR81i081_F-A56A_p1YzkyA_aem_cF1N_NIfWH-IpSFucS0wWg&utm_source=facebook&utm_medium=paid&campaign_id=120210793606340078&ad_id=120210793606500078"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "sugranie-es4so-48561"}, {"key": "_ppcp_paypal_order_id", "value": "9MP32315NJ730882J"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "9MP32315NJ730882J", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:36:24", "createdAt": "2024-07-09 05:38:08", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2462, "domain": "sugranie.com", "transaction_id": "44G21989SA327772V", "orderData": {"billing": {"city": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "", "state": "TN", "country": "US", "postcode": "37757", "address_1": "192 Archery", "address_2": "", "last_name": "dobbs", "first_name": "wilda"}, "set_paid": true, "shipping": {"city": "<PERSON><PERSON>", "state": "TN", "country": "US", "postcode": "37757", "address_1": "192 Archery", "address_2": "", "last_name": "dobbs", "first_name": "wilda"}, "meta_data": [{"key": "ip", "value": "***************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,3;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620083054]"}, {"key": "invoice_id", "value": "sugranie-4iphl-64969"}, {"key": "_ppcp_paypal_order_id", "value": "44G21989SA327772V"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "44G21989SA327772V", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:35:53", "createdAt": "2024-07-09 05:37:46", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2461, "domain": "sugranie.com", "transaction_id": "76G72729MA799973V", "orderData": {"billing": {"city": "<PERSON>", "email": "shauna<PERSON><PERSON><PERSON>@me.com", "phone": "", "state": "OK", "country": "US", "postcode": "73013", "address_1": "2007 Sumac Circle", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "set_paid": true, "shipping": {"city": "<PERSON>", "state": "OK", "country": "US", "postcode": "73013", "address_1": "2007 Sumac Circle", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone13,4;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620244209]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHUCM2tP5N--97veD6NAZG8r_GRv5xhT-5rgNdJq6t0mjQBk2fsfvEARV3w_aem_opOzSxW7DRmHh0pQ8hS0_Q&utm_source=facebook&utm_medium=paid&campaign_id=120213926284330091&ad_id=120213926284340091&utm_id=120213926284300091&utm_content=120213926284340091&utm_term=120213926284330091&utm_campaign=120213926284300091"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120213926284300091DHV120213926284340091DHV120213926284330091"}, {"key": "invoice_id", "value": "sugranie-7tlo1-19770"}, {"key": "_ppcp_paypal_order_id", "value": "76G72729MA799973V"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "76G72729MA799973V", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:35:47", "createdAt": "2024-07-09 05:37:41", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2460, "domain": "sugranie.com", "transaction_id": "82580172TX798980E", "orderData": {"billing": {"city": "FORT LAUDERDALE", "email": "<EMAIL>", "phone": "", "state": "FL", "country": "US", "postcode": "33309", "address_1": "6848 NW 28th Ave", "address_2": "", "last_name": "BOLENDER", "first_name": "MARY"}, "set_paid": true, "shipping": {"city": "FORT LAUDERDALE", "state": "FL", "country": "US", "postcode": "33309", "address_1": "6848 NW 28th Ave", "address_2": "", "last_name": "BOLENDER", "first_name": "MARY"}, "meta_data": [{"key": "ip", "value": "***************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone12,5;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620373543]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHY9tomzrS6jx4fRn-c4cvwUuQyF5bRurHWvu4Cc0QcOgoT0ASUXnHZDROA_aem_sAtVVeTqF38tpCmXBwd10Q&utm_source=facebook&utm_medium=paid&campaign_id=120210666672440236&ad_id=120210666672720236"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "sugranie-g1x1w-97848"}, {"key": "_ppcp_paypal_order_id", "value": "82580172TX798980E"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "82580172TX798980E", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:35:42", "createdAt": "2024-07-09 05:37:18", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2451, "domain": "sugranie.com", "transaction_id": "4V963559576311746", "orderData": {"billing": {"city": "Romeoville", "email": "<EMAIL>", "phone": "", "state": "IL", "country": "US", "postcode": "60446", "address_1": "1594 Baytree Drive", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "Agata"}, "set_paid": true, "shipping": {"city": "Romeoville", "state": "IL", "country": "US", "postcode": "60446", "address_1": "1594 Baytree Drive", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "Agata"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21G5061c [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone16,2;FBMD/iPhone;FBSN/iOS;FBSV/17.6;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620343664]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHTbkorfTG629l6ukvvQAJ-40T_DLqIZnAz724hqUrSajFnXFsTF6ucl52A_aem_pbHIJ8LF9W8YEN-CHlXGqw&utm_source=facebook&utm_medium=paid&campaign_id=120210625797680291&ad_id=120210625797780291&utm_id=120210625797570291&utm_content=120210625797780291&utm_term=120210625797680291&utm_campaign=120210625797570291"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120210625797570291DHV120210625797780291DHV120210625797680291"}, {"key": "invoice_id", "value": "sugranie-szhtp-39999"}, {"key": "_ppcp_paypal_order_id", "value": "4V963559576311746"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "4V963559576311746", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:34:49", "createdAt": "2024-07-09 05:35:18", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2450, "domain": "sugranie.com", "transaction_id": "2NS39030JJ826582U", "orderData": {"billing": {"city": "Brooklyn", "email": "<EMAIL>", "phone": "", "state": "NY", "country": "US", "postcode": "11235", "address_1": "601Brighwater cort apr 2B", "address_2": "", "last_name": "Fuks", "first_name": "E<PERSON><PERSON><PERSON>"}, "set_paid": true, "shipping": {"city": "Brooklyn", "state": "NY", "country": "US", "postcode": "11235", "address_1": "601Brighwater cort apr 2B", "address_2": "", "last_name": "Fuks", "first_name": "E<PERSON><PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 14; SM-X700 Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.162 Safari/537.36 [FB_IAB/FB4A;FBAV/471.*********;]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHYbITEWEi2QF2ZIZuzLljjz95mbIKbhJMlTAtlAYfRnPzXxUJ5JqiNzJCg_aem_IMtoG3HhCFCePnAFAzXXwg&utm_source=facebook&utm_medium=paid&campaign_id=120208609885680284&ad_id=120208609885690284&utm_id=120208609885630284&utm_content=120208609885690284&utm_term=120208609885680284&utm_campaign=120208609885630284"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120208609885630284DHV120208609885690284DHV120208609885680284"}, {"key": "invoice_id", "value": "sugranie-7dq6s-97785"}, {"key": "_ppcp_paypal_order_id", "value": "2NS39030JJ826582U"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "2NS39030JJ826582U", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:34:44", "createdAt": "2024-07-09 05:35:12", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2446, "domain": "sugranie.com", "transaction_id": "4JJ36511A0851643K", "orderData": {"billing": {"city": "Grand Ledge", "email": "<EMAIL>", "phone": "", "state": "MI", "country": "US", "postcode": "48837", "address_1": "7622 <PERSON>", "address_2": "", "last_name": "FOSTER", "first_name": "MARYANNA"}, "set_paid": true, "shipping": {"city": "Grand Ledge", "state": "MI", "country": "US", "postcode": "48837", "address_1": "7622 <PERSON>", "address_2": "", "last_name": "FOSTER", "first_name": "MARYANNA"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/445.**********;FBBV/548375166;FBDV/iPhone15,4;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/555570286]"}, {"key": "invoice_id", "value": "sugranie-36ebx-87312"}, {"key": "_ppcp_paypal_order_id", "value": "4JJ36511A0851643K"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "4JJ36511A0851643K", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:34:16", "createdAt": "2024-07-09 05:35:01", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2441, "domain": "sugranie.com", "transaction_id": "6YN22768VB130635P", "orderData": {"billing": {"city": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "", "state": "NJ", "country": "US", "postcode": "07042", "address_1": "45 Church St", "address_2": "A2", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "set_paid": true, "shipping": {"city": "<PERSON><PERSON><PERSON>", "state": "NJ", "country": "US", "postcode": "07042", "address_1": "45 Church St", "address_2": "A2", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone12,1;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/2;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620452201]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHQVsBf_XCCvb8MJbCp62_ohCr2vSNMU3Sk9zYHtQda93pb_wyCR4wzzWIA_aem_9Jh4udHbjSLXYo1Gl5BH3g&utm_source=facebook&utm_medium=paid&campaign_id=120209853265420123&ad_id=120209853266570123&utm_id=120209853264750123&utm_content=120209853266570123&utm_term=120209853265420123&utm_campaign=120209853264750123"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120209853264750123DHV120209853266570123DHV120209853265420123"}, {"key": "invoice_id", "value": "sugranie-ta6mx-46645"}, {"key": "_ppcp_paypal_order_id", "value": "6YN22768VB130635P"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "6YN22768VB130635P", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:34:05", "createdAt": "2024-07-09 05:32:51", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2439, "domain": "sugranie.com", "transaction_id": "9WA80718MP0869235", "orderData": {"billing": {"city": "Cape Coral", "email": "<EMAIL>", "phone": "", "state": "FL", "country": "US", "postcode": "33904", "address_1": "3521 SE 8th Pl", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": true, "shipping": {"city": "Cape Coral", "state": "FL", "country": "US", "postcode": "33904", "address_1": "3521 SE 8th Pl", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPad; CPU OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPad13,8;FBMD/iPad;FBSN/iPadOS;FBSV/17.5.1;FBSS/2;FBID/tablet;FBLC/en_US;FBOP/5;FBRV/620550028]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHaXSNG-8ZlvpgEPaWURTcPVwhb7Txg0rcRzOB28rzzrdUYcvMHwg_iu__g_aem_wNblKF9rBOvZ6XGpC4HYog&utm_source=facebook&utm_medium=paid&campaign_id=120211517772180312&ad_id=120211517772210312&utm_id=120211517772130312&utm_content=120211517772210312&utm_term=120211517772180312&utm_campaign=120211517772130312"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120211517772130312DHV120211517772210312DHV120211517772180312"}, {"key": "invoice_id", "value": "sugranie-tfu3n-97368"}, {"key": "_ppcp_paypal_order_id", "value": "9WA80718MP0869235"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "9WA80718MP0869235", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:33:49", "createdAt": "2024-07-09 05:31:52", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2437, "domain": "sugranie.com", "transaction_id": "53G43748F1891793C", "orderData": {"billing": {"city": "north ridgeville", "email": "<EMAIL>", "phone": "", "state": "OH", "country": "US", "postcode": "44039-2406", "address_1": "5768 bayberry cir", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "set_paid": true, "shipping": {"city": "north ridgeville", "state": "OH", "country": "US", "postcode": "44039-2406", "address_1": "5768 bayberry cir", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,5;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620452201]"}, {"key": "invoice_id", "value": "sugranie-clg0j-28240"}, {"key": "_ppcp_paypal_order_id", "value": "53G43748F1891793C"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "53G43748F1891793C", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:33:39", "createdAt": "2024-07-09 05:31:02", "updatedAt": "2024-07-10 10:03:23"}], "orders": {"ordersNodejs": [{"id": 2487, "domain": "sugranie.com", "transaction_id": "49C76083CA512605V", "orderData": {"billing": {"city": "Bensalem", "email": "<EMAIL>", "phone": "", "state": "PA", "country": "US", "postcode": "19020", "address_1": "2290 Galloway Road, Apt. B7", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": true, "shipping": {"city": "Bensalem", "state": "PA", "country": "US", "postcode": "19020", "address_1": "2290 Galloway Road, Apt. B7", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,7;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620452201]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHcmDMOnTxJaIYpSxKhq-n-odVNXQLDeSRDQQlDFXU_Ba0i1_1g1Jstr-dA_aem_-NXHQFJFtGTczQfo9hnFEA&utm_source=facebook&utm_medium=paid&campaign_id=120210821690160238&ad_id=120210821690210238"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "sugranie-zjy3x-17011"}, {"key": "_ppcp_paypal_order_id", "value": "49C76083CA512605V"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "49C76083CA512605V", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:38:27", "createdAt": "2024-07-09 05:42:16", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2484, "domain": "sugranie.com", "transaction_id": "3UR725811V808931N", "orderData": {"billing": {"city": "<PERSON>", "email": "<EMAIL>", "phone": "", "state": "NC", "country": "US", "postcode": "28524", "address_1": "272 Croaker St", "address_2": "", "last_name": "Styron", "first_name": "<PERSON>"}, "set_paid": true, "shipping": {"city": "<PERSON>", "state": "NC", "country": "US", "postcode": "28524", "address_1": "272 Croaker St", "address_2": "", "last_name": "Styron", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 13; SM-G781V Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.134 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/471.*********;]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHfmx0PL5ZMV_oTTMM3p49vS7jv7Zhzr7padof66udVaV1kiX4RG-X-LvZw_aem_up57ageE_XpBu_tP-wZ8fg&utm_source=facebook&utm_medium=paid&campaign_id=120209665041930485&ad_id=120209665042830485&utm_id=120209665041680485&utm_content=120209665042830485&utm_term=120209665041930485&utm_campaign=120209665041680485"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120209665041680485DHV120209665042830485DHV120209665041930485"}, {"key": "invoice_id", "value": "sugranie-6ud3r-98740"}, {"key": "_ppcp_paypal_order_id", "value": "3UR725811V808931N"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "3UR725811V808931N", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:38:12", "createdAt": "2024-07-09 05:41:38", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2482, "domain": "sugranie.com", "transaction_id": "5DM14140F1235274U", "orderData": {"billing": {"city": "Muskogee", "email": "<EMAIL>", "phone": "", "state": "OK", "country": "US", "postcode": "74403", "address_1": "3001 Hilltop Avenue ", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "set_paid": true, "shipping": {"city": "Muskogee", "state": "OK", "country": "US", "postcode": "74403", "address_1": "3001 Hilltop Avenue ", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,7;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/619949523]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHc4hu6ZYSzrtClQ7g7PrpzOw4s7viZnSrCF7XWbcwKE9ysAgXwPYT_U9qQ_aem_CmG6Dud_fr6nkLnIABNvdA&utm_source=facebook&utm_medium=paid&campaign_id=120209078577700519&ad_id=120209078577880519&utm_id=120209078577530519&utm_content=120209078577880519&utm_term=120209078577700519&utm_campaign=120209078577530519"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120209078577530519DHV120209078577880519DHV120209078577700519"}, {"key": "invoice_id", "value": "sugranie-iz9ws-33705"}, {"key": "_ppcp_paypal_order_id", "value": "5DM14140F1235274U"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "5DM14140F1235274U", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:37:56", "createdAt": "2024-07-09 05:41:08", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2480, "domain": "sugranie.com", "transaction_id": "8VG87564UR8665202", "orderData": {"billing": {"city": "<PERSON>", "email": "shauna<PERSON><PERSON><PERSON>@me.com", "phone": "", "state": "OK", "country": "US", "postcode": "73013", "address_1": "2007 Sumac Circle", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "set_paid": true, "shipping": {"city": "<PERSON>", "state": "OK", "country": "US", "postcode": "73013", "address_1": "2007 Sumac Circle", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone13,4;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620244209]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHUCM2tP5N--97veD6NAZG8r_GRv5xhT-5rgNdJq6t0mjQBk2fsfvEARV3w_aem_opOzSxW7DRmHh0pQ8hS0_Q&utm_source=facebook&utm_medium=paid&campaign_id=120213926284330091&ad_id=120213926284340091&utm_id=120213926284300091&utm_content=120213926284340091&utm_term=120213926284330091&utm_campaign=120213926284300091"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120213926284300091DHV120213926284340091DHV120213926284330091"}, {"key": "invoice_id", "value": "sugranie-tabnx-34997"}, {"key": "_ppcp_paypal_order_id", "value": "8VG87564UR8665202"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "8VG87564UR8665202", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:37:45", "createdAt": "2024-07-09 05:40:29", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2474, "domain": "sugranie.com", "transaction_id": "9CL16574YN618721C", "orderData": {"billing": {"city": "Atlanta", "email": "<EMAIL>", "phone": "", "state": "GA", "country": "US", "postcode": "30331", "address_1": "3450 Old Fairburn Rd SW", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "set_paid": true, "shipping": {"city": "Atlanta", "state": "GA", "country": "US", "postcode": "30331", "address_1": "3450 Old Fairburn Rd SW", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21E236 [FBAN/FBIOS;FBAV/461.**********;FBBV/591474467;FBDV/iPhone14,5;FBMD/iPhone;FBSN/iOS;FBSV/17.4.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/598035438]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHaugB8U5eAfTJh3CvGbc4iMSBZUXG2IXx3bjfyCPhzY1jcRpJRL3lU_CXw_aem_564EIgDkBFPH-8UPln_eGw&utm_source=facebook&utm_medium=paid&campaign_id=120210847939210722&ad_id=120210847939230722"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "sugranie-px855-64054"}, {"key": "_ppcp_paypal_order_id", "value": "9CL16574YN618721C"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "9CL16574YN618721C", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:37:06", "createdAt": "2024-07-09 05:39:30", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2473, "domain": "sugranie.com", "transaction_id": "4GK49855SS249260E", "orderData": {"billing": {"city": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "", "state": "MT", "country": "US", "postcode": "59101-4841", "address_1": "4615 Bruce Ave", "address_2": "", "last_name": "EVANGELINE", "first_name": "VALERIE"}, "set_paid": true, "shipping": {"city": "<PERSON><PERSON>", "state": "MT", "country": "US", "postcode": "59101-4841", "address_1": "4615 Bruce Ave", "address_2": "", "last_name": "EVANGELINE", "first_name": "VALERIE"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 14; SM-S918U Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.162 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/471.*********;] [FB_IAB/FB4A;FBAV/471.*********;]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHRySG421YvZBffhCCbuka_oSlN6HHuIx6_FzldjisqTT5zcwOe6qsYH0LA_aem_LDssh-MNRnQexLAe-Rgetg&utm_source=facebook&utm_medium=paid&campaign_id=120210586406620487&ad_id=120210586406690487"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "sugranie-k4qis-32574"}, {"key": "_ppcp_paypal_order_id", "value": "4GK49855SS249260E"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "4GK49855SS249260E", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:37:01", "createdAt": "2024-07-09 05:39:10", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2471, "domain": "sugranie.com", "transaction_id": "2JM88309S2735182P", "orderData": {"billing": {"city": "Clyde", "email": "<EMAIL>", "phone": "", "state": "NC", "country": "US", "postcode": "28721", "address_1": "192 <PERSON><PERSON> Dr", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "Juan<PERSON>"}, "set_paid": true, "shipping": {"city": "Clyde", "state": "NC", "country": "US", "postcode": "28721", "address_1": "192 <PERSON><PERSON> Dr", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "Juan<PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,8;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620304316]"}, {"key": "invoice_id", "value": "sugranie-rqae4-60311"}, {"key": "_ppcp_paypal_order_id", "value": "2JM88309S2735182P"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "2JM88309S2735182P", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:36:51", "createdAt": "2024-07-09 05:38:55", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2469, "domain": "sugranie.com", "transaction_id": "2LE56742LC617114Y", "orderData": {"billing": {"city": "SANFORD", "email": "<EMAIL>", "phone": "", "state": "NC", "country": "US", "postcode": "27332", "address_1": "693 Chelsea Dr", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": true, "shipping": {"city": "SANFORD", "state": "NC", "country": "US", "postcode": "27332", "address_1": "693 Chelsea Dr", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPad; CPU OS 16_7_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/20H330 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPad6,11;FBMD/iPad;FBSN/iPadOS;FBSV/16.7.7;FBSS/2;FBID/tablet;FBLC/en_US;FBOP/5;FBRV/620343664]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHWGH0UNNjYHmQfvNdM8iu0PETWQGA6Jc_imMnzKs4IfPw9ByIFiC8c2KHQ_aem_tbLCimWmcL-F3WviA3v2ug&utm_source=facebook&utm_medium=paid&campaign_id=120211088993220740&ad_id=120211088993380740&utm_id=120211088993190740&utm_content=120211088993380740&utm_term=120211088993220740&utm_campaign=120211088993190740"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120211088993190740DHV120211088993380740DHV120211088993220740"}, {"key": "invoice_id", "value": "sugranie-64pu6-79904"}, {"key": "_ppcp_paypal_order_id", "value": "2LE56742LC617114Y"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "2LE56742LC617114Y", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:36:40", "createdAt": "2024-07-09 05:38:25", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2467, "domain": "sugranie.com", "transaction_id": "6Y833457K4227912S", "orderData": {"billing": {"city": "Southgate", "email": "<PERSON><EMAIL>", "phone": "", "state": "MI", "country": "US", "postcode": "48195", "address_1": "14289 Flanders Ave", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON>"}, "set_paid": true, "shipping": {"city": "Southgate", "state": "MI", "country": "US", "postcode": "48195", "address_1": "14289 Flanders Ave", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 14; SM-S918U1 Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.162 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/471.*********;]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHTcAeQOweIr5flYD1B6ai_eu9_bhF0O65KKscrQyTo-NSlKTeG1GZgehnQ_aem_YAyH_2jnk-RTQzXp016tvg&utm_source=facebook&utm_medium=paid&campaign_id=120212385868440215&ad_id=120212385868840215"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "sugranie-4lcje-26221"}, {"key": "_ppcp_paypal_order_id", "value": "6Y833457K4227912S"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "6Y833457K4227912S", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:36:30", "createdAt": "2024-07-09 05:38:11", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2466, "domain": "sugranie.com", "transaction_id": "9MP32315NJ730882J", "orderData": {"billing": {"city": "St Petersburg", "email": "<EMAIL>", "phone": "", "state": "FL", "country": "US", "postcode": "33710", "address_1": "1941 Country Club Rd N", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "set_paid": true, "shipping": {"city": "St Petersburg", "state": "FL", "country": "US", "postcode": "33710", "address_1": "1941 Country Club Rd N", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,5;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620452201]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHXMCAuSFZSRP2Sr2XMGMQevhml3lS60U5U9KR81i081_F-A56A_p1YzkyA_aem_cF1N_NIfWH-IpSFucS0wWg&utm_source=facebook&utm_medium=paid&campaign_id=120210793606340078&ad_id=120210793606500078"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "sugranie-es4so-48561"}, {"key": "_ppcp_paypal_order_id", "value": "9MP32315NJ730882J"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "9MP32315NJ730882J", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:36:24", "createdAt": "2024-07-09 05:38:08", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2462, "domain": "sugranie.com", "transaction_id": "44G21989SA327772V", "orderData": {"billing": {"city": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "", "state": "TN", "country": "US", "postcode": "37757", "address_1": "192 Archery", "address_2": "", "last_name": "dobbs", "first_name": "wilda"}, "set_paid": true, "shipping": {"city": "<PERSON><PERSON>", "state": "TN", "country": "US", "postcode": "37757", "address_1": "192 Archery", "address_2": "", "last_name": "dobbs", "first_name": "wilda"}, "meta_data": [{"key": "ip", "value": "***************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,3;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620083054]"}, {"key": "invoice_id", "value": "sugranie-4iphl-64969"}, {"key": "_ppcp_paypal_order_id", "value": "44G21989SA327772V"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "44G21989SA327772V", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:35:53", "createdAt": "2024-07-09 05:37:46", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2461, "domain": "sugranie.com", "transaction_id": "76G72729MA799973V", "orderData": {"billing": {"city": "<PERSON>", "email": "shauna<PERSON><PERSON><PERSON>@me.com", "phone": "", "state": "OK", "country": "US", "postcode": "73013", "address_1": "2007 Sumac Circle", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "set_paid": true, "shipping": {"city": "<PERSON>", "state": "OK", "country": "US", "postcode": "73013", "address_1": "2007 Sumac Circle", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone13,4;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620244209]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHUCM2tP5N--97veD6NAZG8r_GRv5xhT-5rgNdJq6t0mjQBk2fsfvEARV3w_aem_opOzSxW7DRmHh0pQ8hS0_Q&utm_source=facebook&utm_medium=paid&campaign_id=120213926284330091&ad_id=120213926284340091&utm_id=120213926284300091&utm_content=120213926284340091&utm_term=120213926284330091&utm_campaign=120213926284300091"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120213926284300091DHV120213926284340091DHV120213926284330091"}, {"key": "invoice_id", "value": "sugranie-7tlo1-19770"}, {"key": "_ppcp_paypal_order_id", "value": "76G72729MA799973V"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "76G72729MA799973V", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:35:47", "createdAt": "2024-07-09 05:37:41", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2460, "domain": "sugranie.com", "transaction_id": "82580172TX798980E", "orderData": {"billing": {"city": "FORT LAUDERDALE", "email": "<EMAIL>", "phone": "", "state": "FL", "country": "US", "postcode": "33309", "address_1": "6848 NW 28th Ave", "address_2": "", "last_name": "BOLENDER", "first_name": "MARY"}, "set_paid": true, "shipping": {"city": "FORT LAUDERDALE", "state": "FL", "country": "US", "postcode": "33309", "address_1": "6848 NW 28th Ave", "address_2": "", "last_name": "BOLENDER", "first_name": "MARY"}, "meta_data": [{"key": "ip", "value": "***************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone12,5;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620373543]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHY9tomzrS6jx4fRn-c4cvwUuQyF5bRurHWvu4Cc0QcOgoT0ASUXnHZDROA_aem_sAtVVeTqF38tpCmXBwd10Q&utm_source=facebook&utm_medium=paid&campaign_id=120210666672440236&ad_id=120210666672720236"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "sugranie-g1x1w-97848"}, {"key": "_ppcp_paypal_order_id", "value": "82580172TX798980E"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "82580172TX798980E", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:35:42", "createdAt": "2024-07-09 05:37:18", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2451, "domain": "sugranie.com", "transaction_id": "4V963559576311746", "orderData": {"billing": {"city": "Romeoville", "email": "<EMAIL>", "phone": "", "state": "IL", "country": "US", "postcode": "60446", "address_1": "1594 Baytree Drive", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "Agata"}, "set_paid": true, "shipping": {"city": "Romeoville", "state": "IL", "country": "US", "postcode": "60446", "address_1": "1594 Baytree Drive", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "Agata"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21G5061c [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone16,2;FBMD/iPhone;FBSN/iOS;FBSV/17.6;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620343664]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHTbkorfTG629l6ukvvQAJ-40T_DLqIZnAz724hqUrSajFnXFsTF6ucl52A_aem_pbHIJ8LF9W8YEN-CHlXGqw&utm_source=facebook&utm_medium=paid&campaign_id=120210625797680291&ad_id=120210625797780291&utm_id=120210625797570291&utm_content=120210625797780291&utm_term=120210625797680291&utm_campaign=120210625797570291"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120210625797570291DHV120210625797780291DHV120210625797680291"}, {"key": "invoice_id", "value": "sugranie-szhtp-39999"}, {"key": "_ppcp_paypal_order_id", "value": "4V963559576311746"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "4V963559576311746", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:34:49", "createdAt": "2024-07-09 05:35:18", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2450, "domain": "sugranie.com", "transaction_id": "2NS39030JJ826582U", "orderData": {"billing": {"city": "Brooklyn", "email": "<EMAIL>", "phone": "", "state": "NY", "country": "US", "postcode": "11235", "address_1": "601Brighwater cort apr 2B", "address_2": "", "last_name": "Fuks", "first_name": "E<PERSON><PERSON><PERSON>"}, "set_paid": true, "shipping": {"city": "Brooklyn", "state": "NY", "country": "US", "postcode": "11235", "address_1": "601Brighwater cort apr 2B", "address_2": "", "last_name": "Fuks", "first_name": "E<PERSON><PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 14; SM-X700 Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.162 Safari/537.36 [FB_IAB/FB4A;FBAV/471.*********;]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHYbITEWEi2QF2ZIZuzLljjz95mbIKbhJMlTAtlAYfRnPzXxUJ5JqiNzJCg_aem_IMtoG3HhCFCePnAFAzXXwg&utm_source=facebook&utm_medium=paid&campaign_id=120208609885680284&ad_id=120208609885690284&utm_id=120208609885630284&utm_content=120208609885690284&utm_term=120208609885680284&utm_campaign=120208609885630284"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120208609885630284DHV120208609885690284DHV120208609885680284"}, {"key": "invoice_id", "value": "sugranie-7dq6s-97785"}, {"key": "_ppcp_paypal_order_id", "value": "2NS39030JJ826582U"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "2NS39030JJ826582U", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:34:44", "createdAt": "2024-07-09 05:35:12", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2446, "domain": "sugranie.com", "transaction_id": "4JJ36511A0851643K", "orderData": {"billing": {"city": "Grand Ledge", "email": "<EMAIL>", "phone": "", "state": "MI", "country": "US", "postcode": "48837", "address_1": "7622 <PERSON>", "address_2": "", "last_name": "FOSTER", "first_name": "MARYANNA"}, "set_paid": true, "shipping": {"city": "Grand Ledge", "state": "MI", "country": "US", "postcode": "48837", "address_1": "7622 <PERSON>", "address_2": "", "last_name": "FOSTER", "first_name": "MARYANNA"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/445.**********;FBBV/548375166;FBDV/iPhone15,4;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/555570286]"}, {"key": "invoice_id", "value": "sugranie-36ebx-87312"}, {"key": "_ppcp_paypal_order_id", "value": "4JJ36511A0851643K"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "4JJ36511A0851643K", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:34:16", "createdAt": "2024-07-09 05:35:01", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2441, "domain": "sugranie.com", "transaction_id": "6YN22768VB130635P", "orderData": {"billing": {"city": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "", "state": "NJ", "country": "US", "postcode": "07042", "address_1": "45 Church St", "address_2": "A2", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "set_paid": true, "shipping": {"city": "<PERSON><PERSON><PERSON>", "state": "NJ", "country": "US", "postcode": "07042", "address_1": "45 Church St", "address_2": "A2", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone12,1;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/2;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620452201]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHQVsBf_XCCvb8MJbCp62_ohCr2vSNMU3Sk9zYHtQda93pb_wyCR4wzzWIA_aem_9Jh4udHbjSLXYo1Gl5BH3g&utm_source=facebook&utm_medium=paid&campaign_id=120209853265420123&ad_id=120209853266570123&utm_id=120209853264750123&utm_content=120209853266570123&utm_term=120209853265420123&utm_campaign=120209853264750123"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120209853264750123DHV120209853266570123DHV120209853265420123"}, {"key": "invoice_id", "value": "sugranie-ta6mx-46645"}, {"key": "_ppcp_paypal_order_id", "value": "6YN22768VB130635P"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "6YN22768VB130635P", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:34:05", "createdAt": "2024-07-09 05:32:51", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2439, "domain": "sugranie.com", "transaction_id": "9WA80718MP0869235", "orderData": {"billing": {"city": "Cape Coral", "email": "<EMAIL>", "phone": "", "state": "FL", "country": "US", "postcode": "33904", "address_1": "3521 SE 8th Pl", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": true, "shipping": {"city": "Cape Coral", "state": "FL", "country": "US", "postcode": "33904", "address_1": "3521 SE 8th Pl", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPad; CPU OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPad13,8;FBMD/iPad;FBSN/iPadOS;FBSV/17.5.1;FBSS/2;FBID/tablet;FBLC/en_US;FBOP/5;FBRV/620550028]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHaXSNG-8ZlvpgEPaWURTcPVwhb7Txg0rcRzOB28rzzrdUYcvMHwg_iu__g_aem_wNblKF9rBOvZ6XGpC4HYog&utm_source=facebook&utm_medium=paid&campaign_id=120211517772180312&ad_id=120211517772210312&utm_id=120211517772130312&utm_content=120211517772210312&utm_term=120211517772180312&utm_campaign=120211517772130312"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120211517772130312DHV120211517772210312DHV120211517772180312"}, {"key": "invoice_id", "value": "sugranie-tfu3n-97368"}, {"key": "_ppcp_paypal_order_id", "value": "9WA80718MP0869235"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "9WA80718MP0869235", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:33:49", "createdAt": "2024-07-09 05:31:52", "updatedAt": "2024-07-10 10:03:23"}, {"id": 2437, "domain": "sugranie.com", "transaction_id": "53G43748F1891793C", "orderData": {"billing": {"city": "north ridgeville", "email": "<EMAIL>", "phone": "", "state": "OH", "country": "US", "postcode": "44039-2406", "address_1": "5768 bayberry cir", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "set_paid": true, "shipping": {"city": "north ridgeville", "state": "OH", "country": "US", "postcode": "44039-2406", "address_1": "5768 bayberry cir", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,5;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620452201]"}, {"key": "invoice_id", "value": "sugranie-clg0j-28240"}, {"key": "_ppcp_paypal_order_id", "value": "53G43748F1891793C"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "53G43748F1891793C", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": "2024-07-09 06:33:39", "createdAt": "2024-07-09 05:31:02", "updatedAt": "2024-07-10 10:03:23"}], "allOrders": []}}, "nimbleusgem.com": {"count": 1, "minCreatedAt": "2024-07-04T12:22:58.000Z", "maxCreatedAt": "2024-07-04T12:22:58.000Z", "ordersNodejs": [{"id": 417, "domain": "nimbleusgem.com", "transaction_id": "2BR36878DN300604K", "orderData": {"billing": {"city": "Bear Lake", "email": "<EMAIL>", "phone": "", "state": "PA", "country": "US", "postcode": "16402", "address_1": "1133 Dyer Road", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "set_paid": true, "shipping": {"city": "Bear Lake", "state": "PA", "country": "US", "postcode": "16402", "address_1": "1133 Dyer Road", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "***********"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 12; SM-A025U1 Build/SP1A.210812.016; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.134 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/471.*********;]"}, {"key": "QUERY", "value": "nimbleusgem.com/product/insectrepellent39?fbclid=IwZXh0bgNhZW0BMAABHcjRZh1PPeAe_9feKxm9NSeiZlXVnUScb_4zE6JVELcxJxB_GIuSPSiBCg_aem_tnP8qYaCGIyFXlEMUyAZ3Q"}, {"key": "invoice_id", "value": "nimbleusgem-wsd28-85576"}, {"key": "_ppcp_paypal_order_id", "value": "2BR36878DN300604K"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "4.99", "method_id": "flat_rate"}], "transaction_id": "2BR36878DN300604K", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": null, "createdAt": "2024-07-04 19:22:58", "updatedAt": "2024-07-10 10:03:32"}], "orders": {"ordersNodejs": [{"id": 417, "domain": "nimbleusgem.com", "transaction_id": "2BR36878DN300604K", "orderData": {"billing": {"city": "Bear Lake", "email": "<EMAIL>", "phone": "", "state": "PA", "country": "US", "postcode": "16402", "address_1": "1133 Dyer Road", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "set_paid": true, "shipping": {"city": "Bear Lake", "state": "PA", "country": "US", "postcode": "16402", "address_1": "1133 Dyer Road", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "***********"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 12; SM-A025U1 Build/SP1A.210812.016; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.134 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/471.*********;]"}, {"key": "QUERY", "value": "nimbleusgem.com/product/insectrepellent39?fbclid=IwZXh0bgNhZW0BMAABHcjRZh1PPeAe_9feKxm9NSeiZlXVnUScb_4zE6JVELcxJxB_GIuSPSiBCg_aem_tnP8qYaCGIyFXlEMUyAZ3Q"}, {"key": "invoice_id", "value": "nimbleusgem-wsd28-85576"}, {"key": "_ppcp_paypal_order_id", "value": "2BR36878DN300604K"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "4.99", "method_id": "flat_rate"}], "transaction_id": "2BR36878DN300604K", "payment_method_title": "<PERSON><PERSON>"}, "importedAt": null, "createdAt": "2024-07-04 19:22:58", "updatedAt": "2024-07-10 10:03:32"}], "allOrders": []}}}