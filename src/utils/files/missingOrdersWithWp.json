[{"transaction_id": "5SA454873G388920K", "domain": "flawlesfashionus.com", "id": 1284, "orderData": {"billing": {"city": "Charleston", "email": "<EMAIL>", "phone": "", "state": "WV", "country": "US", "postcode": "25301", "address_1": "1002 Lee St", "address_2": "#11542", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Charleston", "state": "WV", "country": "US", "postcode": "25301", "address_1": "1002 Lee St", "address_2": "#11542", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,8;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/619596583]"}, {"key": "invoice_id", "value": "flawlesfashionus-hghqw-94348"}, {"key": "_ppcp_paypal_order_id", "value": "5SA454873G388920K"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3657}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "5SA454873G388920K", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-06 18:43:08", "wp": "orders-dth.com"}, {"transaction_id": "0RT69389YJ1545625", "domain": "flawlesfashionus.com", "id": 2124, "orderData": {"billing": {"city": "Carlsbad", "email": "<EMAIL>", "phone": "", "state": "NM", "country": "US", "postcode": "88220-4155", "address_1": "1506 Mountain Shadow Dr", "address_2": "", "last_name": "Day", "first_name": "Tambra"}, "set_paid": false, "shipping": {"city": "Carlsbad", "state": "NM", "country": "US", "postcode": "88220-4155", "address_1": "1506 Mountain Shadow Dr", "address_2": "", "last_name": "Day", "first_name": "Tambra"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,7;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620260954]"}, {"key": "invoice_id", "value": "flawlesfashionus-838er-66732"}, {"key": "_ppcp_paypal_order_id", "value": "0RT69389YJ1545625"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3716}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "0RT69389YJ1545625", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-08 10:49:35", "wp": "orders-dth.com"}, {"transaction_id": "73N20118DA776054K", "domain": "imagineemporiumus.com", "id": 3228, "orderData": {"billing": {"city": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "", "state": "CT", "country": "US", "postcode": "06611-4037", "address_1": "25 Sweetbriar Rd", "address_2": "", "last_name": "ragin", "first_name": "<PERSON><PERSON><PERSON>"}, "set_paid": false, "shipping": {"city": "<PERSON><PERSON><PERSON>", "state": "CT", "country": "US", "postcode": "06611-4037", "address_1": "25 Sweetbriar Rd", "address_2": "", "last_name": "ragin", "first_name": "<PERSON><PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone15,3;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620626117]"}, {"key": "invoice_id", "value": "imagineemporiumus-h1dy1-44934"}, {"key": "_ppcp_paypal_order_id", "value": "73N20118DA776054K"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4637, "variation_id": 4676}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "73N20118DA776054K", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-10 03:18:13", "wp": "orders-dth.com"}, {"transaction_id": "28C56005V0956733R", "domain": "equaldealus.com", "id": 3283, "orderData": {"billing": {"city": "Arcadia", "email": "<EMAIL>", "phone": "", "state": "FL", "country": "US", "postcode": "34269-9501", "address_1": "10307 SW Lettuce Lake Ave", "address_2": "Lot M222", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Arcadia", "state": "FL", "country": "US", "postcode": "34269-9501", "address_1": "10307 SW Lettuce Lake Ave", "address_2": "Lot M222", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 12; SM-A125U1 Build/SP1A.210812.016; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.162 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/471.*********;]"}, {"key": "QUERY", "value": "equaldealus.com/product/speedssd13?fbclid=IwZXh0bgNhZW0BMAABHSUK0QhLHUxhpGS9Gykzz9YqLtSNPCLSC-8oU77pXV1h6c1lub5Snovg3w_aem_qAOaqP38tD9QQShzSpdgvQ"}, {"key": "invoice_id", "value": "equaldealus-2417o-97134"}, {"key": "_ppcp_paypal_order_id", "value": "28C56005V0956733R"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3891, "variation_id": 3897}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "28C56005V0956733R", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-10 05:17:32", "wp": "orders-dth.com"}, {"transaction_id": "5NP682461R200733W", "domain": "flawlesfashionus.com", "id": 3720, "orderData": {"billing": {"city": "<PERSON><PERSON><PERSON> ", "email": "<EMAIL>", "phone": "", "state": "GA", "country": "US", "postcode": "30101", "address_1": "4011 <PERSON>, ", "address_2": "", "last_name": "Grzonka", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "<PERSON><PERSON><PERSON> ", "state": "GA", "country": "US", "postcode": "30101", "address_1": "4011 <PERSON>, ", "address_2": "", "last_name": "Grzonka", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "***********"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,7;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/621005030]"}, {"key": "invoice_id", "value": "flawlesfashionus-lhyzk-48667"}, {"key": "_ppcp_paypal_order_id", "value": "5NP682461R200733W"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3722}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "5NP682461R200733W", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-10 14:00:59", "wp": "orders-dth.com"}, {"transaction_id": "0BX91688UG498970F", "domain": "equaldealus.com", "id": 3752, "orderData": {"billing": {"city": "<PERSON><PERSON><PERSON> <PERSON> ", "email": "<EMAIL>", "phone": "", "state": "FL", "country": "US", "postcode": "33919", "address_1": "13731 Markham Ln ", "address_2": "#4", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "<PERSON><PERSON><PERSON> <PERSON> ", "state": "FL", "country": "US", "postcode": "33919", "address_1": "13731 Markham Ln ", "address_2": "#4", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "***********"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 14; SM-S908U Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.162 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/471.*********;]"}, {"key": "QUERY", "value": "equaldealus.com/product/speedssd13?fbclid=IwZXh0bgNhZW0CMTEAAR2h9uU_jeCmLkaeX-CwWAJmH2mQkwP8x73MUjJE12p_E1cOpiEqCqK3K98_aem_y73o7El-14Mlt2muC6q_-w"}, {"key": "invoice_id", "value": "equaldealus-3gjn8-84267"}, {"key": "_ppcp_paypal_order_id", "value": "0BX91688UG498970F"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3891, "variation_id": 3908}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "0BX91688UG498970F", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-10 15:04:14", "wp": "orders-dth.com"}, {"transaction_id": "7DA95376RG426462S", "domain": "modernnestfinds.com", "id": 3782, "orderData": {"billing": {"city": "Knobnoster ", "email": "<EMAIL>", "phone": "", "state": "MO", "country": "US", "postcode": "65336", "address_1": "55ne 791rd ", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Knobnoster ", "state": "MO", "country": "US", "postcode": "65336", "address_1": "55ne 791rd ", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 14; SM-A146U Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.162 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/471.*********;]"}, {"key": "QUERY", "value": "modernnestfinds.com/product/insectrepellent39?fbclid=IwZXh0bgNhZW0BMAABHerZpj9S3JnqAf8BOFhcdnTeQTp3saqyAZyO30hb_CzHffRYOWNFCMfJ1Q_aem_3DmYrruu2UjG0RSM6hSs9w&utm_medium=paid&utm_source=fb&utm_id=120208827963070068&utm_content=120208827963380068&utm_term=120208827963210068&utm_campaign=120208827963070068"}, {"key": "FB_UTM", "value": "fbDHVpaidDHV120208827963070068DHV120208827963380068DHV120208827963210068"}, {"key": "invoice_id", "value": "modernnestfinds-3qdse-59389"}, {"key": "_ppcp_paypal_order_id", "value": "7DA95376RG426462S"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 1991, "variation_id": 1992}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "4.99", "method_id": "flat_rate"}], "transaction_id": "7DA95376RG426462S", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-10 16:30:00", "wp": "orders-dth.com"}, {"transaction_id": "7HJ09551EK305045S", "domain": "imagineemporiumus.com", "id": 3975, "orderData": {"billing": {"city": "<PERSON>", "email": "<EMAIL>", "phone": "", "state": "NC", "country": "US", "postcode": "28356", "address_1": "9152 Bay Trace Drive", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "set_paid": false, "shipping": {"city": "<PERSON>", "state": "NC", "country": "US", "postcode": "28356", "address_1": "9152 Bay Trace Drive", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone16,2;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/621135866]"}, {"key": "invoice_id", "value": "imagineemporiumus-azsx1-72057"}, {"key": "_ppcp_paypal_order_id", "value": "7HJ09551EK305045S"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4637, "variation_id": 4680}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "7HJ09551EK305045S", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 05:12:26", "wp": "orders-dth.com"}, {"transaction_id": "58F34731WM291504B", "domain": "geargroveus.com", "id": 4001, "orderData": {"billing": {"city": "Confluence", "email": "<EMAIL>", "phone": "", "state": "PA", "country": "US", "postcode": "15424", "address_1": "8004 Kingwood Rd.", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Confluence", "state": "PA", "country": "US", "postcode": "15424", "address_1": "8004 Kingwood Rd.", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 14; SM-S918U Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.170 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/472.*********;]"}, {"key": "QUERY", "value": "geargroveus.com/product/phoneholder-carantislip?fbclid=IwZXh0bgNhZW0BMAABHcqMqLqOsiyV5P9kfz_mFQcKBXUSsfGoSFNx76AhWJBUGxQTTRfCsx5qug_aem_LlvC1q3PHETZ2n2QSM5ing"}, {"key": "invoice_id", "value": "geargroveus-oqgem-95313"}, {"key": "_ppcp_paypal_order_id", "value": "58F34731WM291504B"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4589, "variation_id": 4590}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "58F34731WM291504B", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-11 05:59:18", "wp": "orders-002.com"}, {"transaction_id": "24018463CY934974J", "domain": "magicshoppingus.com", "id": 4002, "orderData": {"billing": {"city": "Huber Heights", "email": "<EMAIL>", "phone": "", "state": "OH", "country": "US", "postcode": "45424", "address_1": "5701 Troy Villa Blvd", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Huber Heights", "state": "OH", "country": "US", "postcode": "45424", "address_1": "5701 Troy Villa Blvd", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (iPad; CPU OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPad7,11;FBMD/iPad;FBSN/iPadOS;FBSV/17.5.1;FBSS/2;FBID/tablet;FBLC/en_US;FBOP/5;FBRV/621135866]"}, {"key": "invoice_id", "value": "magicshoppingus-4fghn-70879"}, {"key": "_ppcp_paypal_order_id", "value": "24018463CY934974J"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 5951, "variation_id": 5980}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "4.99", "method_id": "flat_rate"}], "transaction_id": "24018463CY934974J", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-11 05:59:55", "wp": "orders-222.com"}, {"transaction_id": "9JU10926HP910682B", "domain": "trendcentricus.com", "id": 4004, "orderData": {"billing": {"city": "Palmyra PA", "email": "<EMAIL>", "phone": "", "state": "PA", "country": "US", "postcode": "17078", "address_1": "100 Cottage Dr, <PERSON><PERSON> 109, Palmyra PA Pennsylvania 17078", "address_2": "Apt 109", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Palmyra PA", "state": "PA", "country": "US", "postcode": "17078", "address_1": "100 Cottage Dr, <PERSON><PERSON> 109, Palmyra PA Pennsylvania 17078", "address_2": "Apt 109", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21E236 [FBAN/FBIOS;FBAV/468.**********;FBBV/609862394;FBDV/iPhone14,5;FBMD/iPhone;FBSN/iOS;FBSV/17.4.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/614086270]"}, {"key": "invoice_id", "value": "trendcentricus-6qd9h-28387"}, {"key": "_ppcp_paypal_order_id", "value": "9JU10926HP910682B"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4839, "variation_id": 5005}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "9JU10926HP910682B", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-11 06:04:15", "wp": "orders-002.com"}, {"transaction_id": "4RL14048FT363700C", "domain": "trendcentricus.com", "id": 4006, "orderData": {"billing": {"city": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "", "state": "NJ", "country": "US", "postcode": "08759", "address_1": "41 Meadows Lane", "address_2": "", "last_name": "Montalvo", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "<PERSON><PERSON><PERSON>", "state": "NJ", "country": "US", "postcode": "08759", "address_1": "41 Meadows Lane", "address_2": "", "last_name": "Montalvo", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 14; SM-X200 Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.162 Safari/537.36 [FB_IAB/FB4A;FBAV/471.*********;]"}, {"key": "invoice_id", "value": "trendcentricus-1lkyd-17855"}, {"key": "_ppcp_paypal_order_id", "value": "4RL14048FT363700C"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4839, "variation_id": 4904}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "4RL14048FT363700C", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-11 06:06:55", "wp": "orders-002.com"}, {"transaction_id": "8DM15297803452316", "domain": "geargroveus.com", "id": 4007, "orderData": {"billing": {"city": "South Burlington", "email": "<EMAIL>", "phone": "", "state": "VT", "country": "US", "postcode": "05403", "address_1": "23 Lindenwood Drive", "address_2": "none", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON>"}, "set_paid": false, "shipping": {"city": "South Burlington", "state": "VT", "country": "US", "postcode": "05403", "address_1": "23 Lindenwood Drive", "address_2": "none", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "***********"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 14; SM-S146VL Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.170 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/472.*********;]"}, {"key": "QUERY", "value": "geargroveus.com/product/phoneholder-carantislip?fbclid=IwZXh0bgNhZW0BMAABHWQWeEWOp_oUyJi0683kOe5BrqHqzyydHnTUy-lg2rd_J9XqDGTguKw7Kw_aem_ZUdVI0Xek4Lghkm2E_ScJA"}, {"key": "invoice_id", "value": "geargroveus-zx3gb-86336"}, {"key": "_ppcp_paypal_order_id", "value": "8DM15297803452316"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4589, "variation_id": 4590}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "8DM15297803452316", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 06:09:29", "wp": "orders-002.com"}, {"transaction_id": "4LS42705F4223490A", "domain": "sugranie.com", "id": 4008, "orderData": {"billing": {"city": "Raleigh", "email": "<EMAIL>", "phone": "", "state": "NC", "country": "US", "postcode": "27613", "address_1": "2016 Corberrie Lane", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Raleigh", "state": "NC", "country": "US", "postcode": "27613", "address_1": "2016 Corberrie Lane", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,3;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/621118955]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0CMTEAAR1HdrrL8VSDR7ikkQCqfnAR3-CxGNAoH44IZWcChRbufBs33Z3eusMVTBg_aem_RUEbobTto8CeEFXAgkWlpA"}, {"key": "invoice_id", "value": "sugranie-nc9hz-94210"}, {"key": "_ppcp_paypal_order_id", "value": "4LS42705F4223490A"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4814, "variation_id": 4986}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "4LS42705F4223490A", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 06:11:03", "wp": "orders-002.com"}, {"transaction_id": "4A32712945325435G", "domain": "sugranie.com", "id": 4017, "orderData": {"billing": {"city": "Yorktown", "email": "<EMAIL>", "phone": "", "state": "VA", "country": "US", "postcode": "23692", "address_1": "117 Ellis Drive", "address_2": "", "last_name": "Hartzheim", "first_name": "<PERSON><PERSON>"}, "set_paid": false, "shipping": {"city": "Yorktown", "state": "VA", "country": "US", "postcode": "23692", "address_1": "117 Ellis Drive", "address_2": "", "last_name": "Hartzheim", "first_name": "<PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 14; SM-S906U Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.130 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/470.*********;]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHQvH-_-kjHwcp_jWKU3WeUv4nWIxqWWm-o8WYxVQblM9UVqhdXScmmOe7A_aem_2ZL-QxIxPK7hJf57peJO9w&utm_source=facebook&utm_medium=paid&campaign_id=120210963391790521&ad_id=120210963391830521&utm_id=120210963391780521&utm_content=120210963391830521&utm_term=120210963391790521&utm_campaign=120210963391780521"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120210963391780521DHV120210963391830521DHV120210963391790521"}, {"key": "invoice_id", "value": "sugranie-xh1bv-52390"}, {"key": "_ppcp_paypal_order_id", "value": "4A32712945325435G"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4814, "variation_id": 4960}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "4A32712945325435G", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 06:16:46", "wp": "orders-002.com"}, {"transaction_id": "5AJ48428B30496242", "domain": "imagineemporiumus.com", "id": 4022, "orderData": {"billing": {"city": "Delhi", "email": "<EMAIL>", "phone": "", "state": "IA", "country": "US", "postcode": "52223", "address_1": "20762 262nd Street", "address_2": "", "last_name": "A Vance", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Delhi", "state": "IA", "country": "US", "postcode": "52223", "address_1": "20762 262nd Street", "address_2": "", "last_name": "A Vance", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone15,3;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/621005030;IABMV/1]"}, {"key": "invoice_id", "value": "imagineemporiumus-5da29-88083"}, {"key": "_ppcp_paypal_order_id", "value": "5AJ48428B30496242"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4637, "variation_id": 4658}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "5AJ48428B30496242", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 06:18:42", "wp": "orders-dth.com"}, {"transaction_id": "1N348482N68447326", "domain": "stylestackus.com", "id": 4027, "orderData": {"billing": {"city": "Vero Beach", "email": "<EMAIL>", "phone": "", "state": "FL", "country": "US", "postcode": "32968", "address_1": "6325 21st St SW", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Vero Beach", "state": "FL", "country": "US", "postcode": "32968", "address_1": "6325 21st St SW", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "***************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,5;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/621059063]"}, {"key": "QUERY", "value": "stylestackus.com/product/brasupergather04t?fbclid=IwZXh0bgNhZW0BMAABHRBIj2GiTADo5zOIqYO_Q07heuV6YJZMk-JbFNdJd2eu8wIHfch_hee_gg_aem_yYbSK8gAu7YVsLIFiEBFgw&utm_source=facebook&utm_medium=paid&campaign_id=120211753403180332&ad_id=120211754052530332"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "stylestackus-n6ilj-13868"}, {"key": "_ppcp_paypal_order_id", "value": "1N348482N68447326"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 5040, "variation_id": 5098}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "1N348482N68447326", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-11 06:19:53", "wp": "orders-002.com"}, {"transaction_id": "1EV716682H7004711", "domain": "sugranie.com", "id": 4028, "orderData": {"billing": {"city": "Irvington-on-Hudson", "email": "<EMAIL>", "phone": "", "state": "NY", "country": "US", "postcode": "10533", "address_1": "Coalition For Family Justice, 501c3", "address_2": "", "last_name": "getz", "first_name": "monica"}, "set_paid": false, "shipping": {"city": "Irvington-on-Hudson", "state": "NY", "country": "US", "postcode": "10533", "address_1": "Coalition For Family Justice, 501c3", "address_2": "", "last_name": "getz", "first_name": "monica"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,3;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620373543]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0CMTEAAR1Xpp9jaSfL_0xj0UKb0slLJLm5l-b1wJcgtmVztg4VNbqnIcuEUi6P8p4_aem_0i3kvSlIR9yP0eagjv2mqA"}, {"key": "invoice_id", "value": "sugranie-inf06-96565"}, {"key": "_ppcp_paypal_order_id", "value": "1EV716682H7004711"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4814, "variation_id": 4959}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "1EV716682H7004711", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 06:19:54", "wp": "orders-002.com"}, {"transaction_id": "7K3889015H459880T", "domain": "sugranie.com", "id": 4034, "orderData": {"billing": {"city": "North Platte", "email": "<EMAIL>", "phone": "", "state": "NE", "country": "US", "postcode": "69101", "address_1": "1202 W 19th St", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "North Platte", "state": "NE", "country": "US", "postcode": "69101", "address_1": "1202 W 19th St", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 14; Pixel 8 Build/AP2A.240705.005; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.170 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/472.*********;]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHdQqYZIdMukjgr8yUG6zX4krGi0mn5pSvkm6sCrzyZiZ9HnJegI_REjqFA_aem_L0l4KLxvk7C8n-7Gi7X9_w&utm_source=facebook&utm_medium=paid&campaign_id=120210684920310287&ad_id=120210684920320287"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "sugranie-8gu4b-72287"}, {"key": "_ppcp_paypal_order_id", "value": "7K3889015H459880T"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4814, "variation_id": 5020}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "7K3889015H459880T", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-11 06:20:24", "wp": "orders-002.com"}, {"transaction_id": "1TT41326BM246042T", "domain": "oarlogla.com", "id": 4035, "orderData": {"billing": {"city": "Spokane Valley", "email": "<EMAIL>", "phone": "", "state": "WA", "country": "US", "postcode": "99216-0141", "address_1": "13808 E 32nd Ave Apt AA207", "address_2": "", "last_name": "fred<PERSON><PERSON>", "first_name": "christel"}, "set_paid": false, "shipping": {"city": "Spokane Valley", "state": "WA", "country": "US", "postcode": "99216-0141", "address_1": "13808 E 32nd Ave Apt AA207", "address_2": "", "last_name": "fred<PERSON><PERSON>", "first_name": "christel"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone13,4;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/621135866]"}, {"key": "QUERY", "value": "oarlogla.com/product/nipples56-2?fbclid=IwZXh0bgNhZW0BMAABHXJ883qkY6R_fE0fhWfMiYlCcpbA0Kjo3H7L4pLU-b4uYX26TPfPiomQPw_aem_lLd5bZhr8FjgKxLf2xbSpg&utm_source=facebook&utm_medium=paid&campaign_id=120209276919170773&ad_id=120209276919210773"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "oarlogla-9vpmr-53805"}, {"key": "_ppcp_paypal_order_id", "value": "1TT41326BM246042T"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 5720, "variation_id": 5734}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "4.95", "method_id": "flat_rate"}], "transaction_id": "1TT41326BM246042T", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 06:20:25", "wp": "orders-002.com"}, {"transaction_id": "0HH167062D100522E", "domain": "sugranie.com", "id": 4036, "orderData": {"billing": {"city": "Hartford ", "email": "<EMAIL>", "phone": "", "state": "CT", "country": "US", "postcode": "06112", "address_1": "12 Deerfield Ave. ", "address_2": "", "last_name": "Best", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Hartford ", "state": "CT", "country": "US", "postcode": "06112", "address_1": "12 Deerfield Ave. ", "address_2": "", "last_name": "Best", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 12; moto g stylus (2022) Build/S3RDES32.123-37-5-11; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.133 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/453.**********;]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHefcEc398iHLXZhwzJQskK0R76i5_PZ8-n22wymEm14UxvKJ7ne1ZYI7sg_aem_kkTeOyVk1-mbKIZt3bH1xQ&utm_source=facebook&utm_medium=paid&campaign_id=120210524816280783&ad_id=120210524816330783"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "sugranie-8rylj-22793"}, {"key": "_ppcp_paypal_order_id", "value": "0HH167062D100522E"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4814, "variation_id": 4909}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "0HH167062D100522E", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-11 06:20:55", "wp": "orders-002.com"}, {"transaction_id": "1KC39625SV1668337", "domain": "oarlogla.com", "id": 4038, "orderData": {"billing": {"city": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "", "state": "FL", "country": "US", "postcode": "33549", "address_1": "18850 Tracer Dr.", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "<PERSON><PERSON>", "state": "FL", "country": "US", "postcode": "33549", "address_1": "18850 Tracer Dr.", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 14; CPH2515 Build/UKQ1.230924.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.170 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/472.*********;]"}, {"key": "QUERY", "value": "oarlogla.com/product/nipples56-2?fbclid=IwZXh0bgNhZW0BMAABHWFwdaeLRiMC1Nlq-bB4Fk5SV5TXKlI3HM4TFLgL5cNfhJfvuXbUpeHK_A_aem_vHIxLIGl7n1l8DCsWEyESQ&utm_source=facebook&utm_medium=paid&campaign_id=120217048645870550&ad_id=120217048646010550&utm_id=120217048645820550&utm_content=120217048646010550&utm_term=120217048645870550&utm_campaign=120217048645820550"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120217048645820550DHV120217048646010550DHV120217048645870550"}, {"key": "invoice_id", "value": "oarlogla-0gwqj-25613"}, {"key": "_ppcp_paypal_order_id", "value": "1KC39625SV1668337"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 5720, "variation_id": 5730}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "4.95", "method_id": "flat_rate"}], "transaction_id": "1KC39625SV1668337", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 06:21:03", "wp": "orders-002.com"}, {"transaction_id": "7DB99518UJ162683Y", "domain": "sugranie.com", "id": 4039, "orderData": {"billing": {"city": "Mesa", "email": "<EMAIL>", "phone": "", "state": "AZ", "country": "US", "postcode": "85212-1729", "address_1": "3129 S Mesita", "address_2": "", "last_name": "Stoltenberg", "first_name": "<PERSON><PERSON>"}, "set_paid": false, "shipping": {"city": "Mesa", "state": "AZ", "country": "US", "postcode": "85212-1729", "address_1": "3129 S Mesita", "address_2": "", "last_name": "Stoltenberg", "first_name": "<PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 14; SM-S921U Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.162 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/471.*********;]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHTs7hd0NN-XceF5gnBrXioqWhChXvjSwH1GnQr-UAdyBUZ6X_YKzNReu4g_aem_6Wz_cs-dZfDFfp4dXCVnlg&utm_source=facebook&utm_medium=paid&campaign_id=120212866511100325&ad_id=120212866511200325"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "sugranie-8kg0y-30074"}, {"key": "_ppcp_paypal_order_id", "value": "7DB99518UJ162683Y"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4814, "variation_id": 4841}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "7DB99518UJ162683Y", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 06:21:03", "wp": "orders-002.com"}, {"transaction_id": "98T26311Y4343632P", "domain": "oarlogla.com", "id": 4043, "orderData": {"billing": {"city": "Spokane Valley", "email": "<EMAIL>", "phone": "", "state": "WA", "country": "US", "postcode": "99216-0141", "address_1": "13808 E 32nd Ave Apt AA207", "address_2": "", "last_name": "fred<PERSON><PERSON>", "first_name": "christel"}, "set_paid": false, "shipping": {"city": "Spokane Valley", "state": "WA", "country": "US", "postcode": "99216-0141", "address_1": "13808 E 32nd Ave Apt AA207", "address_2": "", "last_name": "fred<PERSON><PERSON>", "first_name": "christel"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone13,4;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/621135866]"}, {"key": "QUERY", "value": "oarlogla.com/product/nipples56-2?fbclid=IwZXh0bgNhZW0BMAABHXJ883qkY6R_fE0fhWfMiYlCcpbA0Kjo3H7L4pLU-b4uYX26TPfPiomQPw_aem_lLd5bZhr8FjgKxLf2xbSpg&utm_source=facebook&utm_medium=paid&campaign_id=120209276919170773&ad_id=120209276919210773"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "oarlogla-jb8i5-80301"}, {"key": "_ppcp_paypal_order_id", "value": "98T26311Y4343632P"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 5720, "variation_id": 5734}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "4.95", "method_id": "flat_rate"}], "transaction_id": "98T26311Y4343632P", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 06:21:30", "wp": "orders-002.com"}, {"transaction_id": "3GA25884BU476421J", "domain": "sugranie.com", "id": 4046, "orderData": {"billing": {"city": "Middletown", "email": "<EMAIL>", "phone": "", "state": "DE", "country": "US", "postcode": "19709", "address_1": "5709 E Central Park Drive", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Middletown", "state": "DE", "country": "US", "postcode": "19709", "address_1": "5709 E Central Park Drive", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,7;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/621059063;IABMV/1]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHWfYflCsUgY8RLysPcrvnZRltzlam1O2lRIRJ6OoaI9zZK2fynGuEh8t4A_aem_yuffoaKuIuhlFPcyuE-wsg&utm_source=facebook&utm_medium=paid&campaign_id=120211624717180209&ad_id=120211624717220209&utm_id=120211624717090209&utm_content=120211624717220209&utm_term=120211624717180209&utm_campaign=120211624717090209"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120211624717090209DHV120211624717220209DHV120211624717180209"}, {"key": "invoice_id", "value": "sugranie-1obif-54937"}, {"key": "_ppcp_paypal_order_id", "value": "3GA25884BU476421J"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4814, "variation_id": 4938}, {"quantity": 1, "product_id": 4814, "variation_id": 4910}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "3GA25884BU476421J", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-11 06:22:14", "wp": "orders-002.com"}, {"transaction_id": "9YD19198EJ575640K", "domain": "sugranie.com", "id": 4054, "orderData": {"billing": {"city": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "", "state": "FL", "country": "US", "postcode": "33469", "address_1": "366 W Riverside Dr", "address_2": "", "last_name": "Frez<PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "<PERSON><PERSON><PERSON>", "state": "FL", "country": "US", "postcode": "33469", "address_1": "366 W Riverside Dr", "address_2": "", "last_name": "Frez<PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone16,2;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620777145]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0CMTEAAR0WlUSGKgTfFPwb1gVkl8w0VTqWB96iYfsM5n69sOfLuBmcHAJhmEaCPe4_aem_7OVF54Mo-mijvngfDhF7AQ"}, {"key": "invoice_id", "value": "sugranie-mqd28-35587"}, {"key": "_ppcp_paypal_order_id", "value": "9YD19198EJ575640K"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4814, "variation_id": 4881}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "9YD19198EJ575640K", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 06:23:47", "wp": "orders-002.com"}, {"transaction_id": "0GX51979T79228820", "domain": "sugranie.com", "id": 4061, "orderData": {"billing": {"city": "Groveland", "email": "m<PERSON><EMAIL>", "phone": "", "state": "FL", "country": "US", "postcode": "34736", "address_1": "16345 Tuscanooga Rd", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Groveland", "state": "FL", "country": "US", "postcode": "34736", "address_1": "16345 Tuscanooga Rd", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone13,2;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/621307430;IABMV/1]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHdoV01s8Oy5P_qD7Cl6a19lPS0f99DNlBQhjqRAYsncSCWvBvQqiEFifbA_aem_3aWgeJdzt_StNe2fXR6gtg&utm_source=facebook&utm_medium=paid&campaign_id=120211293719980346&ad_id=120211293720010346&utm_id=120211293719970346&utm_content=120211293720010346&utm_term=120211293719980346&utm_campaign=120211293719970346"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120211293719970346DHV120211293720010346DHV120211293719980346"}, {"key": "invoice_id", "value": "sugranie-v38pl-41761"}, {"key": "_ppcp_paypal_order_id", "value": "0GX51979T79228820"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4814, "variation_id": 4964}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "0GX51979T79228820", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 06:25:04", "wp": "orders-002.com"}, {"transaction_id": "6VR05285KN3058829", "domain": "imagineemporiumus.com", "id": 4195, "orderData": {"billing": {"city": "Fresno", "email": "<EMAIL>", "phone": "", "state": "CA", "country": "US", "postcode": "93727", "address_1": "5561 E. Atchison St.", "address_2": "", "last_name": "Keochai", "first_name": "<PERSON><PERSON><PERSON>"}, "set_paid": false, "shipping": {"city": "Fresno", "state": "CA", "country": "US", "postcode": "93727", "address_1": "5561 E. Atchison St.", "address_2": "", "last_name": "Keochai", "first_name": "<PERSON><PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "***********"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone16,2;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_Qaau_US;FBOP/5;FBRV/621005030]"}, {"key": "QUERY", "value": "imagineemporiumus.com/product/nipples56?fbclid=IwZXh0bgNhZW0BMAABHca0rEouNhTZyR220g9EK9rSsgKUsPD1pOugxgWAZrbq-i64tPF3Ak9jdA_aem_qpjqcgpoOl4rX0nLRZXY8Q&utm_source=facebook&utm_medium=paid&campaign_id=120211307545050365&ad_id=120211307545040365"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "imagineemporiumus-6ct7v-64109"}, {"key": "_ppcp_paypal_order_id", "value": "6VR05285KN3058829"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4637, "variation_id": 4683}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "6VR05285KN3058829", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 07:37:26", "wp": "orders-dth.com"}, {"transaction_id": "0AD81196Y3997213J", "domain": "imagineemporiumus.com", "id": 4201, "orderData": {"billing": {"city": "<PERSON>", "email": "<EMAIL>", "phone": "", "state": "TX", "country": "US", "postcode": "77365", "address_1": "22011 Rae Lakes Lane", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "<PERSON>", "state": "TX", "country": "US", "postcode": "77365", "address_1": "22011 Rae Lakes Lane", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone15,5;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/621307430]"}, {"key": "QUERY", "value": "imagineemporiumus.com/product/nipples56?fbclid=IwZXh0bgNhZW0BMAABHXfrDnoa2JiW1goPiK70POx2S6FEDvJI90Gi4DSOWuho3HynVGYBi9PnbA_aem_JW-iy9w5ecyt3ruNpAnpfg&utm_source=facebook&utm_medium=paid&campaign_id=120211307545050365&ad_id=120211307545040365"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "imagineemporiumus-e7zf5-97924"}, {"key": "_ppcp_paypal_order_id", "value": "0AD81196Y3997213J"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4637, "variation_id": 4663}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "0AD81196Y3997213J", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 07:44:55", "wp": "orders-dth.com"}, {"transaction_id": "7F395660EX4231103", "domain": "imagineemporiumus.com", "id": 4266, "orderData": {"billing": {"city": "<PERSON>", "email": "<EMAIL>", "phone": "", "state": "MO", "country": "US", "postcode": "63376", "address_1": "75 Madrid Court", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "<PERSON>", "state": "MO", "country": "US", "postcode": "63376", "address_1": "75 Madrid Court", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone15,3;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/621135866]"}, {"key": "QUERY", "value": "imagineemporiumus.com/product/nipples56?fbclid=IwZXh0bgNhZW0BMAABHa0MekdzuBnHNZE4iAsjfuJtPJkdKDvoasJjd-hxZJ1eESr_pa36iFnNzA_aem_5wAV7t0MKPH8t_Gz1hnUMw&utm_source=facebook&utm_medium=paid&campaign_id=120211307545050365&ad_id=120211307545040365"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "imagineemporiumus-e5a3n-64785"}, {"key": "_ppcp_paypal_order_id", "value": "7F395660EX4231103"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4637, "variation_id": 4683}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "7F395660EX4231103", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 10:31:06", "wp": "orders-dth.com"}, {"transaction_id": "667460228J053551F", "domain": "flawlesfashionus.com", "id": 4299, "orderData": {"billing": {"city": "Altoona", "email": "<EMAIL>", "phone": "", "state": "PA", "country": "US", "postcode": "16602-4046", "address_1": "307 Orchard Ave", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Altoona", "state": "PA", "country": "US", "postcode": "16602-4046", "address_1": "307 Orchard Ave", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,6;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/2;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620612915;IABMV/1]"}, {"key": "QUERY", "value": "flawlesfashionus.com/product/premiumsandals52?fbclid=IwZXh0bgNhZW0BMAABHbpvp-xX7jP6fxpmbum9VdymBJdMdCVsWTaenxniu7G2cu0SHip8zzdz1w_aem_pZY5V0Eyx4WfVT-vxf5B4g&utm_source=facebook&utm_medium=paid&campaign_id=120210357794550180&ad_id=120210357794410180"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "flawlesfashionus-oehxm-84180"}, {"key": "_ppcp_paypal_order_id", "value": "667460228J053551F"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3702}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "667460228J053551F", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 12:29:17", "wp": "orders-dth.com"}, {"transaction_id": "4TR93447XG6354730", "domain": "equaldealus.com", "id": 4324, "orderData": {"billing": {"city": "Clute", "email": "<EMAIL>", "phone": "", "state": "TX", "country": "US", "postcode": "77531", "address_1": "905 Industrial St", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Clute", "state": "TX", "country": "US", "postcode": "77531", "address_1": "905 Industrial St", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "***********"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 13; motorola razr 2023 Build/T2TVS33.45-149-4; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.134 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/467.*********;]"}, {"key": "QUERY", "value": "equaldealus.com/product/speedssd13?fbclid=IwZXh0bgNhZW0BMAABHXx9_9wDkMQWR_cAkC4Po9BF7wDKA8fTpf6fU044KeaZhk3mqsIlj10axg_aem_uttUjOZCmLMnDA6yNr8oRQ&utm_medium=paid&utm_source=fb&utm_id=120209478301860301&utm_content=120209478302050301&utm_term=120209478301980301&utm_campaign=120209478301860301"}, {"key": "FB_UTM", "value": "fbDHVpaidDHV120209478301860301DHV120209478302050301DHV120209478301980301"}, {"key": "invoice_id", "value": "equaldealus-zs1s4-31091"}, {"key": "_ppcp_paypal_order_id", "value": "4TR93447XG6354730"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3891, "variation_id": 3892}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "4TR93447XG6354730", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-11 13:11:54", "wp": "orders-dth.com"}, {"transaction_id": "89834364RN4533309", "domain": "flawlesfashionus.com", "id": 4349, "orderData": {"billing": {"city": "Detroit", "email": "marlene_rich<PERSON><EMAIL>", "phone": "", "state": "MI", "country": "US", "postcode": "48228", "address_1": "10046 Warwick Street", "address_2": "", "last_name": "<PERSON>", "first_name": "Marlene"}, "set_paid": false, "shipping": {"city": "Detroit", "state": "MI", "country": "US", "postcode": "48228", "address_1": "10046 Warwick Street", "address_2": "", "last_name": "<PERSON>", "first_name": "Marlene"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_7_8 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/20H343 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone10,5;FBMD/iPhone;FBSN/iOS;FBSV/16.7.8;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/621118955]"}, {"key": "QUERY", "value": "flawlesfashionus.com/product/premiumsandals52?fbclid=IwZXh0bgNhZW0BMAABHYpe9ooSiyLbBMLGP0y2010DsgQzx5tRW6PQPtw8fMjaQfzIPbuD9CdZdA_aem_cILQcTpVYCuTl0W6ZISrXg&utm_source=facebook&utm_medium=paid&campaign_id=120211794314380332&ad_id=120211794314980332"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "flawlesfashionus-9syii-15936"}, {"key": "_ppcp_paypal_order_id", "value": "89834364RN4533309"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3723}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "89834364RN4533309", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 13:51:04", "wp": "orders-dth.com"}, {"transaction_id": "4MW04938AL987283C", "domain": "flawlesfashionus.com", "id": 4392, "orderData": {"billing": {"city": "Wentzville mo", "email": "<EMAIL>", "phone": "", "state": "MO", "country": "US", "postcode": "63385", "address_1": "4 hill place", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Wentzville mo", "state": "MO", "country": "US", "postcode": "63385", "address_1": "4 hill place", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 14; SM-S911U Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.170 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/472.*********;]"}, {"key": "QUERY", "value": "flawlesfashionus.com/product/premiumsandals52?fbclid=IwZXh0bgNhZW0BMAABHZqogld80dkt8Izm1h4puWD5MyvyGKoWD5jAfe_NLRf1pl1A6GXwPO4hgA_aem_jdSZ4Op9R5WHbn7KcXA03g&utm_source=facebook&utm_medium=paid&campaign_id=120209823938590070&ad_id=120209823938090070"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "flawlesfashionus-yjzid-72921"}, {"key": "_ppcp_paypal_order_id", "value": "4MW04938AL987283C"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3718}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "4MW04938AL987283C", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-11 15:09:30", "wp": "orders-dth.com"}, {"transaction_id": "5PL67107YY277680M", "domain": "imagineemporiumus.com", "id": 4414, "orderData": {"billing": {"city": "Chicago", "email": "<EMAIL>", "phone": "", "state": "IL", "country": "US", "postcode": "60634", "address_1": "3853 N. Ottawa", "address_2": "", "last_name": "belluomini", "first_name": "joyce"}, "set_paid": false, "shipping": {"city": "Chicago", "state": "IL", "country": "US", "postcode": "60634", "address_1": "3853 N. Ottawa", "address_2": "", "last_name": "belluomini", "first_name": "joyce"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_8_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/18H107 [FBAN/FBIOS;FBDV/iPhone12,8;FBMD/iPhone;FBSN/iOS;FBSV/14.8.1;FBSS/2;FBID/phone;FBLC/en_US;FBOP/5]"}, {"key": "QUERY", "value": "imagineemporiumus.com/product/nipples56?fbclid=IwZXh0bgNhZW0BMAABHUp4pq3InLMdC2eniE380b2RZX2WGDnNG497D2kD45iyZJwQv5Y9SHJIMQ_aem_hOVRExh-KPWemjIVdCLSRQ&utm_source=facebook&utm_medium=paid&campaign_id=120211307545050365&ad_id=120211307545040365"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "imagineemporiumus-23d66-61567"}, {"key": "_ppcp_paypal_order_id", "value": "5PL67107YY277680M"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4637, "variation_id": 4691}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "5PL67107YY277680M", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-11 15:51:00", "wp": "orders-dth.com"}, {"transaction_id": "22H02388165271437", "domain": "flawlesfashionus.com", "id": 4440, "orderData": {"billing": {"city": "Los Angeles ", "email": "<EMAIL>", "phone": "", "state": "CA", "country": "US", "postcode": "90037", "address_1": "920 W 53rd Street", "address_2": "", "last_name": "Young<PERSON><PERSON><PERSON>", "first_name": "Mona"}, "set_paid": false, "shipping": {"city": "Los Angeles ", "state": "CA", "country": "US", "postcode": "90037", "address_1": "920 W 53rd Street", "address_2": "", "last_name": "Young<PERSON><PERSON><PERSON>", "first_name": "Mona"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 13; SM-G981U Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.134 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/471.*********;]"}, {"key": "QUERY", "value": "flawlesfashionus.com/product/premiumsandals52?fbclid=IwZXh0bgNhZW0BMAABHT8kKvcr370HkKh4wKrVt1jFtvzH8obusbBHb3U3BOECcQYNqznacv8d8w_aem_ZzfKhOxbWLkmwgK0f4HJ3w&utm_source=facebook&utm_medium=paid&campaign_id=120211379542270319&ad_id=120211379541620319"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "flawlesfashionus-l19pf-49844"}, {"key": "_ppcp_paypal_order_id", "value": "22H02388165271437"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3721}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "22H02388165271437", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-11 16:35:22", "wp": "orders-dth.com"}, {"transaction_id": "3GG53228EF984854J", "domain": "flawlesfashionus.com", "id": 4709, "orderData": {"billing": {"city": "KIRKLAND", "email": "<EMAIL>", "phone": "", "state": "WA", "country": "US", "postcode": "98034", "address_1": "13060 109TH AVE NE", "address_2": "", "last_name": "Ultican", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "KIRKLAND", "state": "WA", "country": "US", "postcode": "98034", "address_1": "13060 109TH AVE NE", "address_2": "", "last_name": "Ultican", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone12,3;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/0]"}, {"key": "invoice_id", "value": "flawlesfashionus-lsec6-93155"}, {"key": "_ppcp_paypal_order_id", "value": "3GG53228EF984854J"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3677}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "3GG53228EF984854J", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-12 11:52:58", "wp": "orders-dth.com"}, {"transaction_id": "8D046234XK8004437", "domain": "flawlesfashionus.com", "id": 4772, "orderData": {"billing": {"city": "Westlake Village", "email": "<EMAIL>", "phone": "", "state": "CA", "country": "US", "postcode": "91361", "address_1": "1145 Brookview Ave", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON>"}, "set_paid": false, "shipping": {"city": "Westlake Village", "state": "CA", "country": "US", "postcode": "91361", "address_1": "1145 Brookview Ave", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/472.**********;FBBV/621085421;FBDV/iPhone16,2;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/0]"}, {"key": "QUERY", "value": "flawlesfashionus.com/product/premiumsandals52?fbclid=IwZXh0bgNhZW0BMAABHVQIfzH2-h9ruCIzpvkijaIojhB6NtkG2x7LdoPg7yDl4R1iqNl98u20BA_aem_AzsH4DpCYK-KuNzDSnkxDA&utm_source=facebook&utm_medium=paid&campaign_id=120209502296690539&ad_id=120209502297400539"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "flawlesfashionus-ntnof-39811"}, {"key": "_ppcp_paypal_order_id", "value": "8D046234XK8004437"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3673}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "8D046234XK8004437", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-12 13:25:35", "wp": "orders-dth.com"}, {"transaction_id": "4RM300569T885630W", "domain": "flawlesfashionus.com", "id": 4816, "orderData": {"billing": {"city": "bridgeport", "email": "<EMAIL>", "phone": "", "state": "WV", "country": "US", "postcode": "26330", "address_1": "106 rosewood ct", "address_2": "", "last_name": "abraham", "first_name": "tamara"}, "set_paid": false, "shipping": {"city": "bridgeport", "state": "WV", "country": "US", "postcode": "26330", "address_1": "106 rosewood ct", "address_2": "", "last_name": "abraham", "first_name": "tamara"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone16,1;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/621005030]"}, {"key": "invoice_id", "value": "flawlesfashionus-tc5s6-53875"}, {"key": "_ppcp_paypal_order_id", "value": "4RM300569T885630W"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3701}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "4RM300569T885630W", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-12 15:23:31", "wp": "orders-dth.com"}, {"transaction_id": "7UU75302FU235463K", "domain": "flawlesfashionus.com", "id": 4830, "orderData": {"billing": {"city": "Boca Rston ", "email": "<EMAIL>", "phone": "", "state": "FL", "country": "US", "postcode": "33433", "address_1": "20860 Via Madeira drive ", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Boca Rston ", "state": "FL", "country": "US", "postcode": "33433", "address_1": "20860 Via Madeira drive ", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone15,4;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/620626117]"}, {"key": "invoice_id", "value": "flawlesfashionus-o3azh-30483"}, {"key": "_ppcp_paypal_order_id", "value": "7UU75302FU235463K"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3674}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "7UU75302FU235463K", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-12 15:50:01", "wp": "orders-dth.com"}, {"transaction_id": "7PG83826HW427090E", "domain": "sugranie.com", "id": 4936, "orderData": {"billing": {"city": "<PERSON>", "email": "<EMAIL>", "phone": "", "state": "LA", "country": "US", "postcode": "71201", "address_1": "3005 Hunt Place", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "<PERSON>", "state": "LA", "country": "US", "postcode": "71201", "address_1": "3005 Hunt Place", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone12,5;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/621632390]"}, {"key": "invoice_id", "value": "sugranie-m64fi-81836"}, {"key": "_ppcp_paypal_order_id", "value": "7PG83826HW427090E"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4814, "variation_id": 4909}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "7PG83826HW427090E", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-13 05:49:58", "wp": "orders-002.com"}, {"transaction_id": "6YJ08770TF2852638", "domain": "sugranie.com", "id": 4937, "orderData": {"billing": {"city": "Newport", "email": "<EMAIL>", "phone": "", "state": "RI", "country": "US", "postcode": "02840", "address_1": "142 Mill Street", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Newport", "state": "RI", "country": "US", "postcode": "02840", "address_1": "142 Mill Street", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (iPad; CPU OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/472.**********;FBBV/621085421;FBDV/iPad11,7;FBMD/iPad;FBSN/iPadOS;FBSV/17.5.1;FBSS/2;FBID/tablet;FBLC/en_US;FBOP/5;FBRV/0]"}, {"key": "invoice_id", "value": "sugranie-4lhkd-69183"}, {"key": "_ppcp_paypal_order_id", "value": "6YJ08770TF2852638"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4814, "variation_id": 4965}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "6YJ08770TF2852638", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-13 05:50:17", "wp": "orders-002.com"}, {"transaction_id": "8VU1555528883123F", "domain": "sugranie.com", "id": 4938, "orderData": {"billing": {"city": "Arcadia", "email": "<EMAIL>", "phone": "", "state": "FL", "country": "US", "postcode": "34266-8368", "address_1": "2626 NE Highway 70 Lot 251", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON><PERSON>"}, "set_paid": false, "shipping": {"city": "Arcadia", "state": "FL", "country": "US", "postcode": "34266-8368", "address_1": "2626 NE Highway 70 Lot 251", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON><PERSON><PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone12,1;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/2;FBID/phone;FBLC/en_US;FBOP/5;FBRV/621535582]"}, {"key": "QUERY", "value": "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHReKJYR0w0UvKcebOYpKpyfuYPgf06f4lIox5Bfo3k6yGFqIsDHcYmBsWA_aem_7rqKos9NxvIFoZveO9a7QQ&utm_source=facebook&utm_medium=paid&campaign_id=120210172082020477&ad_id=120210172082040477"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "sugranie-3lo5s-30553"}, {"key": "_ppcp_paypal_order_id", "value": "8VU1555528883123F"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 4814, "variation_id": 4827}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "transaction_id": "8VU1555528883123F", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-13 05:52:13", "wp": "orders-002.com"}, {"transaction_id": "6JC811467A577913J", "domain": "flawlesfashionus.com", "id": 5043, "orderData": {"total": "43.98", "billing": {"city": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "", "state": "MO", "country": "US", "postcode": "63011", "address_1": "820 Westwood Dr", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "<PERSON><PERSON>", "state": "MO", "country": "US", "postcode": "63011", "address_1": "820 Westwood Dr", "address_2": "", "last_name": "<PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/472.**********;FBBV/621085421;FBDV/iPhone12,1;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/2;FBID/phone;FBLC/en_US;FBOP/5;FBRV/621752484]"}, {"key": "QUERY", "value": "flawlesfashionus.com/product/premiumsandals52?fbclid=IwZXh0bgNhZW0BMQABHcuUM9FujjZk71TG07QQ0I21iX-DFZO_36e2Nw610xzLZ4XxUiudjtyBiA_aem_9XfTtK75fpynZiW8RaFlKg"}, {"key": "invoice_id", "value": "flawlesfashionus-oqw7o-13738"}, {"key": "_ppcp_paypal_order_id", "value": "6JC811467A577913J"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3705}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "shipping_total": "6.99", "transaction_id": "6JC811467A577913J", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-13 12:15:56", "wp": "orders-dth.com"}, {"transaction_id": "7A923027T31908334", "domain": "flawlesfashionus.com", "id": 5069, "orderData": {"total": "43.98", "billing": {"city": "<PERSON> ", "email": "<EMAIL>", "phone": "", "state": "FL", "country": "US", "postcode": "32958", "address_1": "1354 <PERSON><PERSON><PERSON> ", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "<PERSON> ", "state": "FL", "country": "US", "postcode": "32958", "address_1": "1354 <PERSON><PERSON><PERSON> ", "address_2": "", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/472.**********;FBBV/621085421;FBDV/iPhone14,7;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/0]"}, {"key": "QUERY", "value": "flawlesfashionus.com/product/premiumsandals52?fbclid=IwZXh0bgNhZW0BMAABHXf1Q1Eir0jLRIrAiS6tx1C4mlfFWy_Pmrjq3Z7_79y4qYb7mLs4qru20g_aem_gPCxQR-G8ICT10fv0ikkTA&utm_source=facebook&utm_medium=paid&campaign_id=120212360222340613&ad_id=120212360222990613"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHVnoneDHVnoneDHVnone"}, {"key": "invoice_id", "value": "flawlesfashionus-q0k5a-35764"}, {"key": "_ppcp_paypal_order_id", "value": "7A923027T31908334"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3719}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "shipping_total": "6.99", "transaction_id": "7A923027T31908334", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-13 12:55:29", "wp": "orders-dth.com"}, {"transaction_id": "*****************", "domain": "flawlesfashionus.com", "id": 5093, "orderData": {"total": "43.98", "billing": {"city": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "", "state": "OK", "country": "US", "postcode": "73080", "address_1": "23289 Hwy 74", "address_2": "", "last_name": "Woolwine", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "<PERSON><PERSON><PERSON>", "state": "OK", "country": "US", "postcode": "73080", "address_1": "23289 Hwy 74", "address_2": "", "last_name": "Woolwine", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "**************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone12,1;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/2;FBID/phone;FBLC/en_US;FBOP/5;FBRV/621632390]"}, {"key": "QUERY", "value": "flawlesfashionus.com/product/premiumsandals52?fbclid=IwZXh0bgNhZW0BMAABHVB1f5LARiO8WfIU_Dbpd-V2hOBbkO_yDSBo4rsEgo_yiRRIvImW76Ewzw_aem_lfeKI66KzLDrAYHPyXt3VA&utm_source=facebook&utm_medium=paid&campaign_id=120209519143740539&ad_id=120209519143950539&utm_id=120209519144350539&utm_content=120209519143950539&utm_term=120209519143740539&utm_campaign=120209519144350539"}, {"key": "FB_UTM", "value": "facebookDHVpaidDHV120209519144350539DHV120209519143950539DHV120209519143740539"}, {"key": "invoice_id", "value": "flawlesfashionus-c7yyr-11377"}, {"key": "_ppcp_paypal_order_id", "value": "*****************"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3714}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "shipping_total": "6.99", "transaction_id": "*****************", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-13 13:37:27", "wp": "orders-dth.com"}, {"transaction_id": "59U94209YF5071335", "domain": "flawlesfashionus.com", "id": 5143, "orderData": {"total": "43.98", "billing": {"city": "Pearland", "email": "<EMAIL>", "phone": "", "state": "TX", "country": "US", "postcode": "77581", "address_1": "2703 S Galveston Ave", "address_2": "", "last_name": "Car<PERSON>", "first_name": "Neni<PERSON>"}, "set_paid": false, "shipping": {"city": "Pearland", "state": "TX", "country": "US", "postcode": "77581", "address_1": "2703 S Galveston Ave", "address_2": "", "last_name": "Car<PERSON>", "first_name": "Neni<PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPhone14,3;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/0]"}, {"key": "invoice_id", "value": "flawlesfashionus-zv5hl-40558"}, {"key": "_ppcp_paypal_order_id", "value": "59U94209YF5071335"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3706}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "shipping_total": "6.99", "transaction_id": "59U94209YF5071335", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-13 15:43:53", "wp": "orders-dth.com"}, {"transaction_id": "9LU43067HC3842841", "domain": "flawlesfashionus.com", "id": 5280, "orderData": {"total": "43.98", "billing": {"city": "Marion Station", "email": "<EMAIL>", "phone": "", "state": "MD", "country": "US", "postcode": "21838", "address_1": "29920 Lovers Ln", "address_2": "", "last_name": "Wylie", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Marion Station", "state": "MD", "country": "US", "postcode": "21838", "address_1": "29920 Lovers Ln", "address_2": "", "last_name": "Wylie", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (Linux; Android 14; SM-A546U Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/126.0.6478.170 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/472.*********;]"}, {"key": "QUERY", "value": "flawlesfashionus.com/product/premiumsandals52?fbclid=IwZXh0bgNhZW0BMAABHZjAwPiCFV0vCm-dWhFrc7i7H_RUvJROgxtaIAkPKvLQdA2frX7AT238XQ_aem_geWxjij5zpbd7LWGcTKFNQ"}, {"key": "invoice_id", "value": "flawlesfashionus-o9loc-20813"}, {"key": "_ppcp_paypal_order_id", "value": "9LU43067HC3842841"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3716}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "shipping_total": "6.99", "transaction_id": "9LU43067HC3842841", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-14 10:03:58", "wp": "orders-dth.com"}, {"transaction_id": "5KK843222H841561D", "domain": "flawlesfashionus.com", "id": 5319, "orderData": {"total": "43.98", "billing": {"city": "Collinsville", "email": "<EMAIL>", "phone": "", "state": "AL", "country": "US", "postcode": "35961", "address_1": "5584 CR-22", "address_2": "", "last_name": "Trail", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Collinsville", "state": "AL", "country": "US", "postcode": "35961", "address_1": "5584 CR-22", "address_2": "", "last_name": "Trail", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "***********"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/472.**********;FBBV/621085421;FBDV/iPhone14,7;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/0]"}, {"key": "invoice_id", "value": "flawlesfashionus-4c8fr-16661"}, {"key": "_ppcp_paypal_order_id", "value": "5KK843222H841561D"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3707}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "shipping_total": "6.99", "transaction_id": "5KK843222H841561D", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-14 11:53:03", "wp": "orders-dth.com"}, {"transaction_id": "2GX08769G6550982K", "domain": "flawlesfashionus.com", "id": 5352, "orderData": {"total": "43.98", "billing": {"city": "<PERSON>", "email": "<EMAIL>", "phone": "", "state": "WA", "country": "US", "postcode": "98354", "address_1": "P0 Box 522", "address_2": "P0 Box 522", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "<PERSON>", "state": "WA", "country": "US", "postcode": "98354", "address_1": "P0 Box 522", "address_2": "P0 Box 522", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/472.**********;FBBV/621085421;FBDV/iPhone13,2;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/622217709]"}, {"key": "QUERY", "value": "flawlesfashionus.com/product/premiumsandals52?fbclid=IwZXh0bgNhZW0BMAABHQp34H04GWJkAItHsaTLeX6YmsdYC9LeFrzx4p-l0Ns1rMq0cLJ1pgeVuQ_aem_TdG8_TEr3pgjKwdnBMbx_w"}, {"key": "invoice_id", "value": "flawlesfashionus-ucrjq-84983"}, {"key": "_ppcp_paypal_order_id", "value": "2GX08769G6550982K"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3714}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "shipping_total": "6.99", "transaction_id": "2GX08769G6550982K", "payment_method_title": "<PERSON><PERSON>", "status": "failed"}, "createdAt": "2024-07-14 12:58:07", "wp": "orders-dth.com"}, {"transaction_id": "09J12679MR453813W", "domain": "flawlesfashionus.com", "id": 5387, "orderData": {"total": "43.98", "billing": {"city": "Onyx", "email": "<EMAIL>", "phone": "", "state": "CA", "country": "US", "postcode": "93255", "address_1": "20657 Cap Canyon Rd PO Box 214", "address_2": "", "last_name": "<PERSON>", "first_name": "Constance"}, "set_paid": false, "shipping": {"city": "Onyx", "state": "CA", "country": "US", "postcode": "93255", "address_1": "20657 Cap Canyon Rd PO Box 214", "address_2": "", "last_name": "<PERSON>", "first_name": "Constance"}, "meta_data": [{"key": "ip", "value": "************"}, {"key": "UA", "value": "Mozilla/5.0 (iPad; CPU OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/471.**********;FBBV/618472162;FBDV/iPad13,16;FBMD/iPad;FBSN/iPadOS;FBSV/17.5.1;FBSS/2;FBID/tablet;FBLC/en_US;FBOP/5;FBRV/621632390]"}, {"key": "QUERY", "value": "flawlesfashionus.com/product/premiumsandals52?fbclid=IwZXh0bgNhZW0BMAABHTJc_kBVCc7bVawgrpnbpHNhLLqWb-qx46-GGhKlAdLdAkjsnnJw4i1q5w_aem_f_7pzN_WlT1hOYQY-jOdZQ"}, {"key": "invoice_id", "value": "flawlesfashionus-kao9b-58465"}, {"key": "_ppcp_paypal_order_id", "value": "09J12679MR453813W"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3699}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "shipping_total": "6.99", "transaction_id": "09J12679MR453813W", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-14 13:42:52", "wp": "orders-dth.com"}, {"transaction_id": "7AL976986S822920T", "domain": "flawlesfashionus.com", "id": 5412, "orderData": {"total": "43.98", "billing": {"city": "Grayslake", "email": "<EMAIL>", "phone": "", "state": "IL", "country": "US", "postcode": "60030", "address_1": "712 Merrill Lane", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "first_name": "Snezana"}, "set_paid": false, "shipping": {"city": "Grayslake", "state": "IL", "country": "US", "postcode": "60030", "address_1": "712 Merrill Lane", "address_2": "", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "first_name": "Snezana"}, "meta_data": [{"key": "ip", "value": "***********"}, {"key": "UA", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/472.**********;FBBV/621085421;FBDV/iPhone14,5;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/622282651]"}, {"key": "QUERY", "value": "flawlesfashionus.com/product/premiumsandals52?fbclid=IwZXh0bgNhZW0BMAABHbG7w_sIJKgJc47lnW-uNwPwMuCal9tHUjg1QwIO8rPCFsrQAaPgNNExCQ_aem_nIrd_FjLKN293XPWnAy1lA&utm_medium=paid&utm_source=fb&utm_id=120210535876180168&utm_content=120210535876340168&utm_term=120210535875910168&utm_campaign=120210535876180168"}, {"key": "FB_UTM", "value": "fbDHVpaidDHV120210535876180168DHV120210535876340168DHV120210535875910168"}, {"key": "invoice_id", "value": "flawlesfashionus-b5cx1-48875"}, {"key": "_ppcp_paypal_order_id", "value": "7AL976986S822920T"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3698}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "shipping_total": "6.99", "transaction_id": "7AL976986S822920T", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-14 14:06:04", "wp": "orders-dth.com"}, {"transaction_id": "6MS66895J38136145", "domain": "flawlesfashionus.com", "id": 5443, "orderData": {"total": "43.98", "billing": {"city": "Naperville", "email": "<EMAIL>", "phone": "", "state": "IL", "country": "US", "postcode": "60540", "address_1": "1320 <PERSON>ball ct., Naperville IL 60540-7655", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "set_paid": false, "shipping": {"city": "Naperville", "state": "IL", "country": "US", "postcode": "60540", "address_1": "1320 <PERSON>ball ct., Naperville IL 60540-7655", "address_2": "", "last_name": "<PERSON><PERSON>", "first_name": "<PERSON>"}, "meta_data": [{"key": "ip", "value": "*************"}, {"key": "UA", "value": "Mozilla/5.0 (iPad; CPU OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/472.**********;FBBV/621085421;FBDV/iPad12,1;FBMD/iPad;FBSN/iPadOS;FBSV/17.5.1;FBSS/2;FBID/tablet;FBLC/en_US;FBOP/5;FBRV/622282651]"}, {"key": "invoice_id", "value": "flawlesfashionus-cudt3-71322"}, {"key": "_ppcp_paypal_order_id", "value": "6MS66895J38136145"}, {"key": "_ppcp_paypal_intent", "value": "CAPTURE"}, {"key": "_ppcp_paypal_payment_mode", "value": "live"}], "line_items": [{"quantity": 1, "product_id": 3620, "variation_id": 3674}], "payment_method": "ppcp-gateway", "shipping_lines": [{"total": "6.99", "method_id": "flat_rate"}], "shipping_total": "6.99", "transaction_id": "6MS66895J38136145", "payment_method_title": "Credit or debit cards (PayPal)", "status": "failed"}, "createdAt": "2024-07-14 14:57:07", "wp": "orders-dth.com"}]