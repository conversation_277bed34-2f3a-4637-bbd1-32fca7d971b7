export function orderConfirmationTemplate(order) {
  const formatCurrency = (amount) => `$${parseFloat(amount).toFixed(2)}`;
  const orderData = order.order;

  return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Order Confirmation</title>
          <style>
              body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
              .container { max-width: 600px; margin: 0 auto; padding: 20px; }
              .logo { max-width: 200px; }
              .button { background-color: #000; color: #fff; padding: 10px 20px; text-decoration: none; display: inline-block; }
              .order-summary { margin-top: 20px; }
              .item { margin-bottom: 10px; border-bottom: 1px solid #eee; padding-bottom: 10px; }
          </style>
      </head>
      <body>
          <div class="container">
              <img src="https://megadealstoreus.com/wp-content/uploads/2023/10/logo.png" alt="MegaDealStore logo" class="logo">
              <h1>Thank You for Your Order!</h1>
              <p>Order number: ${orderData.transaction_id}</p>
              
              <p>Hi ${orderData.billing.first_name} ${orderData.billing.last_name},</p>
              <p>Thank you for shopping with us. We've received your order and will process it shortly.</p>
              
              <h2>Your Order Summary</h2>
              <div class="order-summary">
                  ${orderData.line_items
                    .map(
                      (item) => `
                      <div class="item">
                          <h3>${item.name}</h3>
                          <p>SKU: ${item.sku}</p>
                          <p>Quantity: ${item.quantity}</p>
                          <p>Price: ${formatCurrency(item.price)}</p>
                          ${item.meta_data.map((meta) => `<p>${meta.name}: ${meta.option}</p>`).join("")}
                      </div>
                  `,
                    )
                    .join("")}
              </div>
              
              <h2>Order Total</h2>
              <p>Subtotal: ${formatCurrency(orderData.total)}</p>
              <p>Shipping: ${formatCurrency(orderData.shipping_total)}</p>
              <p><strong>Total: ${formatCurrency(parseFloat(orderData.total) + parseFloat(orderData.shipping_total))}</strong></p>
              
              <h2>Shipping Information</h2>
              <p>${orderData.shipping.first_name} ${orderData.shipping.last_name}</p>
              <p>${orderData.shipping.address_1}</p>
              ${orderData.shipping.address_2 ? `<p>${orderData.shipping.address_2}</p>` : ""}
              <p>${orderData.shipping.city}, ${orderData.shipping.state} ${orderData.shipping.postcode}</p>
              <p>${orderData.shipping.country}</p>
              
              <h2>Payment Information</h2>
              <p>Payment Method: ${orderData.payment_method_title}</p>
              
              <p>If you have any questions about your order, please contact our customer service.</p>
              
              <p>Thank you for choosing MegaDealStore!</p>
          </div>
      </body>
      </html>
    `;
}
