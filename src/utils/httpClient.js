import axios from "axios";
import { getCache, setCache } from "../config/cache.js";

const baseURL = "";
const username = "ck_9eb7bb32ef2d39df2931d106ecb5bfbcc97dce30";
const password = "cs_bb58f4304c9d3f8ed9b109b9ed5eaf3196565b84";

// Retry configuration
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second base delay
const ERROR_CACHE_TTL = 3600; // 1 hour for 4xx errors

// Helper function to check if error should be retried
const shouldRetry = (error, retryCount) => {
  if (retryCount >= MAX_RETRIES) return false;
  
  // Retry on network errors
  if (!error.response) return true;
  
  // Retry on 5xx server errors
  if (error.response.status >= 500) return true;
  
  // Don't retry on 4xx client errors
  return false;
};

// Helper function to calculate retry delay with exponential backoff
const getRetryDelay = (retryCount) => {
  return RETRY_DELAY * Math.pow(2, retryCount);
};

// Helper function to sleep
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const fetchData = async (url, retryCount = 0) => {
  const client = axios.create({
    auth: {
      username,
      password,
    },
  });
  const parsedUrl = new URL(url);
  const domain = parsedUrl.hostname;
  const cacheKey = `${url}`;
  
  try {
    const cachedData = await getCache(cacheKey);
    if (cachedData) {
      console.log("Loaded from cache:", cacheKey);
      return cachedData;
    }
    
    const response = await client.get(url);
    const responseData = response.data;
    setCache(cacheKey, responseData, [domain.replace("admin.", ""), "fe"]);
    console.log("Loaded from API and cached:", cacheKey);
    return responseData;
  } catch (error) {
    // Check if we should retry
    if (shouldRetry(error, retryCount)) {
      const delay = getRetryDelay(retryCount);
      console.log(`Retrying request ${retryCount + 1}/${MAX_RETRIES} after ${delay}ms delay for URL: ${url}`);
      await sleep(delay);
      return fetchData(url, retryCount + 1);
    }
    
    // Handle errors after all retries exhausted
    if (error.response && error.response?.data) {
      const statusCode = error.response.status;
      
      // Only cache 4xx errors with short TTL
      if (statusCode >= 400 && statusCode < 500) {
        setCache(cacheKey, error.response?.data, [
          domain.replace("admin.", ""),
          "fe",
        ], ERROR_CACHE_TTL);
        console.log(`Cached 4xx error for ${ERROR_CACHE_TTL}s:`, cacheKey);
        return error.response?.data;
      }
      
      // Don't cache 5xx errors, throw them
      if (statusCode >= 500) {
        console.log(`5xx error not cached:`, cacheKey, statusCode);
        throw error;
      }
    }
    
    console.error("Error fetching data:", error);
    throw error;
  }
};

const fetchData2 = async (url, domain, retryCount = 0) => {
  const client = axios.create({
    headers: {
      "x-domain": domain,
    },
  });
  const cacheKey = `${domain}|${url}`;
  
  try {
    const cachedData = await getCache(cacheKey);
    if (cachedData) {
      console.log(`Loaded from cache fetchData: ${cacheKey}`);
      return cachedData;
    }
    
    const response = await client.get(url);
    const responseData = response.data;
    setCache(cacheKey, responseData, [domain, "fe"]);
    console.log(`Loaded from API and cached fetchData2: ${cacheKey}`);
    return responseData;
  } catch (error) {
    // Check if we should retry
    if (shouldRetry(error, retryCount)) {
      const delay = getRetryDelay(retryCount);
      console.log(`Retrying request ${retryCount + 1}/${MAX_RETRIES} after ${delay}ms delay for URL: ${url}, domain: ${domain}`);
      await sleep(delay);
      return fetchData2(url, domain, retryCount + 1);
    }
    
    // Handle errors after all retries exhausted
    if (error.response && error.response?.data) {
      const statusCode = error.response.status;
      
      // Only cache 4xx errors with short TTL
      if (statusCode >= 400 && statusCode < 500) {
        setCache(cacheKey, error.response?.data, [domain, "fe"], ERROR_CACHE_TTL);
        console.log(`Cached 4xx error for ${ERROR_CACHE_TTL}s:`, cacheKey);
        return error.response?.data;
      }
      
      // Don't cache 5xx errors, throw them
      if (statusCode >= 500) {
        console.log(`5xx error not cached:`, cacheKey, statusCode);
        throw error;
      }
    }
    
    console.error("Error fetching data:", error);
    throw error;
  }
};

const fetchDataWrapper = async (url, options = {}) => {
  const { headers = {} } = options;
  const domain = headers["x-domain"];

  if (domain) {
    return fetchData2(url, domain);
  }
  return fetchData(url);
};

export { fetchDataWrapper as fetchData };
