import axios from "axios";

/**
 * Revalidate external site cache for a specific domain
 * This function calls the external site's cache clearing endpoint
 * @param {string} domain - The domain to clear cache for
 */
export const revalidateExternalSite = async (domain) => {
  try {
    const response = await axios.post(
      `https://${domain}/api/cache/domain`,
      {
        domain: domain,
        action: "clear_domain",
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    console.log(`Cache revalidated successfully for domain: ${domain}`);

    // Special handling for chicbuysco.com domain
    // Revalidate platform-config tag
    const platformConfigResponse = await axios.get(
      `https://${domain}/api/revalidate?tag=platform-config`
    );
    console.log(
      "Platform config revalidated successfully:",
      platformConfigResponse.data
    );

    // Revalidate product tag
    const productResponse = await axios.get(
      `https://${domain}/api/revalidate?tag=product`
    );
    console.log("Product revalidated successfully:", productResponse.data);

    return response.data;
  } catch (error) {
    console.error(`Error revalidating cache for domain ${domain}:`, error);
    throw error;
  }
};
