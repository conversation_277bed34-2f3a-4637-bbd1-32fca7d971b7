import axios from "axios";
import fs from "fs";

const username = "devtruestore";
const password = "pas72ns2ws5ord";

async function fetchOrders(page = 1) {
  try {
    console.log(`<PERSON><PERSON> tải page ${page}`);
    const response = await axios.get("http://45.77.102.50:3006/api/orders", {
      params: {
        startdate: "09-07-2024",
        enddate: "09-07-2024",
        page,
        perPage: 50,
      },
      auth: {
        username,
        password,
      },
    });

    return response.data;
  } catch (error) {
    console.error(`Lỗi khi tải trang ${page}:`, error.message);
    throw error;
  }
}

async function processOrders() {
  let page = 1;
  let allOrders = [];
  let hasMorePages = true;

  try {
    while (hasMorePages) {
      const orders = await fetchOrders(page);
      allOrders = allOrders.concat(orders.data);

      if (page >= orders.totalPage) {
        hasMorePages = false;
      } else {
        page++;
      }
    }

    const ordersWithLineItems = allOrders.filter(
      (order) => order.orderData.line_items.length > 0,
    );
    const ordersWithoutLineItems = allOrders.filter(
      (order) => order.orderData.line_items.length === 0,
    );

    fs.writeFileSync(
      "orders_with_line_items.json",
      JSON.stringify(ordersWithLineItems, null, 2),
    );
    fs.writeFileSync(
      "orders_without_line_items.json",
      JSON.stringify(ordersWithoutLineItems, null, 2),
    );

    console.log("Đã tải xong tất cả đơn hàng và lưu vào các file riêng biệt.");
  } catch (error) {
    console.error("Có lỗi xảy ra:", error.message);
  }
}

processOrders();
