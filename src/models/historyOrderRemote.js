// models/remoteModel.js
import { DataTypes } from "sequelize";
import remoteSequelize from "../config/dbRemote.js";
import DomainConfig from "./domainConfig.js";

const HistoryOrder = remoteSequelize.define(
  "HistoryOrder",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    OrderID: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    Domain: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    Date: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    TotalPrice: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    LineitemQuantity: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    ItemName: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    ShippingName: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    ShippingAddress1: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    ShippingAddress2: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    ShippingCompany: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    ShippingCity: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    ShippingZip: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    ShippingProvince: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    ShippingCountry: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    ShippingPhone: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    PaymentAccount: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    TransactionIds: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    CustomerEmail: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    Status: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    ShipPrice: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    Sku: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    LinkProduct: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    DateOrder: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    Noti: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
  },
  {
    tableName: "wp_dropifyhistoryorder",
    timestamps: false,
  },
);

HistoryOrder.belongsTo(DomainConfig, {
  foreignKey: "Domain",
  targetKey: "domain",
});
// DomainConfig.hasMany(HistoryOrder, { foreignKey: 'domainConfigId', sourceKey: 'id' });

export default HistoryOrder;
