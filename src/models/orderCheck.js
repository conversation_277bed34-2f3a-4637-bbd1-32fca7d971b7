import { Model, DataTypes } from "sequelize";
import sequelize from "../config/db.js";

class OrderCheck extends Model {}

OrderCheck.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    orderId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Orders',
        key: 'id'
      }
    },
    paypal_order_id: {
      type: DataTypes.STRING,
      allowNull: false
    },
    check_count: {
      type: DataTypes.INTEGER,
      defaultValue: 1
    },
    status: {
      type: DataTypes.ENUM('pending', 'completed', 'failed'),
      defaultValue: 'pending'
    },
    paypal_status: {
      type: DataTypes.STRING,
      allowNull: true
    },
    error: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  },
  {
    sequelize,
    modelName: "OrderCheck",
    tableName: "OrderChecks",
  }
);

// Định nghĩa associations
OrderCheck.associate = (models) => {
  OrderCheck.belongsTo(models.Order, {
    foreignKey: 'orderId',
    as: 'order'
  });
};

export default OrderCheck;
