import { DataTypes } from "sequelize";
import sequelize from "../config/db.js";

const SaleReport = sequelize.define(
  "SaleReport",
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    domain: {
      type: DataTypes.TEXT,
      allowNull: false,
      collate: "utf8_general_ci",
    },
    date: {
      type: DataTypes.TEXT,
      allowNull: false,
      collate: "utf8_general_ci",
    },
    orders: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    money: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    currency: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: "USD",
    },
    view: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    atc: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    checkout: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    Stats: {
      type: DataTypes.JSON,
      allowNull: false,
    },
    StatsUTM: {
      type: DataTypes.JSON,
      allowNull: false,
    },
    orderIds: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: [],
    },
  },
  {
    tableName: "SaleReports",
  },
);

export default SaleReport;
