import { DataTypes } from "sequelize";
import sequelize from "../config/db.js";
import Order from "./order.js";

const OrderSync = sequelize.define("OrderSync", {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  },
  domain: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  transaction_id: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  orderId: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  orderData: {
    type: DataTypes.JSON,
    allowNull: false,
  },
  orderCreatedAt: {
    type: DataTypes.DATE,
    allowNull: true,
  },
});

Order.hasOne(OrderSync, {
  foreignKey: "transaction_id",
  sourceKey: "transaction_id",
  as: "orderSync",
});
OrderSync.belongsTo(Order, {
  foreignKey: "transaction_id",
  targetKey: "transaction_id",
  as: "order",
});

export default OrderSync;
