import { DataTypes } from "sequelize";
import sequelize from "../config/db.js";
import Order from "./order.js";

const TransactionEventLog = sequelize.define(
  "TransactionEventLog",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    order_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: { model: "Orders", key: "id" },
    },
    event_type: {
      type: DataTypes.ENUM(
        "CAPTURE_SUCCESS",
        "CAPTURE_PENDING",
        "CAPTURE_FAILED",
        "CAPTURE_ERROR"
      ),
      allowNull: false,
    },
    paypal_order_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    transaction_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    old_status: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    new_status: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    paypal_api_response: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    indexes: [
      { fields: ["order_id"] },
      { fields: ["event_type"] },
      { fields: ["paypal_order_id"] },
      { fields: ["transaction_id"] },
      { fields: ["created_at"] },
    ],
    tableName: "TransactionEventLogs",
    timestamps: false,
  }
);

TransactionEventLog.belongsTo(Order, { foreignKey: "order_id" });

export default TransactionEventLog; 