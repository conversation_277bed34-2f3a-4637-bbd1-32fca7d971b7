import { DataTypes } from "sequelize";
import sequelize from "../config/db.js";

const OrderNote = sequelize.define(
  "OrderNote",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    orderId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    note: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    createdBy: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  },
  {
    indexes: [
      {
        fields: ["orderId"],
      },
    ],
  },
);

export default OrderNote;
