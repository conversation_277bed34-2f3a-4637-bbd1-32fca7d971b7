import { DataTypes } from "sequelize";
import remoteSequelize from "../config/db2.js";

const DomainConfig = remoteSequelize.define(
  "DomainConfig",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    userid: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    domain: {
      type: DataTypes.TEXT,
      allowNull: false,
      collate: "utf8_general_ci",
    },
    consumer_key: {
      type: DataTypes.TEXT,
      allowNull: false,
      collate: "utf8_general_ci",
    },
    consumer_secret: {
      type: DataTypes.TEXT,
      allowNull: false,
      collate: "utf8_general_ci",
    },
    port: {
      type: DataTypes.TEXT,
      allowNull: false,
      collate: "utf8_general_ci",
    },
    mastore: {
      type: DataTypes.TEXT,
      allowNull: false,
      collate: "utf8_general_ci",
    },
    id_sale: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    madomain: {
      type: DataTypes.TEXT,
      allowNull: false,
      collate: "utf8_general_ci",
    },
  },
  {
    tableName: "wp_danhsachdomain",
    timestamps: false,
  },
);

export default DomainConfig;
