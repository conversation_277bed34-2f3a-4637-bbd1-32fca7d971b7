import { DataTypes } from "sequelize";
import sequelize from "../config/db.js";

const DomainReport = sequelize.define(
  "DomainReport",
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    domain: {
      type: DataTypes.TEXT,
      allowNull: false,
      collate: "utf8_general_ci",
    },
    date: {
      type: DataTypes.TEXT,
      allowNull: false,
      collate: "utf8_general_ci",
    },
    currency: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: "USD",
    },
    Stats: {
      type: DataTypes.JSON,
      allowNull: false,
    },
    orderIds: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: [],
    },
  },
  {
    tableName: "DomainReports",
  },
);

export default DomainReport;
