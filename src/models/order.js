import { DataTypes } from "sequelize";
import sequelize from "../config/db.js";
import OrderNote from "./orderNote.js";
import OrderCheck from "./orderCheck.js";

const Order = sequelize.define(
  "Order",
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    order_key: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    domain: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    transaction_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    paypal_client_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    orderData: {
      type: DataTypes.JSON,
      allowNull: false,
    },
    status: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: "processing",
    },
    tracking_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    port: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    paypal_order_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    paypal_checked_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    indexes: [
      {
        fields: ["transaction_id"],
      },
      {
        fields: ["domain", "status", "createdAt"],
      },
      {
        fields: ["createdAt"],
      },
      {
        fields: ["paypal_client_id"],
      },
      {
        fields: ["paypal_order_id"],
      },
      {
        fields: ["order_key"],
        unique: true,
      },
    ],
  }
);
Order.hasOne(OrderCheck, {
  foreignKey: "orderId",
});
Order.hasMany(OrderNote, { foreignKey: "orderId" });

export default Order;
