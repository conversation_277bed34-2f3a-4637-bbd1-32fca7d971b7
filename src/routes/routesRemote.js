import express from "express";
import { Op, Sequelize } from "sequelize";
import basicAuth from "basic-auth";
import { subMinutes } from "date-fns";
import { createObjectCsvWriter as createCsvWriter } from "csv-writer";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import Order from "../models/order.js";
import { convertToClientTimezone } from "../utils/utils.js";
import HistoryOrder from "../models/historyOrderRemote.js";
import DomainConfig from "../models/domainConfig.js";

const router = express.Router();
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
// Basic Authentication Middleware
const auth = (req, res, next) => {
  const user = basicAuth(req);

  if (!user || !user.name || !user.pass) {
    res.set("WWW-Authenticate", 'Basic realm="example"');
    return res.status(401).send("Authentication required.");
  }

  const username = "devtruestore";
  const password = "pas72ns2ws5ord";

  if (user.name === username && user.pass === password) {
    return next();
  }
  res.set("WWW-Authenticate", 'Basic realm="example"');
  return res.status(401).send("Invalid credentials.");
};

// Fetch history orders with optional filters
router.get("/history-orders", auth, async (req, res) => {
  const { perPage = 10, page = 1, domain, startdate, enddate } = req.query;

  const perPageInt = Math.min(Math.max(parseInt(perPage, 10), 1), 100);
  const pageInt = Math.max(parseInt(page, 10), 1);

  try {
    const historyOrderWhere = {};

    if (startdate || enddate) {
      const { startDate, endDate } = convertToClientTimezone(
        startdate,
        enddate,
      );
      historyOrderWhere.DateOrder = {};
      if (startDate) {
        historyOrderWhere.DateOrder[Op.gte] = startDate;
      }
      if (endDate) {
        historyOrderWhere.DateOrder[Op.lte] = new Date(endDate);
      }
      historyOrderWhere.totalPrice = {
        [Sequelize.Op.gt]: 0,
      };
    }

    const domainConfigWhere = {
      port: {
        [Op.ne]: "cardshield",
      },
    };

    if (domain) {
      domainConfigWhere.domain = domain;
    }

    const historyOrders = await HistoryOrder.findAndCountAll({
      where: historyOrderWhere,
      include: [
        {
          model: DomainConfig,
          where: domainConfigWhere,
        },
      ],
      limit: perPageInt,
      offset: (pageInt - 1) * perPageInt,
      order: [["id", "DESC"]],
    });

    res.json({
      totalItem: historyOrders.count,
      totalPage: Math.ceil(historyOrders.count / perPageInt),
      perPage: perPageInt,
      page: pageInt,
      data: historyOrders.rows,
    });
  } catch (error) {
    console.log(error);
    res.status(500).send("Internal Server Error");
  }
});

// Fetch domain configurations
router.get("/domain-config", auth, async (req, res) => {
  const { perPage = 10, page = 1 } = req.query;

  const perPageInt = Math.min(Math.max(parseInt(perPage, 10), 1), 100);
  const pageInt = Math.max(parseInt(page, 10), 1);

  try {
    const domains = await DomainConfig.findAndCountAll({
      where: {
        port: "cardshield",
      },
      limit: perPageInt,
      offset: (pageInt - 1) * perPageInt,
      order: [["id", "DESC"]],
    });

    res.json({
      totalItem: domains.count,
      totalPage: Math.ceil(domains.count / perPageInt),
      perPage: perPageInt,
      page: pageInt,
      data: domains.rows,
    });
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
});

router.get("/orders-missing", auth, async (req, res) => {
  const { startdate, enddate } = req.query;

  if (!startdate || !enddate) {
    return res.status(400).send("startdate and enddate are required");
  }

  try {
    const { startDate, endDate } = convertToClientTimezone(startdate, enddate);
    const orders = await Order.findAll({
      where: {
        createdAt: {
          [Op.between]: [startDate, endDate],
        },
      },
      attributes: ["transaction_id", "domain", "id", "orderData", "createdAt"],
    });

    const historyOrders = await HistoryOrder.findAll({
      where: {
        DateOrder: {
          [Op.between]: [startDate, endDate],
        },
        TotalPrice: {
          [Sequelize.Op.gt]: 0,
        },
      },
      include: [
        {
          model: DomainConfig,
          where: {
            port: {
              [Op.ne]: "cardshield",
            },
          },
        },
      ],
      attributes: [
        "TransactionIds",
        "Domain",
        "id",
        "OrderID",
        "Date",
        "TotalPrice",
        "LineitemQuantity",
        "ItemName",
        "ShippingName",
        "ShippingAddress1",
        "ShippingAddress2",
        "ShippingCompany",
        "ShippingCity",
        "ShippingZip",
        "ShippingProvince",
        "ShippingCountry",
        "ShippingPhone",
        "PaymentAccount",
        "CustomerEmail",
        "Status",
        "ShipPrice",
        "Sku",
        "LinkProduct",
        "DateOrder",
        "Noti",
      ],
    });

    const ordersByTransactionId = orders.reduce((acc, order) => {
      acc[order.transaction_id] = order;
      return acc;
    }, {});

    const historyOrdersByTransactionId = historyOrders.reduce((acc, order) => {
      acc[order.TransactionIds] = order;
      return acc;
    }, {});

    const missingInHistory = Object.keys(ordersByTransactionId)
      .filter((transactionId) => !historyOrdersByTransactionId[transactionId])
      .map((transactionId) => ordersByTransactionId[transactionId]);

    const csvWriter = createCsvWriter({
      path: path.join(__dirname, "missing_orders.csv"),
      header: [
        { id: "transaction_id", title: "Transaction ID" },
        { id: "domain", title: "Domain" },
        { id: "id", title: "Order ID" },
        { id: "orderData", title: "Order Data" },
        { id: "createdAt", title: "Created At" },
      ],
    });
    res.json(missingInHistory);

    // await csvWriter.writeRecords(missingInHistory);

    // res.download(path.join(__dirname, "missing_orders.csv"), "missing_orders.csv", (err) => {
    //   if (err) {
    //     console.log(err);
    //     res.status(500).send("Internal Server Error");
    //   } else {
    //     // Xóa file sau khi gửi xong
    //     fs.unlinkSync(path.join(__dirname, "missing_orders.csv"));
    //   }
    // });
  } catch (error) {
    console.log(JSON.stringify(error));
    res.status(500).send("Internal Server Error");
  }
});

router.get("/compare-orders-history", auth, async (req, res) => {
  const { startdate, enddate } = req.query;

  if (!startdate || !enddate) {
    return res.status(400).send("startdate and enddate are required");
  }

  try {
    const { startDate, endDate } = convertToClientTimezone(startdate, enddate);
    const orders = await Order.findAll({
      where: {
        createdAt: {
          [Op.between]: [startDate, endDate],
        },
      },
      attributes: ["transaction_id", "domain", "id", "orderData", "createdAt"],
    });

    const historyOrders = await HistoryOrder.findAll({
      where: {
        DateOrder: {
          [Op.between]: [startDate, endDate],
        },
        TotalPrice: {
          [Sequelize.Op.gt]: 0,
        },
      },
      include: [
        {
          model: DomainConfig,
          where: {
            port: {
              [Op.ne]: "cardshield",
            },
          },
        },
      ],
      attributes: [
        "TransactionIds",
        "Domain",
        "id",
        "OrderID",
        "Date",
        "TotalPrice",
        "LineitemQuantity",
        "ItemName",
        "ShippingName",
        "ShippingAddress1",
        "ShippingAddress2",
        "ShippingCompany",
        "ShippingCity",
        "ShippingZip",
        "ShippingProvince",
        "ShippingCountry",
        "ShippingPhone",
        "PaymentAccount",
        "CustomerEmail",
        "Status",
        "ShipPrice",
        "Sku",
        "LinkProduct",
        "DateOrder",
        "Noti",
      ],
    });

    const ordersByDomain = orders.reduce((acc, order) => {
      acc[order.domain] = acc[order.domain] || {};
      acc[order.domain][order.transaction_id] = order;
      return acc;
    }, {});

    const historyOrdersByDomain = historyOrders.reduce((acc, order) => {
      acc[order.Domain] = acc[order.Domain] || {};
      acc[order.Domain][order.TransactionIds] = order;
      return acc;
    }, {});

    const integrityCheck = {};

    const domains = new Set([
      ...Object.keys(ordersByDomain),
      ...Object.keys(historyOrdersByDomain),
    ]);

    domains.forEach((domain) => {
      const orderTransactions = ordersByDomain[domain] || {};
      const historyOrderTransactions = historyOrdersByDomain[domain] || {};
      const missingInHistory = Object.keys(orderTransactions)
        .filter((transactionId) => !historyOrderTransactions[transactionId])
        .map((transactionId) => orderTransactions[transactionId]);
      const missingInOrders = Object.keys(historyOrderTransactions)
        .filter((transactionId) => !orderTransactions[transactionId])
        .map((transactionId) => historyOrderTransactions[transactionId]);

      integrityCheck[domain] = {
        missingInHistory,
        missingInOrders,
      };
    });

    res.json(integrityCheck);
  } catch (error) {
    console.log(JSON.stringify(error));
    res.status(500).send("Internal Server Error");
  }
});

router.get("/sync-orders-history", auth, async (req, res) => {
  const { startdate, enddate } = req.query;

  if (!startdate || !enddate) {
    return res.status(400).send("startdate and enddate are required");
  }

  try {
    const { startDate, endDate } = convertToClientTimezone(startdate, enddate);
    const orders = await Order.findAll({
      where: {
        createdAt: {
          [Op.between]: [startDate, endDate],
        },
      },
      attributes: ["transaction_id", "domain", "id", "orderData"],
    });

    const historyOrders = await HistoryOrder.findAll({
      where: {
        DateOrder: {
          [Op.between]: [startDate, endDate],
        },
        TotalPrice: {
          [Sequelize.Op.gt]: 0,
        },
      },
      include: [
        {
          model: DomainConfig,
          where: {
            port: {
              [Op.ne]: "cardshield",
            },
          },
        },
      ],
      attributes: [
        "TransactionIds",
        "Domain",
        "id",
        "OrderID",
        "Date",
        "TotalPrice",
        "LineitemQuantity",
        "ItemName",
        "ShippingName",
        "ShippingAddress1",
        "ShippingAddress2",
        "ShippingCompany",
        "ShippingCity",
        "ShippingZip",
        "ShippingProvince",
        "ShippingCountry",
        "ShippingPhone",
        "PaymentAccount",
        "CustomerEmail",
        "Status",
        "ShipPrice",
        "Sku",
        "LinkProduct",
        "DateOrder",
        "Noti",
      ],
    });

    const ordersByDomain = orders.reduce((acc, order) => {
      acc[order.domain] = acc[order.domain] || {};
      acc[order.domain][order.transaction_id] = order;
      return acc;
    }, {});

    const historyOrdersByDomain = historyOrders.reduce((acc, order) => {
      acc[order.Domain] = acc[order.Domain] || {};
      acc[order.Domain][order.TransactionIds] = order;
      return acc;
    }, {});

    const integrityCheck = {};

    const domains = new Set([
      ...Object.keys(ordersByDomain),
      ...Object.keys(historyOrdersByDomain),
    ]);

    domains.forEach((domain) => {
      const orderTransactions = ordersByDomain[domain] || {};
      const historyOrderTransactions = historyOrdersByDomain[domain] || {};
      const missingInHistory = Object.keys(orderTransactions)
        .filter((transactionId) => !historyOrderTransactions[transactionId])
        .map((transactionId) => orderTransactions[transactionId]);
      const missingInOrders = Object.keys(historyOrderTransactions)
        .filter((transactionId) => !orderTransactions[transactionId])
        .map((transactionId) => historyOrderTransactions[transactionId]);

      integrityCheck[domain] = {
        missingInHistory,
        missingInOrders,
      };
    });

    res.json(integrityCheck);
  } catch (error) {
    console.log(JSON.stringify(error));
    res.status(500).send("Internal Server Error");
  }
});
export default router;
