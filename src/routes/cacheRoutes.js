import express from "express";
import {
  clearCacheByTagController,
  clearRedisCacheByTagController,
  revalidateExternalSiteController,
  getCacheByTagController,
  reduceCacheTTLController,
  clearCacheByPriorityController,
  flushAllCacheController,
  clearOldVersionCacheController,
  clearNextJSCacheController,
  cacheStatsController,
  clearAllConfigCacheController
} from "../controllers/cacheController.js";

const router = express.Router();

// Clear cache by tag (includes external revalidation + Redis clearing)
router.get("/clear-cache", clearCacheByTagController);

// Clear only Redis cache by tag (without external revalidation)
router.get("/clear-redis-cache", clearRedisCacheByTagController);

// Revalidate external site only
router.get("/revalidate-external", revalidateExternalSiteController);

// Get cache by tag
router.get("/get-cache", getCacheByTagController);

// Reduce cache TTL
router.get("/reduce-cache-ttl", reduceCacheTTLController);

// Clear cache by priority
router.get("/clear-cache-priority", clearCacheByPriorityController);

// Flush all cache
router.get("/flush-all-cache", flushAllCacheController);

// Clear old version cache
router.get("/clear-old-version-cache", clearOldVersionCacheController);

// Clear NextJS cache
router.get("/clear-nextjs-cache", clearNextJSCacheController);

// Cache statistics
router.get("/cache-stats", cacheStatsController);

// Clear all config cache
router.get("/clear-all-config-cache", clearAllConfigCacheController);

export default router;
