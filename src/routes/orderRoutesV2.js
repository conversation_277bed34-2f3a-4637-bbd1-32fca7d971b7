import express from "express";
import {
  createDraftOrderForPayPalV2,
  handleCapturePayPalV2
} from "../controllers/orderControllerV2.js";
import { getCache } from "../config/cache.js";

const router = express.Router();

// V2 PayPal draft order creation with Redis cache
router.post("/api/v2/payments/paypal/create", createDraftOrderForPayPalV2);

// V2 PayPal order capture with duplicate prevention
router.post("/api/v2/payments/paypal/capture/:orderId", handleCapturePayPalV2);

// V2 Health check endpoint
router.get("/api/v2/health", async (req, res) => {
  try {
    // Test Redis connection
    const testKey = `health_check_${Date.now()}`;
    await getCache(testKey); // This will test Redis connectivity
    
    res.json({
      status: "healthy",
      timestamp: new Date().toISOString(),
      api_version: "v2",
      services: {
        redis: "connected",
        api: "operational"
      }
    });
  } catch (error) {
    res.status(503).json({
      status: "unhealthy",
      timestamp: new Date().toISOString(),
      api_version: "v2",
      error: error.message,
      services: {
        redis: "disconnected",
        api: "operational"
      }
    });
  }
});

export default router; 