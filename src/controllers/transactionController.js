import TransactionEventLog from "../models/transactionEventLog.js";
import Order from "../models/order.js";

export const listTransactions = async (req, res) => {
  try {
    const {
      page = 1,
      perPage = 20,
      transaction_id,
      event_type,
      order_id
    } = req.query;

    const where = {};
    if (transaction_id) where.transaction_id = transaction_id;
    if (event_type) where.event_type = event_type;
    if (order_id) where.order_id = order_id;

    const limit = Math.max(1, Math.min(parseInt(perPage, 10), 100));
    const offset = (parseInt(page, 10) - 1) * limit;

    const { count, rows } = await TransactionEventLog.findAndCountAll({
      where,
      include: [{ model: Order, attributes: ["id", "domain", "transaction_id"] }],
      order: [["created_at", "DESC"]],
      limit,
      offset
    });

    res.json({
      total: count,
      totalPage: Math.ceil(count / limit),
      perPage: limit,
      page: parseInt(page, 10),
      data: rows
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}; 