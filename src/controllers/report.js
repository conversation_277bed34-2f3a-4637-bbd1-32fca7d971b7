import { Op } from "sequelize";
import currency from "currency.js";
import Order from "../models/order.js";
import { convertToClientTimezone } from "../utils/utils.js";
import logger from "../utils/logger.js";

export const generateReport = async (req, res) => {
  const { startdate, enddate } = req.query;

  if (!startdate || !enddate) {
    return res.status(400).send("startdate and enddate are required");
  }

  const { startDate, endDate } = convertToClientTimezone(startdate, enddate);

  const diffTime = Math.abs(endDate - startDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays > 90) {
    return res.status(400).send("The date range should not exceed 90 days");
  }

  try {
    const orders = await Order.findAll({
      where: {
        createdAt: {
          [Op.between]: [startDate, endDate],
        },
      },
    });

    const report = {};

    orders.forEach((order) => {
      const { domain } = order;
      const { orderData } = order;

      if (!report[domain]) {
        report[domain] = {
          total_sales: currency(0),
          total_orders: 0,
          total_items: 0,
          total_shipping: currency(0),
          items: new Map(),
        };
      }

      report[domain].total_sales = report[domain].total_sales.add(
        orderData.total,
      );
      report[domain].total_orders += 1;
      report[domain].total_items += orderData.line_items.reduce(
        (sum, item) => sum + item.quantity,
        0,
      );
      report[domain].total_shipping = report[domain].total_shipping.add(
        orderData.shipping_total,
      );

      orderData.line_items.forEach((item) => {
        const existingItem = report[domain].items.get(item.product_id);
        if (existingItem) {
          existingItem.quantity += item.quantity;
          existingItem.gross = existingItem.gross.add(item.total);
        } else {
          report[domain].items.set(item.product_id, {
            product_id: item.product_id,
            quantity: item.quantity,
            gross: currency(item.total),
            product_url: item.product_link,
            revenue: currency(orderData.total),
          });
        }
      });
    });

    // Convert Map to Array for items
    Object.keys(report).forEach((domain) => {
      report[domain].total_sales = report[domain].total_sales.value;
      report[domain].total_shipping = report[domain].total_shipping.value;
      report[domain].items = Array.from(report[domain].items.values());
    });

    res.json(report);
  } catch (error) {
    logger.error("Error generating report", {
      message: error.message,
      stack: error.stack,
      sql: error.sql,
      sqlState: error.sqlState,
      errno: error.errno,
    });
    res.status(500).send("Internal Server Error");
  }
};

export const generateProductReport = async (req, res) => {
  const { startdate, enddate } = req.query;

  if (!startdate || !enddate) {
    return res.status(400).send("startdate and enddate are required");
  }

  const { startDate, endDate } = convertToClientTimezone(startdate, enddate);

  const diffTime = Math.abs(endDate - startDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays > 90) {
    return res.status(400).send("The date range should not exceed 90 days");
  }

  try {
    const orders = await Order.findAll({
      where: {
        createdAt: {
          [Op.between]: [startDate, endDate],
        },
      },
    });

    const report = {};

    orders.forEach((order) => {
      const { domain } = order;
      const { orderData } = order;

      if (!report[domain]) {
        report[domain] = {};
      }

      orderData.line_items.forEach((item) => {
        if (!report[domain][item.product_id]) {
          report[domain][item.product_id] = {
            quantity: 0,
            revenue: currency(0),
            name: item.name,
            link: item.product_link,
            thumbnail: item.image,
          };
        }

        report[domain][item.product_id].quantity += item.quantity;
        report[domain][item.product_id].revenue = report[domain][
          item.product_id
        ].revenue.add(item.total);
      });
    });

    Object.keys(report).forEach((domain) => {
      Object.keys(report[domain]).forEach((productId) => {
        report[domain][productId].revenue =
          report[domain][productId].revenue.value;
      });
    });

    res.json(report);
  } catch (error) {
    logger.error("Error generating product report", {
      message: error.message,
      stack: error.stack,
      sql: error.sql,
      sqlState: error.sqlState,
      errno: error.errno,
    });
    res.status(500).send("Internal Server Error");
  }
};
