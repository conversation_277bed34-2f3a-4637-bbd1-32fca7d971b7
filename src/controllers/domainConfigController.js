import DomainConfig from "../models/domainConfig.js";

export const getDomainConfigs = async (req, res) => {
  const { page = 1, limit = 10 } = req.query;

  try {
    const offset = (page - 1) * limit;
    const { count, rows } = await DomainConfig.findAndCountAll({
      limit: parseInt(limit),
      offset: parseInt(offset),
    });

    res.json({
      totalItems: count,
      totalPages: Math.ceil(count / limit),
      currentPage: parseInt(page),
      data: rows,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};
