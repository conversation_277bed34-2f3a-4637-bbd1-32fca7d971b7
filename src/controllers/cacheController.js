import {
  clearCacheByTag,
  getCacheByTag,
  reduceCacheTTL,
  clearCacheByPriority,
  flushAllCache,
  clearOldVersionCache,
  clearAllNextJSCache,
  redisClient
} from "../config/cache.js";
import { revalidateExternalSite } from "../utils/externalSiteRevalidation.js";
import logger from "../utils/logger.js";
import axios from "axios";

// Clear cache by tag controller (includes external revalidation + Redis clearing)
export const clearCacheByTagController = async (req, res) => {
  const { tag } = req.query;

  if (!tag) {
    return res.status(400).send("Tag is required");
  }

  try {
    // First, revalidate external site (tag is the domain)
    await revalidateExternalSite(tag);

    // Then clear Redis cache
    const clearedCount = await clearCacheByTag(tag);

    res.json({
      success: true,
      domain: tag,
      action: "clear_domain",
      results: [
        `Cleared Redis cache for domain: ${tag} (${clearedCount} entries)`,
        `Cleared domain cache for: ${tag}`
      ],
      timestamp: Date.now()
    });
  } catch (error) {
    logger.error("Error clearing cache", {
      message: error.message,
      stack: error.stack,
      tag: tag
    });
    res.status(500).send("Internal Server Error");
  }
};

// Clear only Redis cache by tag (without external revalidation)
export const clearRedisCacheByTagController = async (req, res) => {
  const { tag } = req.query;

  if (!tag) {
    return res.status(400).send("Tag is required");
  }

  try {
    const clearedCount = await clearCacheByTag(tag);
    res.json({
      message: `Redis cache cleared for tag: ${tag}`,
      clearedCount: clearedCount,
      externalRevalidated: false
    });
  } catch (error) {
    logger.error("Error clearing Redis cache", {
      message: error.message,
      stack: error.stack,
      tag: tag
    });
    res.status(500).send("Internal Server Error");
  }
};

// Revalidate external site only
export const revalidateExternalSiteController = async (req, res) => {
  const { domain } = req.query;

  if (!domain) {
    return res.status(400).send("Domain is required");
  }

  try {
    await revalidateExternalSite(domain);
    res.json({
      success: true,
      domain: domain,
      action: "clear_domain",
      results: [`Cleared domain cache for: ${domain}`],
      timestamp: Date.now()
    });
  } catch (error) {
    logger.error("Error revalidating external site", {
      message: error.message,
      stack: error.stack,
      domain: domain
    });
    res.status(500).send("Internal Server Error");
  }
};

// Get cache by tag controller
export const getCacheByTagController = async (req, res) => {
  const { tag } = req.query;

  if (!tag) {
    return res.status(400).send("Tag is required");
  }

  try {
    const data = await getCacheByTag(tag);
    res.json(data);
  } catch (error) {
    logger.error("Error getting cache by tag", {
      message: error.message,
      stack: error.stack,
      tag: tag
    });
    res.status(500).send("Internal Server Error");
  }
};

// Reduce cache TTL controller
export const reduceCacheTTLController = async (req, res) => {
  try {
    const percentage = parseInt(req.query.percentage) || 90;
    const modifiedCount = await reduceCacheTTL(percentage);
    res.json({ 
      message: `Reduced TTL for ${modifiedCount} cache entries by ${percentage}%`,
      modifiedCount: modifiedCount,
      percentage: percentage
    });
  } catch (error) {
    logger.error('Error reducing cache TTL', { 
      message: error.message, 
      stack: error.stack 
    });
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// Clear cache by priority controller
export const clearCacheByPriorityController = async (req, res) => {
  try {
    const percentage = parseInt(req.query.percentage) || 20;
    const deletedCount = await clearCacheByPriority(percentage);
    res.json({ 
      message: `Cleared ${deletedCount} cache entries`,
      deletedCount: deletedCount,
      percentage: percentage
    });
  } catch (error) {
    logger.error('Error clearing cache by priority', { 
      message: error.message, 
      stack: error.stack 
    });
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// Flush all cache controller
export const flushAllCacheController = async (req, res) => {
  try {
    await flushAllCache();
    res.json({ message: "All cache has been flushed" });
  } catch (error) {
    logger.error('Error flushing all cache', { 
      message: error.message, 
      stack: error.stack 
    });
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// Clear old version cache controller
export const clearOldVersionCacheController = async (req, res) => {
  try {
    const deletedCount = await clearOldVersionCache();
    
    if (deletedCount > 0) {
      res.json({ 
        message: `Cleared ${deletedCount} cache entries from old versions`,
        current_version: process.env.NEXT_PUBLIC_VERSION,
        deletedCount: deletedCount
      });
    } else {
      res.json({ 
        message: "No old version cache found",
        current_version: process.env.NEXT_PUBLIC_VERSION,
        deletedCount: 0
      });
    }
  } catch (error) {
    logger.error('Error clearing old version cache', { 
      message: error.message, 
      stack: error.stack,
      current_version: process.env.NEXT_PUBLIC_VERSION
    });
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// Clear NextJS cache controller
export const clearNextJSCacheController = async (req, res) => {
  try {
    const deletedCount = await clearAllNextJSCache();
    
    if (deletedCount > 0) {
      res.json({ 
        message: `Cleared ${deletedCount} NextJS cache entries`,
        current_version: process.env.NEXT_PUBLIC_VERSION,
        deletedCount: deletedCount
      });
    } else {
      res.json({ 
        message: "No NextJS cache found",
        current_version: process.env.NEXT_PUBLIC_VERSION,
        deletedCount: 0
      });
    }
  } catch (error) {
    logger.error('Error clearing NextJS cache', { 
      message: error.message, 
      stack: error.stack,
      current_version: process.env.NEXT_PUBLIC_VERSION
    });
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// Cache stats controller
export const cacheStatsController = async (req, res) => {
  try {
    // 1. Thông tin về bộ nhớ Redis đang sử dụng
    const memoryInfo = await redisClient.info("memory");

    // 2. Số lượng keys trong Redis
    const dbSize = await redisClient.dbsize();

    // 3. Lấy mẫu các keys và kích thước của chúng
    const sampleSize = 1000; // Số lượng keys mẫu
    const keys = await redisClient.keys('*');
    const sampleKeys = keys.slice(0, sampleSize);
    
    let totalSize = 0;
    const keySizes = await Promise.all(sampleKeys.map(async (key) => {
      const size = await redisClient.memory('usage', key);
      totalSize += size;
      return { key, size };
    }));

    const averageSize = totalSize / sampleKeys.length;
    const estimatedTotalSize = averageSize * keys.length / (1024 * 1024); // Convert to MB

    // 4. Phân tích các loại dữ liệu
    const typeCount = {};
    await Promise.all(sampleKeys.map(async (key) => {
      const type = await redisClient.type(key);
      typeCount[type] = (typeCount[type] || 0) + 1;
    }));

    // Thêm phân tích version
    const versionCount = {};
    await Promise.all(sampleKeys.map(async (key) => {
      const match = key.match(/next-cache:(.*?):/);
      const version = match ? match[1] : 'unknown';
      versionCount[version] = (versionCount[version] || 0) + 1;
    }));

    res.json({
      memoryInfo,
      dbSize,
      sampleSize,
      averageKeySize: averageSize,
      estimatedTotalSize: `${estimatedTotalSize.toFixed(2)} MB`,
      typeDistribution: typeCount,
      versionDistribution: versionCount,
      currentVersion: process.env.NEXT_PUBLIC_VERSION,
      sampleKeySizes: keySizes
    });
  } catch (error) {
    logger.error("Error fetching cache stats", {
      message: error.message,
      stack: error.stack
    });
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// Clear all config cache controller
export const clearAllConfigCacheController = async (req, res) => {
  try {
    const response = await axios.get("https://itemlandus.com/api/cache/domain?action=clear_config_all");

    console.log("All config cache cleared successfully");

    res.json({
      success: true,
      message: "All config cache cleared successfully",
      externalResponse: response.data,
      timestamp: Date.now()
    });
  } catch (error) {
    logger.error("Error clearing all config cache", {
      message: error.message,
      stack: error.stack
    });
    res.status(500).json({ error: "Internal Server Error" });
  }
};
