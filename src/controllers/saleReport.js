import SaleReport from "../models/saleReport.js";
import logger from "../utils/logger.js";

export const getSaleReport = async (req, res) => {
  try {
    const { domain, date } = req.query;

    if (!domain && !date) {
      return res.status(400).send("At least one of domain or date is required");
    }
    const dateSplit = date.split("-");
    const formattedDate = `${dateSplit[2]}-${dateSplit[1]}-${dateSplit[0]}`;

    const whereClause = {};
    if (domain) whereClause.domain = domain;
    if (date) whereClause.date = formattedDate;

    const report = await SaleReport.findAll({
      where: whereClause
    });

    if (!report) {
      return res.status(404).send("Sale report not found");
    }

    res.status(200).json(report);
  } catch (error) {
    logger.error("Error getting sale report", {
      message: error.message,
      stack: error.stack,
      sql: error.sql,
      sqlState: error.sqlState,
      errno: error.errno,
    });
    res.status(500).send("Internal Server Error");
  }
};
