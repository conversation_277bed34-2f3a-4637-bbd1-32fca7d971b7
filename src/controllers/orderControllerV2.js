import { Op, Sequelize } from "sequelize";
import logger from "../utils/logger.js";
import { getCache, setCache, clearCache } from "../config/cache.js";
import {
  generateDraftCacheKey,
  generateSuccessCacheKey,
  generateCapturedInvoiceCacheKey,
  generateDraftInvoiceCacheKey,
  getMetaDataValue
} from "../utils/keyGenerator.js";
import {
  calculateOrderTotals,
  generateOrderKey,
  formatStats,
} from "../utils/utils.js";
import Order from "../models/order.js";
import {
  createPayPalOrder,
  capturePayPalOrder,
  checkPaymentStatus,
} from "../services/payPalService.js";
import {
  createNewOrderFromPayPal,
  updateOrderAfterPayPalCapture,
} from "../services/orderService.js";
import TransactionLogger from "../services/transactionLogger.js";
import { sendToQueue } from "../config/rabbitmq.js";
import config from "../config/config.js";

// Environment variables for V2 configuration
const DRAFT_ORDER_TTL = parseInt(process.env.DRAFT_ORDER_TTL) || 1800; // 30 minutes
const SUCCESS_ORDER_TTL = parseInt(process.env.SUCCESS_ORDER_TTL) || 7200; // 120 minutes
const DUPLICATE_CHECK_WINDOW = parseInt(process.env.DUPLICATE_CHECK_WINDOW) || 300; // 5 minutes

/**
 * V2 Create Draft Order with Redis Cache
 * Response format same as V1 for compatibility
 */
const createDraftOrderForPayPalV2 = async (req, res) => {
  console.time("createDraftOrderForPayPalV2 - Total");
  try {
    // STEP 1: Validation section (same structure as V1)
    let validationResult;
    try {
      const {
        domain,
        paypal_client_id,
        payment_method,
        shipping_lines,
        meta_data,
        set_paid,
        billing,
        shipping,
        payment_method_title,
        line_items,
        free_shipping,
        discount,
        tip,
        email,
        currency = "USD"
      } = req.body;

      logger.info(`[V2] [${domain}] Creating PayPal draft order`, {
        domain,
        paypal_client_id,
        currency,
        total: req.body.total,
        requestBody: req.body,
        api_version: "v2"
      });

      if (!domain || !paypal_client_id || !line_items) {
        console.timeEnd("createDraftOrderForPayPalV2 - Total");
        return res.status(400).json({
          error: "Missing required fields",
        });
      }

      if (!meta_data || !Array.isArray(meta_data)) {
        console.timeEnd("createDraftOrderForPayPalV2 - Total");
        return res.status(400).json({
          error: "meta_data is required and must be an array",
        });
      }

      if (!Array.isArray(line_items) || line_items.length === 0) {
        console.timeEnd("createDraftOrderForPayPalV2 - Total");
        return res.status(400).json({
          error: "line_items is required and must be a non-empty array",
        });
      }

      if (!Array.isArray(shipping_lines)) {
        console.timeEnd("createDraftOrderForPayPalV2 - Total");
        return res.status(400).json({
          error: "shipping_lines is required and must be an array",
        });
      }

      const validCurrencies = ["USD", "EUR", "GBP", "AUD", "CAD"];
      if (!validCurrencies.includes(currency)) {
        logger.warn("Invalid currency code, using USD", {
          providedCurrency: currency,
          defaultingTo: "USD"
        });
      }

      const invoice_id = getMetaDataValue(meta_data, 'invoice_id');
      const funding_source = getMetaDataValue(meta_data, 'funding_source');
      const ip = getMetaDataValue(meta_data, 'ip');
      const userAgent = getMetaDataValue(meta_data, 'UA');

      if (!invoice_id || !funding_source) {
        console.timeEnd("createDraftOrderForPayPalV2 - Total");
        return res.status(400).json({
          error: "Missing invoice_id or funding_source in meta_data",
        });
      }

      if (!ip || !userAgent) {
        console.timeEnd("createDraftOrderForPayPalV2 - Total");
        return res.status(400).json({
          error: "Missing ip or UA in meta_data for V2 caching",
        });
      }

      validationResult = {
        domain, paypal_client_id, payment_method, shipping_lines, meta_data,
        set_paid, billing, shipping, payment_method_title, line_items,
        free_shipping, discount, tip, email, currency, invoice_id, funding_source,
        ip, userAgent
      };
    } catch (validationError) {
      logger.error(`[V2] [${validationResult?.domain || 'unknown'}] Error in validation step:`, validationError);
      throw validationError;
    }

    // STEP 2: V2 Cache Logic - Check for duplicates and draft cache
    console.time(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Cache Operations`);
    try {
      // Generate cache keys
      const draftCacheKey = generateDraftCacheKey(validationResult.domain, validationResult.meta_data, validationResult.line_items, validationResult.shipping_lines);
      const successCacheKey = generateSuccessCacheKey(validationResult.domain, validationResult.meta_data, validationResult.line_items, validationResult.shipping_lines);
      
      logger.info(`[V2] [${validationResult.domain}] Generated cache keys`, {
        draftCacheKey,
        successCacheKey,
        domain: validationResult.domain,
        ip: validationResult.ip,
        userAgent: validationResult.userAgent.substring(0, 50) + '...',
        lineItemsCount: validationResult.line_items.length
      });

      // Check for duplicate successful orders first (EARLY PREVENTION)
      const cachedSuccess = await getCache(successCacheKey);
      
      if (cachedSuccess) {
        const timeDifferenceMs = Date.now() - cachedSuccess.created_at;
        const timeDifferenceMinutes = timeDifferenceMs / 1000 / 60;
        
        if (timeDifferenceMinutes <= (DUPLICATE_CHECK_WINDOW / 60)) {
          logger.warn(`[V2] [${validationResult.domain}] Duplicate order blocked at draft creation`, {
            successCacheKey,
            existingOrderId: cachedSuccess.order_id,
            timeDifferenceMinutes: Math.round(timeDifferenceMinutes * 100) / 100,
            api_version: "v2"
          });

          console.timeEnd(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Cache Operations`);
          console.timeEnd("createDraftOrderForPayPalV2 - Total");
          
          // Return 409 with existing order info for FE redirect (EARLY BLOCK)
          return res.status(409).json({
            error: "DUPLICATE_ORDER",
            message: "Duplicate order detected within 5 minutes",
            existing_order: {
              id: cachedSuccess.order_id,
              order_id: cachedSuccess.order_id,
              order_key: cachedSuccess.order_key,
              transaction_id: cachedSuccess.transaction_id,
              created_at: new Date(cachedSuccess.created_at).toISOString()
            },
            time_remaining_minutes: Math.ceil((DUPLICATE_CHECK_WINDOW / 60) - timeDifferenceMinutes),
            blocked_at: "draft_creation"
          });
        } else {
          logger.info(`[V2] [${validationResult.domain}] Cached success order found but outside duplicate window`, {
            successCacheKey,
            timeDifferenceMinutes: Math.round(timeDifferenceMinutes * 100) / 100,
            windowMinutes: DUPLICATE_CHECK_WINDOW / 60,
            allowNewOrder: true,
            api_version: "v2"
          });
        }
      }

      // INVOICE SAFETY CHECK: Kiểm tra invoice_id hiện tại đã được capture chưa  
      const capturedInvoiceCacheKey = generateCapturedInvoiceCacheKey(validationResult.domain, validationResult.invoice_id);
      const capturedInvoice = await getCache(capturedInvoiceCacheKey);
      
      if (capturedInvoice) {
        logger.warn(`[V2] [${validationResult.domain}] INVOICE SAFETY: Current invoice_id already captured`, {
          capturedInvoiceCacheKey,
          invoiceId: validationResult.invoice_id,
          existingOrderId: capturedInvoice.order_id,
          existingTransactionId: capturedInvoice.transaction_id,
          api_version: "v2"
        });

        console.timeEnd(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Cache Operations`);
        console.timeEnd("createDraftOrderForPayPalV2 - Total");
        
        // Return 409 với existing order info (invoice collision)
        return res.status(409).json({
          error: "INVOICE_ALREADY_CAPTURED",
          message: "This invoice_id has already been captured",
          existing_order: {
            id: capturedInvoice.order_id,
            order_key: capturedInvoice.order_key,
            transaction_id: capturedInvoice.transaction_id,
            created_at: new Date(capturedInvoice.created_at).toISOString()
          },
          blocked_at: "invoice_check",
          invoice_id: validationResult.invoice_id
        });
      }

      // Check draft cache for performance optimization
      const cachedDraft = await getCache(draftCacheKey);
      
      if (cachedDraft) {
        // INVOICE MISMATCH CHECK: Draft cache có invoice_id khác với request hiện tại
        if (cachedDraft.invoice_id !== validationResult.invoice_id) {
          logger.warn(`[V2] [${validationResult.domain}] INVOICE MISMATCH: Draft cache has different invoice_id`, {
            draftCacheKey,
            cachedInvoiceId: cachedDraft.invoice_id,
            currentInvoiceId: validationResult.invoice_id,
            paypalOrderId: cachedDraft.paypal_order_id,
            api_version: "v2"
          });
          
          // Don't use stale cache with different invoice_id
          logger.info(`[V2] [${validationResult.domain}] Ignoring draft cache due to invoice_id mismatch`, {
            reason: "invoice_mismatch",
            api_version: "v2"
          });
        } else {
          const cacheAge = Date.now() - cachedDraft.created_at;
          const cacheAgeMinutes = Math.floor(cacheAge / 1000 / 60);
          
          logger.info(`[V2] [${validationResult.domain}] Draft cache HIT with matching invoice_id`, {
            draftCacheKey,
            paypalOrderId: cachedDraft.paypal_order_id,
            invoiceId: validationResult.invoice_id,
            cacheAgeMinutes,
            cached: true,
            api_version: "v2"
          });

          console.timeEnd(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Cache Operations`);
          console.timeEnd("createDraftOrderForPayPalV2 - Total");

          // Return cached PayPal order ID (same format as V1)
          return res.json({
            id: cachedDraft.paypal_order_id,
            currency: cachedDraft.currency,
            cached: true,
            cache_age_minutes: cacheAgeMinutes
          });
        }
      }

      logger.info(`[V2] [${validationResult.domain}] Draft cache MISS - Creating new order`, {
        draftCacheKey,
        cached: false,
        api_version: "v2"
      });

      validationResult.draftCacheKey = draftCacheKey;
      validationResult.successCacheKey = successCacheKey;
    } catch (cacheError) {
      console.timeEnd(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Cache Operations`);
      logger.error(`[V2] [${validationResult.domain}] Error in cache operations step:`, cacheError);
      throw cacheError;
    }
    console.timeEnd(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Cache Operations`);

    // STEP 3: Calculate Totals section (same as V1)
    let calculationResult;
    try {
      const {
        total,
        subTotal: sub_total,
        shippingTotal: shipping_total,
        discountTotal: discount_total,
        tipTotal: tip_total
      } = calculateOrderTotals(
        validationResult.line_items,
        validationResult.shipping_lines,
        validationResult.free_shipping,
        validationResult.discount,
        validationResult.tip
      );

      const order_key = generateOrderKey();

      calculationResult = {
        total, sub_total, shipping_total, discount_total, tip_total, order_key
      };
    } catch (calculationError) {
      logger.error(`[V2] [${validationResult.domain}] Error in calculation step:`, calculationError);
      throw calculationError;
    }

    // STEP 4: Create Draft Order DB section (same as V1)
    console.time(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Create Draft Order DB`);
    let draftOrder;
    try {
      draftOrder = await createNewOrderFromPayPal({
        status: "draft",
        domain: validationResult.domain,
        paypal_client_id: validationResult.paypal_client_id,
        order_key: calculationResult.order_key,
        orderData: {
          payment_method: validationResult.payment_method,
          shipping_lines: validationResult.shipping_lines,
          meta_data: validationResult.meta_data,
          set_paid: validationResult.set_paid,
          billing: validationResult.billing,
          shipping: validationResult.shipping,
          payment_method_title: validationResult.payment_method_title,
          total: calculationResult.total,
          shipping_total: calculationResult.shipping_total,
          sub_total: calculationResult.sub_total,
          discount_total: calculationResult.discount_total,
          tip_total: calculationResult.tip_total,
          line_items: validationResult.line_items,
          funding_source: validationResult.funding_source,
          invoice_id: validationResult.invoice_id,
          free_shipping: validationResult.free_shipping,
          email: validationResult.email,
          currency: validationResult.currency
        },
      });
    } catch (dbError) {
      console.timeEnd(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Create Draft Order DB`);
      logger.error(`[V2] [${validationResult.domain}] Error in database creation step:`, dbError);
      throw dbError;
    }
    console.timeEnd(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Create Draft Order DB`);

    // STEP 5: Create PayPal Order section (same as V1)
    console.time(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Create PayPal Order`);
    let paypalOrder;
    try {
      paypalOrder = await createPayPalOrder(validationResult.paypal_client_id, {
        total: calculationResult.total,
        sub_total: calculationResult.sub_total,
        shipping_total: calculationResult.shipping_total,
        discount_total: calculationResult.discount_total,
        tip_total: calculationResult.tip_total,
        line_items: validationResult.line_items,
        invoice_id: validationResult.invoice_id,
        funding_source: validationResult.funding_source,
        currency: validationResult.currency,
        paypal_client_id: validationResult.paypal_client_id
      });
    } catch (paypalError) {
      console.timeEnd(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Create PayPal Order`);
      logger.error(`[V2] [${validationResult.domain}] Error in PayPal order creation step:`, paypalError);
      throw paypalError;
    }
    console.timeEnd(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Create PayPal Order`);

    // STEP 6: Update Draft Order section (same as V1)
    console.time(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Update Draft Order - PayPal ID: ${paypalOrder.id}`);
    try {
      await draftOrder.update({
        paypal_order_id: paypalOrder.id,
      });
    } catch (updateError) {
      console.timeEnd(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Update Draft Order - PayPal ID: ${paypalOrder.id}`);
      logger.error(`[V2] [${validationResult.domain}] Error in draft order update step - PayPal ID: ${paypalOrder.id}:`, updateError);
      throw updateError;
    }
    console.timeEnd(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Update Draft Order - PayPal ID: ${paypalOrder.id}`);

    // STEP 7: V2 Cache the draft order
    console.time(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Cache Draft Order - PayPal ID: ${paypalOrder.id}`);
    try {
      const cacheValue = {
        paypal_order_id: paypalOrder.id,
        db_order_id: draftOrder.id,
        created_at: Date.now(),
        domain: validationResult.domain,
        total: calculationResult.total,
        currency: validationResult.currency,
        line_items: validationResult.line_items,
        invoice_id: validationResult.invoice_id, // IMPORTANT: Store invoice_id for mismatch detection
        user_session: {
          ip: validationResult.ip,
          ua_hash: validationResult.userAgent.substring(0, 50)
        }
      };

      await setCache(validationResult.draftCacheKey, cacheValue, [], DRAFT_ORDER_TTL);
    } catch (cacheError) {
      console.timeEnd(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Cache Draft Order - PayPal ID: ${paypalOrder.id}`);
      logger.error(`[V2] [${validationResult.domain}] Error in draft order caching step - PayPal ID: ${paypalOrder.id}:`, cacheError);
      throw cacheError;
    }
    console.timeEnd(`[V2] [${validationResult.domain}] createDraftOrderForPayPalV2 - Cache Draft Order - PayPal ID: ${paypalOrder.id}`);

    logger.info(`[V2] [${validationResult.domain}] Draft order created successfully - PayPal ID: ${paypalOrder.id}`, {
      orderId: draftOrder.id,
      orderKey: calculationResult.order_key,
      paypalOrderId: paypalOrder.id,
      currency: validationResult.currency,
      domain: validationResult.domain,
      api_version: "v2"
    });

    console.timeEnd("createDraftOrderForPayPalV2 - Total");
    res.status(201).json({
      id: paypalOrder.id,
      currency: validationResult.currency,
      cached: false
    });
  } catch (error) {
    console.timeEnd("createDraftOrderForPayPalV2 - Total");
    const domain = error.validationResult?.domain || 'unknown';
    logger.error(`[V2] [${domain}] Error in createDraftOrderForPayPalV2:`, error);
    res.status(500).json({ error: error.message || "Internal Server Error" });
  }
};

/**
 * V2 Capture PayPal Order with Duplicate Prevention
 */
const handleCapturePayPalV2 = async (req, res) => {
  console.time("handleCapturePayPalV2 - Total");
  let order;
  
  try {
    const { orderId } = req.params;
    console.time(`[V2] [PayPal ID: ${orderId}] handleCapturePayPalV2 - Initial Validation & Order Lookup`);
    
    if (!orderId) {
      console.timeEnd(`[V2] [PayPal ID: ${orderId}] handleCapturePayPalV2 - Initial Validation & Order Lookup`);
      console.timeEnd("handleCapturePayPalV2 - Total");
      return res.status(400).json({ error: "PayPal Order ID is required" });
    }
    
    order = await Order.findOne({ where: { paypal_order_id: orderId } });
    
    if (!order) {
      logger.error(`[V2] [PayPal ID: ${orderId}] Order not found for PayPal order ID:`, orderId);
      console.timeEnd(`[V2] [PayPal ID: ${orderId}] handleCapturePayPalV2 - Initial Validation & Order Lookup`);
      console.timeEnd("handleCapturePayPalV2 - Total");
      return res.status(404).json({ error: "Order not found" });
    }
    
    console.timeEnd(`[V2] [PayPal ID: ${orderId}] handleCapturePayPalV2 - Initial Validation & Order Lookup`);

    const domain = order.domain;
    const meta_data = order.orderData?.meta_data || [];
    const line_items = order.orderData?.line_items || [];

    console.time(`[V2] [${domain}] [PayPal ID: ${orderId}] handleCapturePayPalV2 - Check PayPal Order Status`);
    let paypalOrderStatus;
    try {
      paypalOrderStatus = await checkPaymentStatus(orderId, order.paypal_client_id);
    } catch (statusError) {
      console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] handleCapturePayPalV2 - Check PayPal Order Status`);
      logger.error(`[V2] [${domain}] [PayPal ID: ${orderId}] Error checking PayPal order status before capture`, { orderId, error: statusError.message });
      await TransactionLogger.logFinalOutcome({
        orderId: order.id,
        eventType: "CAPTURE_ERROR",
        paypalOrderId: orderId,
        notes: `V2 Error checking PayPal order status: ${statusError.message}`,
        paypalApiResponse: statusError,
        order: order
      });
      console.timeEnd("handleCapturePayPalV2 - Total");
      return res.status(500).json({
        error: "Failed to check PayPal order status",
        details: statusError.message
      });
    }
    console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] handleCapturePayPalV2 - Check PayPal Order Status`);

    if (paypalOrderStatus !== "APPROVED") {
      logger.error(`[V2] [${domain}] [PayPal ID: ${orderId}] PayPal order is not in APPROVED state, cannot capture`, { orderId, paypalOrderStatus });
      await TransactionLogger.logFinalOutcome({
        orderId: order.id,
        eventType: "CAPTURE_FAILED",
        paypalOrderId: orderId,
        notes: `V2 Order not approved: ${paypalOrderStatus}`,
        paypalApiResponse: { orderStatus: paypalOrderStatus },
        order: order
      });
      console.timeEnd("handleCapturePayPalV2 - Total");
      return res.status(400).json({
        error: "Order is not approved for capture",
        details: `Current PayPal order status: ${paypalOrderStatus}`,
        code: "ORDER_NOT_APPROVED"
      });
    }

    console.time(`[V2] [${domain}] [PayPal ID: ${orderId}] handleCapturePayPalV2 - PayPal Capture API Call`);
    try {
      const captureData = await capturePayPalOrder(
        order.paypal_client_id,
        orderId,
        order.domain,
        order.orderData?.meta_data || []
      );
      console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] handleCapturePayPalV2 - PayPal Capture API Call`);

      console.time(`[V2] [${domain}] [PayPal ID: ${orderId}] handleCapturePayPalV2 - Process Capture Response`);
      const captureStatus = captureData.status;
      const captureDetails = captureData.purchase_units[0]?.payments?.captures?.[0];
      const captureAmount = parseFloat(captureDetails?.amount?.value || 0);
      const expectedAmount = parseFloat(order.orderData.total || 0);
      console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] handleCapturePayPalV2 - Process Capture Response`);

      if (captureStatus === "COMPLETED") {
        const transactionId = captureDetails?.id;
        console.time(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPalV2 - Amount Validation`);
        // Validate amount
        if (Math.abs(captureAmount - expectedAmount) > 0.01) {
          console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPalV2 - Amount Validation`);
          await TransactionLogger.logFinalOutcome({
            orderId: order.id,
            eventType: "CAPTURE_ERROR",
            paypalOrderId: orderId,
            transactionId: captureDetails?.id,
            oldStatus: order.status,
            newStatus: order.status,
            paypalApiResponse: captureData,
            notes: `V2 Amount mismatch: captured ${captureAmount}, expected ${expectedAmount}`,
            order: order
          });
          console.timeEnd("handleCapturePayPalV2 - Total");
          return res.status(400).json({
            error: "Amount mismatch",
            details: `Captured ${captureAmount}, expected ${expectedAmount}`,
            code: "AMOUNT_MISMATCH"
          });
        }
        console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPalV2 - Amount Validation`);

        console.time(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPalV2 - Process Shipping & Billing Data`);
        // Update order
        const shipping = captureData.purchase_units[0]?.shipping;
        const { payer } = captureData;
        const shippingData = shipping ? {
          city: shipping.address.admin_area_2,
          state: shipping.address.admin_area_1,
          country: shipping.address.country_code,
          postcode: shipping.address.postal_code,
          address_1: shipping.address.address_line_1,
          address_2: shipping.address.address_line_2 || "",
          last_name: shipping.name.full_name.split(" ").pop(),
          first_name: shipping.name.full_name.split(" ").shift(),
        } : {};
        const updatedOrderData = {
          ...order.orderData,
          billing: {
            ...shippingData,
            phone: payer.phone?.phone_number?.national_number || "",
            email: payer.email_address,
          },
          set_paid: true,
          shipping: shippingData,
        };
        console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPalV2 - Process Shipping & Billing Data`);

        console.time(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPalV2 - Update Order in DB`);
        const { updatedOrder, stats } = await updateOrderAfterPayPalCapture(
          order,
          captureData,
          updatedOrderData
        );
        const formattedStats = formatStats(stats);
        console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPalV2 - Update Order in DB`);

        // V2 CACHE LOGIC - Generate cache keys for success and draft  
        console.time(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPalV2 - V2 Cache Operations`);
        const successCacheKey = generateSuccessCacheKey(domain, meta_data, line_items, shipping_lines);
        const draftCacheKey = generateDraftCacheKey(domain, meta_data, line_items, shipping_lines);

        // Cache successful order for duplicate prevention
        const successCacheValue = {
          order_id: updatedOrder.id,
          transaction_id: transactionId,
          paypal_order_id: orderId,
          order_key: updatedOrder.order_key,
          created_at: Date.now(),
          total: order.orderData.total,
          line_items_count: line_items.length,
          line_items,
          user_session: {
            ip: getMetaDataValue(meta_data, 'ip'),
            ua_hash: getMetaDataValue(meta_data, 'UA')?.substring(0, 50)
          }
        };

        await setCache(successCacheKey, successCacheValue, [], SUCCESS_ORDER_TTL);

        // INVOICE SAFETY LAYER: Cache captured invoice to prevent future collisions
        const invoice_id = getMetaDataValue(meta_data, 'invoice_id');
        if (invoice_id) {
          const capturedInvoiceCacheKey = generateCapturedInvoiceCacheKey(domain, invoice_id);
          const capturedInvoiceValue = {
            order_id: updatedOrder.id,
            transaction_id: transactionId,
            paypal_order_id: orderId,
            order_key: updatedOrder.order_key,
            created_at: Date.now(),
            invoice_id: invoice_id
          };
          
          await setCache(capturedInvoiceCacheKey, capturedInvoiceValue, [], SUCCESS_ORDER_TTL);
          
          logger.info(`[V2] [${domain}] Invoice captured and cached for safety`, {
            invoiceId: invoice_id,
            capturedInvoiceCacheKey,
            orderId: updatedOrder.id,
            transactionId: transactionId,
            api_version: "v2"
          });
        }

        // Clear draft cache since order is now complete
        await clearCache(draftCacheKey);
        console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPalV2 - V2 Cache Operations`);

        console.time(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${updatedOrder.id}] handleCapturePayPalV2 - Log Transaction Success`);
        await TransactionLogger.logFinalOutcome({
          orderId: updatedOrder.id,
          eventType: "CAPTURE_SUCCESS",
          paypalOrderId: orderId,
          transactionId: captureDetails?.id,
          oldStatus: order.status,
          newStatus: "processing",
          paypalApiResponse: captureData,
          notes: `V2 Capture completed successfully. Amount: ${captureDetails?.amount?.value}`,
          order: order
        });

        // Log successful capture with all important IDs
        logger.info(`[V2] [${domain}] PayPal capture completed successfully - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${updatedOrder.id}`, {
          domain: order.domain,
          paypalOrderId: orderId,
          transactionId: transactionId,
          orderId: updatedOrder.id,
          orderKey: updatedOrder.order_key,
          amount: captureDetails?.amount?.value,
          currency: captureDetails?.amount?.currency_code,
          api_version: "v2",
          v2_optimizations: {
            early_duplicate_prevention: "enabled",
            draft_cache_cleared: "yes",
            success_cache_created: "yes"
          }
        });

        console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${updatedOrder.id}] handleCapturePayPalV2 - Log Transaction Success`);

        console.time(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${updatedOrder.id}] handleCapturePayPalV2 - Build Response`);
        // Build response
        const responseOrder = {
          id: updatedOrder.id,
          order_key: updatedOrder.order_key,
          domain: updatedOrder.domain,
          transaction_id: updatedOrder.transaction_id,
          orderData: {
            total: updatedOrder.orderData.total,
            sub_total: updatedOrder.orderData.sub_total,
            shipping_total: updatedOrder.orderData.shipping_total,
            billing: updatedOrder.orderData.billing,
            shipping: updatedOrder.orderData.shipping,
            line_items: updatedOrder.orderData.line_items,
            payment_method: updatedOrder.orderData.payment_method,
            payment_method_title: updatedOrder.orderData.payment_method_title,
            set_paid: updatedOrder.orderData.set_paid,
            invoice_id: updatedOrder.orderData.invoice_id,
            funding_source: updatedOrder.orderData.funding_source,
          },
          createdAt: updatedOrder.createdAt,
          updatedAt: updatedOrder.updatedAt,
        };
        console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${updatedOrder.id}] handleCapturePayPalV2 - Build Response`);

        console.timeEnd("handleCapturePayPalV2 - Total");
        // Return response immediately
        res.status(200).json(responseOrder);

        // Non-blocking async operations - execute after response is sent
        setImmediate(async () => {
          console.time(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${updatedOrder.id}] handleCapturePayPalV2 - Background - Email Queue`);
          // Queue order confirmation email
          if (!config.disableEmailSending) {
            try {
              await sendToQueue("EMAIL", "order_confirmation", {
                id: updatedOrder.id,
                order_key: updatedOrder.order_key,
                domain: updatedOrder.domain,
                transaction_id: updatedOrder.transaction_id,
                orderData: updatedOrder.orderData,
                status: updatedOrder.status,
                createdAt: updatedOrder.createdAt,
                updatedAt: updatedOrder.updatedAt,
              });
              logger.info(`[V2] [${domain}] Order confirmation email queued - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${updatedOrder.id}`, {
                orderId: updatedOrder.id,
                paypalOrderId: orderId,
                transactionId: transactionId,
                domain: order.domain,
                api_version: "v2"
              });
            } catch (emailError) {
              logger.error(`[V2] [${domain}] Failed to queue order confirmation email - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${updatedOrder.id}`, {
                error: emailError.message,
                stack: emailError.stack,
                orderId: updatedOrder.id,
                paypalOrderId: orderId,
                transactionId: transactionId,
                domain: order.domain,
                api_version: "v2"
              });
            }
          } else {
            logger.info(
              `[V2] [${domain}] Email sending is disabled. Skipping order confirmation email - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${updatedOrder.id}`,
              {
                orderId: updatedOrder.id,
                paypalOrderId: orderId,
                transactionId: transactionId,
                domain: order.domain,
                api_version: "v2"
              }
            );
          }
          console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${updatedOrder.id}] handleCapturePayPalV2 - Background - Email Queue`);

          console.time(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${updatedOrder.id}] handleCapturePayPalV2 - Background - TrueStore Queue`);
          // Send to TrueStore
          if (!config.disableTrueStoreSync) {
            try {
              await sendToQueue("TRUESTORE", "new_order", {
                order: updatedOrder,
                stats: formattedStats
              });
              logger.info(`[V2] [${domain}] Order queued for TrueStore - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${updatedOrder.id}`, {
                orderId: updatedOrder.id,
                orderKey: updatedOrder.order_key,
                domain: updatedOrder.domain,
                paypalOrderId: orderId,
                transactionId: transactionId,
                api_version: "v2"
              });
            } catch (trueStoreError) {
              logger.error(`[V2] [${domain}] Error queueing order for TrueStore - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${updatedOrder.id}`, {
                orderId: updatedOrder.id,
                orderKey: updatedOrder.order_key,
                domain: updatedOrder.domain,
                paypalOrderId: orderId,
                transactionId: transactionId,
                error: trueStoreError.message,
                stack: trueStoreError.stack,
                api_version: "v2"
              });
            }
          } else {
            logger.info(`[V2] [${domain}] TrueStore sync is disabled. Skipping order sync - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${updatedOrder.id}`, {
              orderId: updatedOrder.id,
              paypalOrderId: orderId,
              transactionId: transactionId,
              domain: order.domain,
              api_version: "v2"
            });
          }
          console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${updatedOrder.id}] handleCapturePayPalV2 - Background - TrueStore Queue`);
        });
        
        return;

      } else if (captureStatus === "PENDING") {
        const transactionId = captureDetails?.id;
        console.time(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPalV2 - Handle PENDING Status`);
        await order.update({
          status: "pending",
          transaction_id: captureDetails?.id || null,
          orderData: {
            ...order.orderData,
            set_paid: false,
            payment_status: "pending",
            capture_status: "PENDING",
            capture_reason: captureDetails?.status_details?.reason || "Under review"
          }
        });
        await TransactionLogger.logFinalOutcome({
          orderId: order.id,
          eventType: "CAPTURE_PENDING",
          paypalOrderId: orderId,
          transactionId: captureDetails?.id,
          oldStatus: order.status,
          newStatus: "pending",
          paypalApiResponse: captureData,
          notes: `V2 Capture pending: ${captureDetails?.status_details?.reason || 'Payment under review'}`,
          order: order
        });

        logger.info(`[V2] [${domain}] PayPal capture pending - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${order.id}`, {
          domain: order.domain,
          paypalOrderId: orderId,
          transactionId: transactionId,
          orderId: order.id,
          reason: captureDetails?.status_details?.reason || 'Payment under review',
          api_version: "v2"
        });

        console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPalV2 - Handle PENDING Status`);
        console.timeEnd("handleCapturePayPalV2 - Total");
        return res.status(202).json({
          id: order.id,
          status: "pending",
          message: "Payment is pending review",
          details: "You will be notified when payment is completed",
          capture_status: "PENDING",
          transaction_id: captureDetails?.id
        });
      } else if (captureStatus === "DECLINED") {
        const transactionId = captureDetails?.id;
        console.time(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPalV2 - Handle DECLINED Status`);
        await order.update({
          status: "failed",
          orderData: {
            ...order.orderData,
            set_paid: false,
            payment_status: "failed",
            capture_status: "DECLINED"
          }
        });
        await TransactionLogger.logFinalOutcome({
          orderId: order.id,
          eventType: "CAPTURE_FAILED",
          paypalOrderId: orderId,
          transactionId: captureDetails?.id,
          oldStatus: order.status,
          newStatus: "failed",
          paypalApiResponse: captureData,
          notes: `V2 Capture declined`,
          order: order
        });

        logger.error(`[V2] [${domain}] PayPal capture declined - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${order.id}`, {
          domain: order.domain,
          paypalOrderId: orderId,
          transactionId: transactionId,
          orderId: order.id,
          api_version: "v2"
        });

        console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPalV2 - Handle DECLINED Status`);
        console.timeEnd("handleCapturePayPalV2 - Total");
        return res.status(400).json({
          error: "Payment declined",
          details: "Payment was declined. Try a different payment method.",
          code: "CAPTURE_DECLINED"
        });
      } else {
        const transactionId = captureDetails?.id;
        console.time(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPalV2 - Handle Unknown Status`);
        await TransactionLogger.logFinalOutcome({
          orderId: order.id,
          eventType: "CAPTURE_ERROR",
          paypalOrderId: orderId,
          transactionId: captureDetails?.id,
          oldStatus: order.status,
          newStatus: order.status,
          paypalApiResponse: captureData,
          notes: `V2 Unknown capture status: ${captureStatus}`,
          order: order
        });

        logger.error(`[V2] [${domain}] Unknown PayPal capture status - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${order.id}`, {
          domain: order.domain,
          paypalOrderId: orderId,
          transactionId: transactionId,
          orderId: order.id,
          captureStatus: captureStatus,
          api_version: "v2"
        });

        console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPalV2 - Handle Unknown Status`);
        console.timeEnd("handleCapturePayPalV2 - Total");
        return res.status(500).json({
          error: "Unknown capture status",
          details: `PayPal returned unexpected status: ${captureStatus}`,
          code: "UNKNOWN_STATUS"
        });
      }
    } catch (error) {
      console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] handleCapturePayPalV2 - PayPal Capture API Call`);
      console.time(`[V2] [${domain}] [PayPal ID: ${orderId}] handleCapturePayPalV2 - Handle Capture Error`);
      const paypalError = error.paypalError;
      const orderStatus = paypalError?.canRetry ? "pending" : "failed";
      await order.update({
        status: orderStatus,
        orderData: {
          ...order.orderData,
          payment_status: orderStatus,
          payment_error: paypalError
        }
      });
      await TransactionLogger.logFinalOutcome({
        orderId: order.id,
        eventType: "CAPTURE_ERROR",
        paypalOrderId: req.params.orderId,
        notes: `V2 Capture error: ${error.message}`,
        paypalApiResponse: paypalError || null,
        order: order
      });

      logger.error(`[V2] [${domain}] PayPal capture error - PayPal ID: ${orderId}, Order ID: ${order.id}`, {
        domain: order.domain,
        paypalOrderId: orderId,
        orderId: order.id,
        error: error.message,
        paypalError: paypalError,
        api_version: "v2"
      });

      console.timeEnd(`[V2] [${domain}] [PayPal ID: ${orderId}] handleCapturePayPalV2 - Handle Capture Error`);
      console.timeEnd("handleCapturePayPalV2 - Total");
      return res.status(paypalError?.status || 500).json({
        error: paypalError?.error || "Internal server error",
        details: paypalError?.details || "An unexpected error occurred",
        code: paypalError?.code
      });
    }
  } catch (error) {
    console.timeEnd("handleCapturePayPalV2 - Total");
    const domain = order?.domain || 'unknown';
    const paypalOrderId = req.params?.orderId || 'unknown';
    const dbOrderId = order?.id || 'unknown';
    logger.error(`[V2] [${domain}] Error processing PayPal approval - PayPal ID: ${paypalOrderId}, Order ID: ${dbOrderId}:`, error);
    res.status(500).json({
      error: "Internal server error",
      details: "An unexpected error occurred while processing the payment"
    });
  }
};

export {
  createDraftOrderForPayPalV2,
  handleCapturePayPalV2
}; 