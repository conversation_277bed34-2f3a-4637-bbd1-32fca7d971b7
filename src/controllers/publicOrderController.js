import Order from '../models/order.js';
import logger from '../utils/logger.js';

/**
 * Public API controller to get order information by order key
 * This endpoint is used by external systems to check order status
 * Domain validation is handled by middleware before reaching this controller
 */
export const getOrderByKey = async (req, res) => {
  const startTime = Date.now();
  let orderKey, orderId;
  
  try {
    orderKey = req.query.key;
    orderId = req.params.orderId;
    const requestDomain = req.get('x-domain');
    
    if (!orderKey) {
      return res.status(400).json({
        error: 'Bad request',
        details: 'Order key parameter is required'
      });
    }

    let order;
    
    if (orderId) {
      order = await Order.findOne({
        where: { 
          id: orderId
        }
      });
      
      if (order) {
        const orderKeyMatches = order.order_key === orderKey;
        const emailMatches = order.orderData?.billing?.email === orderKey;
        
        if (!orderKeyMatches && !emailMatches) {
          order = null;
        }
      }
    } else {
      order = await Order.findOne({
        where: { 
          order_key: orderKey 
        }
      });
    }

    if (!order) {
      logger.warn('Order not found in public controller', {
        orderKey,
        orderId,
        requestDomain,
        ip: req.ip
      });
      
      return res.status(404).json({
        error: 'Order not found',
        details: 'Order information is not available'
      });
    }

    const publicOrderInfo = {
      id: order.id,
      order_key: order.order_key,
      domain: order.domain,
      status: order.status,
      tracking_id: order.tracking_id,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      total: order.orderData?.total,
      billing: order.orderData?.billing ? {
        city: order.orderData.billing.city,
        email: order.orderData.billing.email,
        phone: order.orderData.billing.phone,
        state: order.orderData.billing.state,
        country: order.orderData.billing.country,
        postcode: order.orderData.billing.postcode,
        address_1: order.orderData.billing.address_1,
        address_2: order.orderData.billing.address_2,
        last_name: order.orderData.billing.last_name,
        first_name: order.orderData.billing.first_name
      } : null,
      currency: order.orderData?.currency || 'USD',
      shipping: order.orderData?.shipping ? {
        city: order.orderData.shipping.city,
        state: order.orderData.shipping.state,
        country: order.orderData.shipping.country,
        postcode: order.orderData.shipping.postcode,
        address_1: order.orderData.shipping.address_1,
        address_2: order.orderData.shipping.address_2,
        last_name: order.orderData.shipping.last_name,
        first_name: order.orderData.shipping.first_name
      } : null,
      sub_total: order.orderData?.sub_total,
      tip_total: order.orderData?.tip_total || 0,
      line_items: order.orderData?.line_items?.map(item => ({
        sku: item.sku,
        name: item.name,
        image: item.image,
        price: item.price,
        total: item.total,
        quantity: item.quantity,
        meta_data: item.meta_data,
        product_id: item.product_id,
        product_link: item.product_link,
        variation_id: item.variation_id
      })) || [],
      discount_total: order.orderData?.discount_total || 0,
      shipping_lines: order.orderData?.shipping_lines || [],
      shipping_total: order.orderData?.shipping_total
    };

    const duration = Date.now() - startTime;
    
    logger.info('Public order retrieved successfully', {
      orderKey,
      orderId,
      domain: requestDomain,
      orderIdFound: order.id,
      duration: `${duration}ms`,
      ip: req.ip
    });

    res.json({
      success: true,
      data: publicOrderInfo
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    
    logger.error('Error in public order controller', {
      message: error.message,
      stack: error.stack,
      orderKey,
      orderId,
      requestDomain: req.get('x-domain'),
      duration: `${duration}ms`,
      ip: req.ip
    });
    
    res.status(500).json({
      error: 'Internal server error',
      details: 'An error occurred while retrieving order information'
    });
  }
};
