import { sendNewOrderToTrueStore } from "../services/trueStoreService.js";

const testOrder = {
  id: 994865,
  order_key: "1738206323800vPmIrciB",
  domain: "1siteclone.com",
  transaction_id: "3VH99307RN1778716",
  paypal_client_id:
    "AfxRzHiEPxzUBUg4G4ZveG6MUi_fEjPLcc3-P5Qq8BGpcs1Umf6rkI9r8GU2iJNKHw93AJ24UoOZc0oq",
  orderData: {
    total: 59.98,
    billing: {
      city: "walnut",
      email: "<EMAIL>",
      phone: "",
      state: "CA",
      country: "US",
      postcode: "91789",
      address_1: "844 Bridgewater lane",
      address_2: "",
      last_name: "esmail",
      first_name: "salina",
    },
    set_paid: true,
    shipping: {
      city: "walnut",
      state: "CA",
      country: "US",
      postcode: "91789",
      address_1: "844 Bridgewater lane",
      address_2: "",
      last_name: "esmail",
      first_name: "salina",
    },
    meta_data: [
      {
        key: "ip",
        value: "*************",
      },
      {
        key: "UA",
        value:
          "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/22B91 [FBAN/FBIOS;FBAV/496.**********;FBBV/684558567;FBDV/iPhone17,1;FBMD/iPhone;FBSN/iOS;FBSV/18.1.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/691333855;IABMV/1]",
      },
      {
        key: "invoice_id",
        value: "1siteclone-3eo-20250129190523645",
      },
      {
        key: "funding_source",
        value: "paypal",
      },
    ],
    sub_total: 59.98,
    tip_total: 0,
    invoice_id: "1siteclone-3eo-20250129190523645",
    line_items: [
      {
        sku: "20241218-80ts-05-02-7O2P62",
        name: "Multi-Level Smart Drawer Organizer-Type 1 Row with 2-Tier(9.84*5.91*1.77in) - 🔥buy-more-save-more🔥 Buy 2 Get 10% Discount (F",
        image:
          "https://admin.rarehotdeals.com/wp-content/uploads/2024/12/05ec4cce4230622ca69ccfa0719a11abc864d17f-900.jpeg",
        price: 59.98,
        total: "59.98",
        quantity: 1,
        meta_data: [
          {
            id: 0,
            name: "Type",
            option: "1 Row with 2-Tier(9.84*5.91*1.77in)",
          },
          {
            id: 0,
            name: "🔥buy-more-save-more🔥",
            option: "Buy 2 Get 10% Discount (Free Shipping✈️)",
          },
        ],
        product_id: 31241,
        product_link: "https://1siteclone.com/product/drawerorganizer80ts",
        variation_id: 308006,
      },
    ],
    discount_total: 0,
    funding_source: "paypal",
    payment_method: "ppcp-gateway",
    shipping_lines: [
      {
        total: "0",
        method_id: "flat_rate",
      },
    ],
    shipping_total: 0,
    payment_method_title: "paypal",
  },
  status: "processing",
  tracking_id: null,
  port: null,
  paypal_order_id: "59K51428TD0848504",
  paypal_checked_at: "2025-01-29 20:06:31",
  createdAt: "2025-01-29 20:05:24",
  updatedAt: "2025-01-29 20:06:31",
};

const stats = {};

async function testSendOrder() {
  try {
    console.log("Starting to send test order...");

    // Modify trueStoreService to return the form data for debugging
    const results = await sendNewOrderToTrueStore(testOrder, stats);

    // Log the full response
    console.log("\nFull Response:");
    console.log("Results:", JSON.stringify(results, null, 2));
  } catch (error) {
    console.error("\nDetailed Error Information:");
    console.error("Error Message:", error.message);
    if (error.response) {
      console.error("Response Status:", error.response.status);
      console.error("Response Headers:", error.response.headers);
      console.error("Response Data:", await error.response.text());
    }
    console.error("Stack Trace:", error.stack);
  }
}

// Add debug mode to see request data
process.env.DEBUG = "true";

testSendOrder();
