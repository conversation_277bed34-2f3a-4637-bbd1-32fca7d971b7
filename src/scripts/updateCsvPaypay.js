import fs from 'fs/promises';
import path from 'path';
import { createObjectCsvWriter } from 'csv-writer';
import { parse } from 'csv-parse/sync';

async function updateCsvPaypal(inputDir) {
  const allDataFile = path.join(inputDir, 'all_transactions.json');
  const allOrdersFile = path.join(inputDir, 'all_orders.json');
  const allShippingFile = path.join(inputDir, 'all_shipping.json');
  const csvFile = path.join(inputDir, 'all_transactions.csv');
  const updatedCsvFile = path.join(inputDir, 'updated_transactions.csv');
  const errorCsvFile = path.join(inputDir, 'error_transactions.csv');
  const shippedCsvFile = path.join(inputDir, 'shipped_transactions.csv');
  const errorLogFile = path.join(inputDir, 'error_log.txt');

  async function logError(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `${timestamp}: ${message}\n`;
    await fs.appendFile(errorLogFile, logMessage);
  }

  // Read all_data.json, all_orders.json and all_shipping.json
  const allData = JSON.parse(await fs.readFile(allDataFile, 'utf8'));
  const allOrders = JSON.parse(await fs.readFile(allOrdersFile, 'utf8'));
  const allShipping = JSON.parse(await fs.readFile(allShippingFile, 'utf8'));

  let records = [];
  try {
    // Try to read existing CSV file
    const csvContent = await fs.readFile(csvFile, 'utf8');
    records = parse(csvContent, { 
      columns: true, 
      skip_empty_lines: true,
      bom: true // Handle BOM if present in the input file
    });
  } catch (error) {
    if (error.code === 'ENOENT') {
      console.log('CSV file not found. Creating a new one.');
      records = [];
    } else {
      throw error;
    }
  }

  // Define headers based on the full list of columns
  const headers = [
    { id: 'Date', title: 'Date' },
    { id: 'Time', title: 'Time' },
    { id: 'TimeZone', title: 'TimeZone' },
    { id: 'Name', title: 'Name' },
    { id: 'Type', title: 'Type' },
    { id: 'Status', title: 'Status' },
    { id: 'Currency', title: 'Currency' },
    { id: 'Gross', title: 'Gross' },
    { id: 'Fee', title: 'Fee' },
    { id: 'Net', title: 'Net' },
    { id: 'From Email Address', title: 'From Email Address' },
    { id: 'To Email Address', title: 'To Email Address' },
    { id: 'Transaction ID', title: 'Transaction ID' },
    { id: 'Shipping Address', title: 'Shipping Address' },
    { id: 'Address Status', title: 'Address Status' },
    { id: 'Item Title', title: 'Item Title' },
    { id: 'Item ID', title: 'Item ID' },
    { id: 'Shipping and Handling Amount', title: 'Shipping and Handling Amount' },
    { id: 'Insurance Amount', title: 'Insurance Amount' },
    { id: 'Sales Tax', title: 'Sales Tax' },
    { id: 'Option 1 Name', title: 'Option 1 Name' },
    { id: 'Option 1 Value', title: 'Option 1 Value' },
    { id: 'Option 2 Name', title: 'Option 2 Name' },
    { id: 'Option 2 Value', title: 'Option 2 Value' },
    { id: 'Reference Txn ID', title: 'Reference Txn ID' },
    { id: 'Invoice Number', title: 'Invoice Number' },
    { id: 'Custom Number', title: 'Custom Number' },
    { id: 'Quantity', title: 'Quantity' },
    { id: 'Receipt ID', title: 'Receipt ID' },
    { id: 'Balance', title: 'Balance' },
    { id: 'Address Line 1', title: 'Address Line 1' },
    { id: 'Address Line 2/District/Neighborhood', title: 'Address Line 2/District/Neighborhood' },
    { id: 'Town/City', title: 'Town/City' },
    { id: 'State/Province/Region/County/Territory/Prefecture/Republic', title: 'State/Province/Region/County/Territory/Prefecture/Republic' },
    { id: 'Zip/Postal Code', title: 'Zip/Postal Code' },
    { id: 'Country', title: 'Country' },
    { id: 'Contact Phone Number', title: 'Contact Phone Number' },
    { id: 'Subject', title: 'Subject' },
    { id: 'Note', title: 'Note' },
    { id: 'Country Code', title: 'Country Code' },
    { id: 'Balance Impact', title: 'Balance Impact' },
    { id: 'Order ID', title: 'Order ID' },
    { id: 'order_status', title: 'Order Status' },
    { id: 'tracking_number', title: 'Tracking Number' },
    { id: 'tracking_carrier', title: 'Tracking Carrier' },
  ];

  // Create CSV writers for normal, error, and shipped transactions
  const csvWriter = createObjectCsvWriter({
    path: updatedCsvFile,
    header: headers,
    encoding: 'utf8',
    append: false,
    bom: true
  });

  const errorCsvWriter = createObjectCsvWriter({
    path: errorCsvFile,
    header: headers,
    encoding: 'utf8',
    append: false,
    bom: true
  });

  const shippedCsvWriter = createObjectCsvWriter({
    path: shippedCsvFile,
    header: headers,
    encoding: 'utf8',
    append: false,
    bom: true
  });

  const updatedRecords = [];
  const errorRecords = [];
  const shippedRecords = [];

  for (const transaction_id of Object.keys(allData)) {
    const existingRecord = records.find(r => r['Transaction ID'] === transaction_id) || {};
    let updatedRecord = { ...existingRecord, 'Transaction ID': transaction_id };

    const transactionData = allData[transaction_id];
    
    if (transactionData) {
      // Check if the transaction has a dispute
      if (transactionData.has_dispute === true) {
        const errorMessage = `Transaction ${transaction_id} has a dispute.`;
        console.error(errorMessage);
        await logError(errorMessage);
        continue; // Skip further processing for this transaction
      }

      const orderId = transactionData.order_id;
      let orderData = null;

      if (orderId && allOrders[orderId] && allOrders[orderId].success) {
        orderData = allOrders[orderId].data;
      }

      let isError = false;
      let errorReason = '';
      let hasShipping = false;

      // Process order data if available
      if (orderData) {
        const purchaseUnit = orderData.purchase_units && orderData.purchase_units[0];
        const capture = purchaseUnit?.payments?.captures?.[0];
        const item = purchaseUnit?.items?.[0];

        if (purchaseUnit && capture && item) {
          const paypalFee = capture.seller_receivable_breakdown?.paypal_fee?.value;
          const grossAmount = capture.seller_receivable_breakdown?.gross_amount?.value;
          
          // Sử dụng create_time thay vì update_time
          const createTime = new Date(capture.create_time);
          
          updatedRecord = {
            ...updatedRecord,
            'Date': createTime.toISOString().split('T')[0],
            'Time': createTime.toISOString().split('T')[1].split('.')[0],
            'TimeZone': 'Z',
            'Name': purchaseUnit.shipping?.name?.full_name || '',
            'Type': 'Payment',
            'Status': capture.status || '',
            'Currency': capture.amount?.currency_code || '',
            'Gross': grossAmount || '',
            'Fee': paypalFee || '',
            'Net': capture.seller_receivable_breakdown?.net_amount?.value || '',
            'From Email Address': orderData.payer?.email_address || '',
            'To Email Address': purchaseUnit.payee?.email_address || '',
            'Transaction ID': capture.id || '',
            'Shipping Address': `${purchaseUnit.shipping?.address?.address_line_1 || ''}, ${purchaseUnit.shipping?.address?.admin_area_2 || ''}, ${purchaseUnit.shipping?.address?.admin_area_1 || ''}, ${purchaseUnit.shipping?.address?.postal_code || ''}, ${purchaseUnit.shipping?.address?.country_code || ''}`,
            'Address Status': '',
            'Item Title': item.name || '',
            'Item ID': item.sku || '',
            'Shipping and Handling Amount': purchaseUnit.amount?.breakdown?.shipping?.value || '',
            'Insurance Amount': purchaseUnit.amount?.breakdown?.insurance?.value || '',
            'Sales Tax': item.tax?.value || '',
            'Option 1 Name': '',
            'Option 1 Value': '',
            'Option 2 Name': '',
            'Option 2 Value': '',
            'Reference Txn ID': orderData.id || '',
            'Invoice Number': capture.invoice_id || '',
            'Custom Number': capture.custom_id || '',
            'Quantity': item.quantity || '',
            'Receipt ID': '',
            'Balance': '',
            'Address Line 1': purchaseUnit.shipping?.address?.address_line_1 || '',
            'Address Line 2/District/Neighborhood': '',
            'Town/City': purchaseUnit.shipping?.address?.admin_area_2 || '',
            'State/Province/Region/County/Territory/Prefecture/Republic': purchaseUnit.shipping?.address?.admin_area_1 || '',
            'Zip/Postal Code': purchaseUnit.shipping?.address?.postal_code || '',
            'Country': purchaseUnit.shipping?.address?.country_code || '',
            'Contact Phone Number': orderData.payer?.phone?.phone_number?.national_number || '',
            'Subject': '',
            'Note': purchaseUnit.description || '',
            'Country Code': orderData.payer?.address?.country_code || '',
            'Balance Impact': ''
          };

          // Check if it's an error transaction
          if (paypalFee === '8.00' || paypalFee === '16.00') {
            isError = true;
            errorReason = `PayPal fee is ${paypalFee}`;
          }
        } else {
          isError = true;
          errorReason = 'Incomplete order data';
          if (!purchaseUnit) errorReason += ' (missing purchase unit)';
          if (!capture) errorReason += ' (missing capture)';
          if (!item) errorReason += ' (missing item)';
        }
      } else {
        isError = true;
        errorReason = 'No order data available';
      }

      // Check for shipping information
      if (transactionData.tracking_number) {
        hasShipping = true;
        updatedRecord.tracking_number = transactionData.tracking_number;
        updatedRecord.tracking_carrier = transactionData.tracking_carrier || '';
      } else if (allShipping[transaction_id] && allShipping[transaction_id].success) {
        hasShipping = true;
        const shippingData = allShipping[transaction_id].data;
        updatedRecord.tracking_number = shippingData.tracking_number || '';
        updatedRecord.tracking_carrier = shippingData.carrier || '';
      }

      // Categorize the transaction
      if (hasShipping) {
        shippedRecords.push(updatedRecord);
      } else if (isError) {
        errorRecords.push(updatedRecord);
        console.log(`Error in transaction ${transaction_id}: ${errorReason}`);
      } else {
        updatedRecords.push(updatedRecord);
      }
    } else {
      console.log(`Error in transaction ${transaction_id}: No transaction data available`);
      errorRecords.push(updatedRecord);
    }
  }

  // Write updated records to new CSV file
  if (updatedRecords.length > 0) {
    await csvWriter.writeRecords(updatedRecords);
    console.log(`Updated CSV file has been written to ${updatedCsvFile} with UTF-8 encoding`);
  } else {
    console.log('No updated transactions to write');
  }

  // Write error records to separate CSV file
  if (errorRecords.length > 0) {
    await errorCsvWriter.writeRecords(errorRecords);
    console.log(`Error transactions CSV file has been written to ${errorCsvFile} with UTF-8 encoding`);
  } else {
    console.log('No error transactions found');
  }

  // Write shipped records to separate CSV file
  if (shippedRecords.length > 0) {
    await shippedCsvWriter.writeRecords(shippedRecords);
    console.log(`Shipped transactions CSV file has been written to ${shippedCsvFile} with UTF-8 encoding`);
  } else {
    console.log('No shipped transactions found');
  }

  // Log summary
  const totalTransactions = Object.keys(allData).length;
  console.log('\nSummary:');
  console.log(`Total transactions processed: ${totalTransactions}`);
  console.log(`Error transactions: ${errorRecords.length}`);
  console.log(`Transactions with shipping: ${shippedRecords.length}`);
  console.log(`Normal transactions: ${updatedRecords.length}`);
  console.log(`Total (should match total transactions): ${errorRecords.length + shippedRecords.length + updatedRecords.length}`);

  // Log error reasons summary
  const errorReasons = {};
  errorRecords.forEach(record => {
    const reason = record.errorReason || 'Unknown';
    errorReasons[reason] = (errorReasons[reason] || 0) + 1;
  });
  console.log('\nError Reasons Summary:');
  Object.entries(errorReasons).forEach(([reason, count]) => {
    console.log(`${reason}: ${count}`);
  });
}

// Helper function to safely get nested values
function getNestedValue(obj, path, defaultValue = '') {
  return path.split('.').reduce((acc, part) => acc && acc[part], obj) || defaultValue;
}

// Usage
const inputDir = path.join(process.cwd(), 'data');
updateCsvPaypal(inputDir).catch(error => {
  console.error('Error updating CSV:', error);
});
