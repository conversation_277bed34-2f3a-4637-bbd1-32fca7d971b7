import fs from 'fs/promises';
import path from 'path';
import { createReadStream, existsSync } from 'fs';
import readline from 'readline';
import { createObjectCsvWriter } from 'csv-writer';
import axios from 'axios';
import dotenv from 'dotenv';
import paypalAccounts from "../config/paypal.json" assert { type: "json" };

dotenv.config();

const PAYPAL_API_BASE = "https://api.paypal.com";

async function getAccessToken(clientId, clientSecret) {
  const auth = Buffer.from(`${clientId}:${clientSecret}`).toString("base64");
  const response = await axios.post(
    `${PAYPAL_API_BASE}/v1/oauth2/token`,
    "grant_type=client_credentials",
    {
      headers: {
        Authorization: `Basic ${auth}`,
        "Content-Type": "application/x-www-form-urlencoded",
      },
    }
  );
  return response.data.access_token;
}

async function getPaymentDetails(transactionId, accessToken, account) {
  try {
    const response = await axios.get(
      `${PAYPAL_API_BASE}/v2/payments/captures/${transactionId}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );
    return { success: true, data: response.data };
  } catch (error) {
    const errorMessage = `Error fetching payment details for ${transactionId}: ${error.message}`;
    console.error(errorMessage);
    await logError(errorMessage, account);
    return { success: false, error: errorMessage };
  }
}

async function getOrderDetails(orderId, accessToken, account) {
  try {
    const response = await axios.get(
      `${PAYPAL_API_BASE}/v2/checkout/orders/${orderId}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );
    return { success: true, data: response.data };
  } catch (error) {
    const errorMessage = `Error fetching order details for ${orderId}: ${error.message}`;
    console.error(errorMessage);
    await logError(errorMessage, account);
    return { success: false, error: errorMessage };
  }
}

async function logError(message, account) {
  const logDir = path.join(process.cwd(), 'logs', account);
  const logFile = path.join(logDir, 'error.log');
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp}: ${message}\n`;

  try {
    await fs.mkdir(logDir, { recursive: true });
    await fs.appendFile(logFile, logMessage);
  } catch (error) {
    console.error('Failed to write to error log:', error);
  }
}

async function getShippingDetails(transactionId, accessToken, account) {
  try {
    const response = await axios.get(
      `${PAYPAL_API_BASE}/v1/shipping/trackers`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        params: {
          transaction_id: transactionId
        }
      }
    );

    if (response.data && response.data.trackers && response.data.trackers.length > 0) {
      return { success: true, data: response.data.trackers[0] };
    } else {
      console.log(`No shipping details found for transaction ${transactionId}`);
      return { success: false, error: 'No shipping details found' };
    }
  } catch (error) {
    const errorMessage = `Error fetching shipping details for ${transactionId}: ${error.message}`;
    console.error(errorMessage);
    await logError(errorMessage, account);
    return { success: false, error: errorMessage };
  }
}

async function getDisputeDetails(transactionId, accessToken, account) {
  try {
    const response = await axios.get(
      `${PAYPAL_API_BASE}/v1/customer/disputes`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        params: {
          disputed_transaction_id: transactionId
        }
      }
    );

    if (response.data && response.data.items && response.data.items.length > 0) {
      return { success: true, data: response.data.items[0] };
    } else {
      console.log(`No dispute found for transaction ${transactionId}`);
      return { success: false, error: 'No dispute found' };
    }
  } catch (error) {
    const errorMessage = `Error fetching dispute details for ${transactionId}: ${error.message}`;
    console.error(errorMessage);
    await logError(errorMessage, account);
    return { success: false, error: errorMessage };
  }
}

async function processTransactions(inputDir) {
  try {
    await fs.mkdir(inputDir, { recursive: true });
    console.log(`Created directory: ${inputDir}`);
  } catch (error) {
    if (error.code !== 'EEXIST') {
      await logError(`Error creating directory ${inputDir}: ${error.message}`, 'general');
      console.error(`Error creating directory ${inputDir}:`, error);
      return;
    }
  }

  const allDataFile = path.join(inputDir, 'all_transactions.json');
  const allPaymentsFile = path.join(inputDir, 'all_payments.json');
  const allOrdersFile = path.join(inputDir, 'all_orders.json');
  const allShippingFile = path.join(inputDir, 'all_shipping.json');
  const allDisputesFile = path.join(inputDir, 'all_disputes.json');

  let allData = {};
  let allPayments = {};
  let allOrders = {};
  let allShipping = {};
  let allDisputes = {};

  // Load existing data if files exist
  if (existsSync(allDataFile)) {
    allData = JSON.parse(await fs.readFile(allDataFile, 'utf8'));
  }
  if (existsSync(allPaymentsFile)) {
    allPayments = JSON.parse(await fs.readFile(allPaymentsFile, 'utf8'));
  }
  if (existsSync(allOrdersFile)) {
    allOrders = JSON.parse(await fs.readFile(allOrdersFile, 'utf8'));
  }
  if (existsSync(allShippingFile)) {
    allShipping = JSON.parse(await fs.readFile(allShippingFile, 'utf8'));
  }
  if (existsSync(allDisputesFile)) {
    allDisputes = JSON.parse(await fs.readFile(allDisputesFile, 'utf8'));
  }

  const accessTokens = {};
  for (const [key, account] of Object.entries(paypalAccounts)) {
    accessTokens[key] = await getAccessToken(account.client_id, account.secret_key);
  }

  const files = await fs.readdir(inputDir);
  for (const file of files) {
    if (path.extname(file) === '.txt') {
      const filePath = path.join(inputDir, file);
      const paypalAccount = path.parse(file).name;
      
      const fileStream = createReadStream(filePath);
      const rl = readline.createInterface({
        input: fileStream,
        crlfDelay: Infinity
      });

      for await (const line of rl) {
        if (line.trim() === 'Transaction ID') continue; // Skip header if present

        const transactionId = line.trim();

        let paymentResult;
        let orderResult;
        let shippingResult;
        let disputeResult;

        // Use existing payment data if available
        if (allPayments[transactionId]) {
          paymentResult = allPayments[transactionId];
        } else {
          paymentResult = await getPaymentDetails(transactionId, accessTokens[paypalAccount], paypalAccount);
          allPayments[transactionId] = paymentResult;
          await fs.writeFile(allPaymentsFile, JSON.stringify(allPayments, null, 2));
        }

        if (paymentResult.success && 
            paymentResult.data.supplementary_data && 
            paymentResult.data.supplementary_data.related_ids && 
            paymentResult.data.supplementary_data.related_ids.order_id) {
          const orderId = paymentResult.data.supplementary_data.related_ids.order_id;
          // Use existing order data if available
          if (allOrders[orderId]) {
            orderResult = allOrders[orderId];
          } else {
            orderResult = await getOrderDetails(orderId, accessTokens[paypalAccount], paypalAccount);
            allOrders[orderId] = orderResult;
            await fs.writeFile(allOrdersFile, JSON.stringify(allOrders, null, 2));
          }
        } else {
          orderResult = { success: false, error: 'Order details not fetched' };
        }

        // Use existing shipping data if available, otherwise fetch new data
        if (allShipping[transactionId]) {
          shippingResult = allShipping[transactionId];
        } else {
          shippingResult = await getShippingDetails(transactionId, accessTokens[paypalAccount], paypalAccount);
          allShipping[transactionId] = shippingResult;
          await fs.writeFile(allShippingFile, JSON.stringify(allShipping, null, 2));
        }

        // Use existing dispute data if available, otherwise fetch new data
        if (allDisputes[transactionId]) {
          disputeResult = allDisputes[transactionId];
        } else {
          disputeResult = await getDisputeDetails(transactionId, accessTokens[paypalAccount], paypalAccount);
          allDisputes[transactionId] = disputeResult;
          await fs.writeFile(allDisputesFile, JSON.stringify(allDisputes, null, 2));
        }

        // Update allData
        allData[transactionId] = {
          paypal_account: paypalAccount,
          payment_id: transactionId,
          order_id: orderResult.success ? orderResult.data.id : null,
          tracking_number: shippingResult.success ? shippingResult.data.tracking_number : null,
          tracking_carrier: shippingResult.success ? shippingResult.data.carrier : null,
          has_dispute: disputeResult.success,
          dispute_status: disputeResult.success ? disputeResult.data.status : null
        };

        // Write updated data to file
        await fs.writeFile(allDataFile, JSON.stringify(allData, null, 2));

        console.log(`Processed transaction ${transactionId} with tracking number: ${allData[transactionId].tracking_number || 'Not available'} and dispute status: ${allData[transactionId].dispute_status || 'No dispute'}`);
      }
    }
  }

  // Write CSV file
//   const csvWriter = createObjectCsvWriter({
//     path: path.join(inputDir, 'all_transactions.csv'),
//     header: [
//       {id: 'transaction_id', title: 'Transaction ID'},
//       {id: 'paypal_account', title: 'PayPal Account'},
//       {id: 'status', title: 'Status'},
//       {id: 'create_time', title: 'Create Time'},
//       {id: 'update_time', title: 'Update Time'},
//       {id: 'amount', title: 'Amount'},
//       {id: 'currency', title: 'Currency'},
//       {id: 'order_id', title: 'Order ID'},
//       // Add more fields as needed
//     ]
//   });

//   const csvRecords = Object.entries(allData).map(([transactionId, data]) => {
//     const payment = allPayments[transactionId];
//     const order = data.order_id ? allOrders[data.order_id] : null;
//     return {
//       transaction_id: transactionId,
//       paypal_account: data.paypal_account,
//       status: payment.success ? payment.data.status : 'Error',
//       create_time: payment.success ? payment.data.create_time : '',
//       update_time: payment.success ? payment.data.update_time : '',
//       amount: payment.success ? payment.data.amount.value : '',
//       currency: payment.success ? payment.data.amount.currency_code : '',
//       order_id: data.order_id || '',
//       // Add more fields as needed
//     };
//   });

//   await csvWriter.writeRecords(csvRecords);
//   console.log('CSV file has been written');
}

// Usage
const inputDir = path.join(process.cwd(), 'data');
processTransactions(inputDir).catch(async (error) => {
  console.error(error);
  await logError(`Error in processTransactions: ${error.message}`, 'general');
});
