#!/usr/bin/env node

import { getCache, redisClient } from '../config/cache.js';
import paypalConfig from '../config/paypal.json' with { type: "json" };

// Monitor PayPal token cache performance
async function monitorTokens() {
  console.log('🔍 PayPal Token Cache Monitor');
  console.log('============================');
  
  const clientIds = Object.values(paypalConfig).map(config => config.client_id);
  
  for (const clientId of clientIds) {
    const cacheKey = `paypal_access_token_${clientId}`;
    const lockKey = `paypal_token_lock_${clientId}`;
    
    try {
      const startTime = Date.now();
      const token = await getCache(cacheKey);
      const lock = await getCache(lockKey);
      const duration = Date.now() - startTime;
      
      const status = token ? '✅ CACHED' : '❌ MISSING';
      const lockStatus = lock ? `🔒 LOCKED (PID: ${lock})` : '🔓 UNLOCKED';
      
      console.log(`${clientId}: ${status} | ${lockStatus} | Query: ${duration}ms`);
      
      if (duration > 50) {
        console.warn(`  ⚠️  Slow cache query detected: ${duration}ms`);
      }
      
    } catch (error) {
      console.error(`❌ Error checking ${clientId}:`, error.message);
    }
  }
  
  // Redis connection status
  if (redisClient) {
    try {
      const info = await redisClient.info('memory');
      const memoryMatch = info.match(/used_memory_human:(.+)/);
      const memory = memoryMatch ? memoryMatch[1].trim() : 'Unknown';
      
      const connectedClients = await redisClient.client('list');
      const clientCount = connectedClients.split('\n').length - 1;
      
      console.log('\n📊 Redis Status:');
      console.log(`   Memory Usage: ${memory}`);
      console.log(`   Connected Clients: ${clientCount}`);
      console.log(`   Connection Status: ${redisClient.status}`);
      
    } catch (error) {
      console.error('❌ Redis status check failed:', error.message);
    }
  }
  
  console.log('\n' + '='.repeat(50));
}

// Monitor token creation patterns
async function analyzeTokenPatterns() {
  console.log('\n🔬 Token Creation Pattern Analysis');
  console.log('==================================');
  
  if (!redisClient) {
    console.log('❌ Redis not available for pattern analysis');
    return;
  }
  
  try {
    // Get all PayPal related keys
    const keys = await redisClient.keys('paypal_*');
    const tokenKeys = keys.filter(key => key.includes('access_token'));
    const lockKeys = keys.filter(key => key.includes('lock'));
    
    console.log(`📈 Total PayPal cache keys: ${keys.length}`);
    console.log(`🎫 Active tokens: ${tokenKeys.length}`);
    console.log(`🔒 Active locks: ${lockKeys.length}`);
    
    // Check for potential issues
    if (lockKeys.length > 0) {
      console.log('\n⚠️  Active locks detected:');
      for (const lockKey of lockKeys) {
        const lockValue = await redisClient.get(lockKey);
        const ttl = await redisClient.ttl(lockKey);
        console.log(`   ${lockKey}: PID ${lockValue}, TTL: ${ttl}s`);
      }
    }
    
    // Check token TTLs
    console.log('\n⏰ Token TTL Status:');
    for (const tokenKey of tokenKeys.slice(0, 5)) { // Show first 5
      const ttl = await redisClient.ttl(tokenKey);
      const clientId = tokenKey.replace('paypal_access_token_', '');
      const status = ttl > 3600 ? '✅' : ttl > 1800 ? '⚠️' : '❌';
      console.log(`   ${clientId}: ${status} ${ttl}s remaining`);
    }
    
  } catch (error) {
    console.error('❌ Pattern analysis failed:', error.message);
  }
}

// Performance test
async function performanceTest() {
  console.log('\n⚡ Cache Performance Test');
  console.log('========================');
  
  const testKey = 'test_performance_key';
  const testValue = { test: 'data', timestamp: Date.now() };
  
  try {
    // Test SET performance
    const setStart = Date.now();
    await redisClient.setex(testKey, 60, JSON.stringify(testValue));
    const setDuration = Date.now() - setStart;
    
    // Test GET performance
    const getStart = Date.now();
    const result = await redisClient.get(testKey);
    const getDuration = Date.now() - getStart;
    
    // Cleanup
    await redisClient.del(testKey);
    
    console.log(`📝 SET operation: ${setDuration}ms`);
    console.log(`📖 GET operation: ${getDuration}ms`);
    
    if (setDuration > 50 || getDuration > 50) {
      console.warn('⚠️  Performance degradation detected!');
    } else {
      console.log('✅ Cache performance is good');
    }
    
  } catch (error) {
    console.error('❌ Performance test failed:', error.message);
  }
}

// Main execution
async function main() {
  try {
    await monitorTokens();
    await analyzeTokenPatterns();
    await performanceTest();
  } catch (error) {
    console.error('❌ Monitor failed:', error);
  } finally {
    if (redisClient) {
      await redisClient.quit();
    }
    process.exit(0);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { monitorTokens, analyzeTokenPatterns, performanceTest };
