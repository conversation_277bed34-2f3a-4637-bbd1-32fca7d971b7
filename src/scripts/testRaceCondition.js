#!/usr/bin/env node

import { getAccessToken } from '../services/payPalService.js';
import { setCache, getCache } from '../config/cache.js';

// Test race condition với multiple concurrent requests
async function testRaceCondition() {
  console.log('🧪 Testing PayPal Token Race Condition');
  console.log('======================================');
  
  const clientId = 'AYik9q9KjVgRJNhYWSrGdC9GYG1cmdaKFCQ6NwMimVUu6hB-QCI58j1gd9RYPm8E4xTvYmlgrFZIRfe2'; // Test client ID
  const cacheKey = `paypal_access_token_${clientId}`;
  
  // Clear existing cache
  await setCache(cacheKey, null);
  console.log('🧹 Cleared existing cache');
  
  // Simulate multiple processes requesting token simultaneously
  const concurrentRequests = 10;
  const promises = [];
  
  console.log(`🚀 Starting ${concurrentRequests} concurrent token requests...`);
  
  const startTime = Date.now();
  
  for (let i = 0; i < concurrentRequests; i++) {
    const promise = getAccessToken(clientId).then(token => ({
      requestId: i + 1,
      token: token ? token.substring(0, 20) + '...' : null,
      timestamp: Date.now(),
      process: process.pid
    })).catch(error => ({
      requestId: i + 1,
      error: error.message,
      timestamp: Date.now(),
      process: process.pid
    }));
    
    promises.push(promise);
  }
  
  try {
    const results = await Promise.all(promises);
    const totalDuration = Date.now() - startTime;
    
    console.log('\n📊 Results:');
    console.log(`⏱️  Total Duration: ${totalDuration}ms`);
    console.log(`📈 Average per request: ${Math.round(totalDuration / concurrentRequests)}ms`);
    
    // Analyze results
    const successful = results.filter(r => r.token);
    const failed = results.filter(r => r.error);
    const uniqueTokens = new Set(results.filter(r => r.token).map(r => r.token));
    
    console.log(`\n✅ Successful requests: ${successful.length}`);
    console.log(`❌ Failed requests: ${failed.length}`);
    console.log(`🎫 Unique tokens generated: ${uniqueTokens.size}`);
    
    if (uniqueTokens.size > 1) {
      console.warn('⚠️  RACE CONDITION DETECTED: Multiple tokens generated!');
    } else if (uniqueTokens.size === 1) {
      console.log('✅ Race condition handled correctly - single token used');
    }
    
    // Show timing details
    console.log('\n⏰ Request Timing:');
    results.forEach(result => {
      const relativeTime = result.timestamp - startTime;
      const status = result.token ? '✅' : '❌';
      console.log(`   Request ${result.requestId}: ${status} ${relativeTime}ms`);
    });
    
    // Show any errors
    if (failed.length > 0) {
      console.log('\n❌ Errors:');
      failed.forEach(result => {
        console.log(`   Request ${result.requestId}: ${result.error}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Test cache performance under load
async function testCachePerformance() {
  console.log('\n⚡ Testing Cache Performance Under Load');
  console.log('======================================');
  
  const testKey = 'performance_test_key';
  const testData = { 
    large_data: 'x'.repeat(1000), // 1KB of data
    timestamp: Date.now(),
    process: process.pid
  };
  
  const operations = 100;
  const concurrentBatches = 5;
  
  console.log(`🔄 Running ${operations} operations in ${concurrentBatches} concurrent batches...`);
  
  const startTime = Date.now();
  const batchPromises = [];
  
  for (let batch = 0; batch < concurrentBatches; batch++) {
    const batchPromise = async () => {
      const results = [];
      
      for (let i = 0; i < operations / concurrentBatches; i++) {
        const key = `${testKey}_${batch}_${i}`;
        
        try {
          // SET operation
          const setStart = Date.now();
          await setCache(key, testData, [], 60);
          const setDuration = Date.now() - setStart;
          
          // GET operation
          const getStart = Date.now();
          const retrieved = await getCache(key);
          const getDuration = Date.now() - getStart;
          
          results.push({
            operation: i + 1,
            batch: batch + 1,
            setDuration,
            getDuration,
            success: !!retrieved
          });
          
        } catch (error) {
          results.push({
            operation: i + 1,
            batch: batch + 1,
            error: error.message
          });
        }
      }
      
      return results;
    };
    
    batchPromises.push(batchPromise());
  }
  
  try {
    const batchResults = await Promise.all(batchPromises);
    const allResults = batchResults.flat();
    const totalDuration = Date.now() - startTime;
    
    // Calculate statistics
    const successful = allResults.filter(r => r.success);
    const failed = allResults.filter(r => r.error);
    
    const setTimes = successful.map(r => r.setDuration);
    const getTimes = successful.map(r => r.getDuration);
    
    const avgSetTime = setTimes.reduce((a, b) => a + b, 0) / setTimes.length;
    const avgGetTime = getTimes.reduce((a, b) => a + b, 0) / getTimes.length;
    const maxSetTime = Math.max(...setTimes);
    const maxGetTime = Math.max(...getTimes);
    
    console.log('\n📊 Performance Results:');
    console.log(`⏱️  Total Duration: ${totalDuration}ms`);
    console.log(`✅ Successful Operations: ${successful.length}/${operations}`);
    console.log(`❌ Failed Operations: ${failed.length}`);
    console.log(`📝 Average SET time: ${avgSetTime.toFixed(2)}ms`);
    console.log(`📖 Average GET time: ${avgGetTime.toFixed(2)}ms`);
    console.log(`🔺 Max SET time: ${maxSetTime}ms`);
    console.log(`🔺 Max GET time: ${maxGetTime}ms`);
    
    // Performance warnings
    if (avgSetTime > 50 || avgGetTime > 50) {
      console.warn('⚠️  Performance degradation detected!');
    }
    
    if (maxSetTime > 200 || maxGetTime > 200) {
      console.warn('⚠️  Slow operations detected!');
    }
    
    // Cleanup
    for (let batch = 0; batch < concurrentBatches; batch++) {
      for (let i = 0; i < operations / concurrentBatches; i++) {
        const key = `${testKey}_${batch}_${i}`;
        await setCache(key, null); // Delete
      }
    }
    
    console.log('🧹 Cleanup completed');
    
  } catch (error) {
    console.error('❌ Performance test failed:', error);
  }
}

// Main execution
async function main() {
  try {
    await testRaceCondition();
    await testCachePerformance();
  } catch (error) {
    console.error('❌ Test suite failed:', error);
  } finally {
    process.exit(0);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { testRaceCondition, testCachePerformance };
