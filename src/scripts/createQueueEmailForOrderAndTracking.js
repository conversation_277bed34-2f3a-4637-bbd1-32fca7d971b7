// scripts/createQueueEmailForOrderAndTracking.js

import { Op } from "sequelize";
import Order from "../models/order.js";
import logger from "../utils/logger.js";
import { sendToQueue, QUEUES } from '../config/rabbitmq.js';

const BATCH_SIZE = 100;

async function getOrdersBetweenDates(startDate, endDate, type = 'new', page = 1) {
  try {
    let whereClause;

    if (type === 'tracking') {
      whereClause = {
        tracking_id: { [Op.not]: null },
        updatedAt: { [Op.between]: [startDate, endDate] }
      };
    } else {
      whereClause = {
        createdAt: { [Op.between]: [startDate, endDate] }
      };
    }

    const { rows: orders, count } = await Order.findAndCountAll({
      where: whereClause,
      limit: BATCH_SIZE,
      offset: (page - 1) * BATCH_SIZE,
      order: [['id', 'DESC']]
    });

    return { orders, totalPages: Math.ceil(count / BATCH_SIZE), currentPage: page };
  } catch (error) {
    logger.error('Error fetching orders:', error);
    throw error;
  }
}

async function processOrdersAndCreateEmailQueue() {
  // Cố định các ngày tháng bằng chuỗi
  const targetDate = '2023-10-16 00:00:00';
  const runTime = '2023-10-16 15:30:00';

  const startDateTracking = '2023-10-16 00:00:00';
  const endDateTracking = '2023-10-17 00:50:59';
  const startDateOrders = '2023-10-16 00:00:00';
  const endDateOrders = '2023-10-17 00:50:59';
  console.log('Target Date:', targetDate);
  console.log('Run Time:', runTime);
  console.log('Start Date Tracking:', startDateTracking);
  console.log('End Date Tracking:', endDateTracking);
  console.log('Start Date Orders:', startDateOrders);
  console.log('End Date Orders:', endDateOrders);

  // Object chứa các tham số cho truy vấn
  const queryParams = {
    targetDate,
    runTime,
    startDateTracking,
    endDateTracking,
    startDateOrders,
    endDateOrders
  };

  console.log('\nCác tham số cho truy vấn database:');
  console.log(queryParams);

  async function processOrders(startDate, endDate, type) {
    let currentPage = 1;
    let totalPages;

    do {
      const { orders, totalPages: pages, currentPage: page } = await getOrdersBetweenDates(startDate, endDate, type, currentPage);
      totalPages = pages;
      currentPage = page;

      for (const order of orders) {
        try {
          const emailData = {
            id: order.id,
            order_key: order.order_key,
            domain: order.domain,
            transaction_id: order.transaction_id,
            orderData: order.orderData,
            status: order.status,
            createdAt: order.createdAt,
            updatedAt: order.updatedAt,
            tracking_id: order.tracking_id  // Thêm tracking_id vào đây
          };

          if (type === 'new') {
            await sendToQueue('EMAIL', 'order_confirmation', emailData);
            logger.info(`Order confirmation email queued for order ${order.id}`);
          } else {
            await sendToQueue('EMAIL', 'shipping_confirmation', emailData);
            logger.info(`Shipping confirmation email queued for order ${order.id}`);
          }
        } catch (error) {
          logger.error(`Failed to queue ${type === 'new' ? 'order confirmation' : 'shipping confirmation'} email for order ${order.id}:`, error);
        }
      }

      currentPage++;

      if (currentPage <= totalPages) {
        await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay between batches
      }
    } while (currentPage <= totalPages);
  }

  // Process new orders
  await processOrders(startDateOrders, endDateOrders, 'new');

  // Process tracking updates
  await processOrders(startDateTracking, endDateTracking, 'tracking');

  logger.info('Finished queueing all order and shipping confirmation emails');
}

// Run the script
processOrdersAndCreateEmailQueue()
  .then(() => console.log('Email queue creation completed'))
  .catch(error => console.error('Error:', error));

// Export function if you want to use it elsewhere
export { processOrdersAndCreateEmailQueue };
