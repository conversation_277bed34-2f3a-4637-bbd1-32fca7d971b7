import fs from 'fs/promises';
import path from 'path';
import { createReadStream, existsSync } from 'fs';
import readline from 'readline';
import { createObjectCsvWriter } from 'csv-writer';
import axios from 'axios';
import dotenv from 'dotenv';
import paypalAccounts from "../config/paypal.json" assert { type: "json" };
import Order from '../models/order.js';
import { subHours } from 'date-fns';
import { sendNewOrderToTrueStore } from "../services/trueStoreService.js";
import logger from "../utils/logger.js";

dotenv.config();

const PAYPAL_API_BASE = "https://api.paypal.com";

async function getAccessToken(clientId, clientSecret) {
  const auth = Buffer.from(`${clientId}:${clientSecret}`).toString("base64");
  const response = await axios.post(
    `${PAYPAL_API_BASE}/v1/oauth2/token`,
    "grant_type=client_credentials",
    {
      headers: {
        Authorization: `Basic ${auth}`,
        "Content-Type": "application/x-www-form-urlencoded",
      },
    }
  );
  return response.data.access_token;
}

async function getPaymentDetails(transactionId, accessToken, account) {
  try {
    const response = await axios.get(
      `${PAYPAL_API_BASE}/v2/payments/captures/${transactionId}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );
    return { 
      success: true, 
      data: response.data,
      accessToken,  // Trả về accessToken thành công
      account      // Trả về account thành công
    };
  } catch (error) {
    const errorMessage = `Error fetching payment details for ${transactionId}: ${error.message}`;
    console.error(errorMessage);
    await logError(errorMessage, account);
    return { success: false, error: errorMessage };
  }
}

async function getOrderDetails(orderId, accessToken, account) {
  try {
    const response = await axios.get(
      `${PAYPAL_API_BASE}/v2/checkout/orders/${orderId}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );
    return { success: true, data: response.data };
  } catch (error) {
    const errorMessage = `Error fetching order details for ${orderId}: ${error.message}`;
    console.error(errorMessage);
    await logError(errorMessage, account);
    return { success: false, error: errorMessage };
  }
}

async function logError(message, account) {
  const logDir = path.join(process.cwd(), 'logs', account);
  const logFile = path.join(logDir, 'error.log');
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp}: ${message}\n`;

  try {
    await fs.mkdir(logDir, { recursive: true });
    await fs.appendFile(logFile, logMessage);
  } catch (error) {
    console.error('Failed to write to error log:', error);
  }
}

async function getShippingDetails(transactionId, accessToken, account) {
  try {
    const response = await axios.get(
      `${PAYPAL_API_BASE}/v1/shipping/trackers`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        params: {
          transaction_id: transactionId
        }
      }
    );

    if (response.data && response.data.trackers && response.data.trackers.length > 0) {
      return { success: true, data: response.data.trackers[0] };
    } else {
      console.log(`No shipping details found for transaction ${transactionId}`);
      return { success: false, error: 'No shipping details found' };
    }
  } catch (error) {
    const errorMessage = `Error fetching shipping details for ${transactionId}: ${error.message}`;
    console.error(errorMessage);
    await logError(errorMessage, account);
    return { success: false, error: errorMessage };
  }
}

async function getDisputeDetails(transactionId, accessToken, account) {
  try {
    const response = await axios.get(
      `${PAYPAL_API_BASE}/v1/customer/disputes`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        params: {
          disputed_transaction_id: transactionId
        }
      }
    );

    if (response.data && response.data.items && response.data.items.length > 0) {
      return { success: true, data: response.data.items[0] };
    } else {
      console.log(`No dispute found for transaction ${transactionId}`);
      return { success: false, error: 'No dispute found' };
    }
  } catch (error) {
    const errorMessage = `Error fetching dispute details for ${transactionId}: ${error.message}`;
    console.error(errorMessage);
    await logError(errorMessage, account);
    return { success: false, error: errorMessage };
  }
}

async function saveToOrder(transactionData) {
  try {
    const {
      transactionId,
      paymentResult,
      orderResult,
      shippingResult,
      successfulAccount,
      domain
    } = transactionData;

    // Lấy client_id từ cấu hình PayPal dựa trên tên tài khoản
    const paypalClientId = paypalAccounts[successfulAccount]?.client_id;

    if (!paypalClientId) {
      throw new Error(`PayPal client ID not found for account: ${successfulAccount}`);
    }

    // Điều chỉnh thời gian tạo (trừ đi 7 giờ)
    const createdAt = subHours(new Date(paymentResult.data.create_time), 7);

    const orderData = {
      domain,
      transaction_id: transactionId,
      paypal_client_id: paypalClientId,
      orderData: {
        total: paymentResult.data.amount?.value,
        billing: {
          city: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.admin_area_2,
          email: orderResult?.data?.payer?.email_address,
          phone: orderResult?.data?.payer?.phone?.phone_number?.national_number,
          state: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.admin_area_1,
          country: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.country_code,
          postcode: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.postal_code,
          address_1: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.address_line_1,
          address_2: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.address_line_2,
          last_name: orderResult?.data?.purchase_units?.[0]?.shipping?.name?.surname,
          first_name: orderResult?.data?.purchase_units?.[0]?.shipping?.name?.given_name
        },
        set_paid: true,
        shipping: {
          city: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.admin_area_2,
          state: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.admin_area_1,
          country: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.country_code,
          postcode: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.postal_code,
          address_1: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.address_line_1,
          address_2: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.address_line_2,
          last_name: orderResult?.data?.purchase_units?.[0]?.shipping?.name?.surname,
          first_name: orderResult?.data?.purchase_units?.[0]?.shipping?.name?.given_name
        },
        meta_data: [
          {
            key: "invoice_id",
            value: paymentResult.data.invoice_id
          },
          {
            key: "_ppcp_paypal_order_id",
            value: transactionId
          },
          {
            key: "_ppcp_paypal_payment_mode",
            value: "live"
          }
        ],
        line_items: orderResult?.data?.purchase_units?.[0]?.items?.map(item => ({
          sku: item.sku,
          name: item.name,
          price: parseFloat(item.unit_amount.value),
          total: (parseFloat(item.unit_amount.value) * parseInt(item.quantity)).toString(),
          quantity: parseInt(item.quantity),
          product_id: item.sku
        })) || [],
        payment_method: "ppcp-gateway",
        shipping_lines: [
          {
            total: orderResult?.data?.purchase_units?.[0]?.amount?.breakdown?.shipping?.value,
            method_id: "flat_rate"
          }
        ],
        shipping_total: orderResult?.data?.purchase_units?.[0]?.amount?.breakdown?.shipping?.value,
        transaction_id: transactionId,
        payment_method_title: "Paypal"
      },
      status: paymentResult.data.status,
      tracking_id: shippingResult?.data?.tracking_number || null,
      port: null,
      paypal_order_id: orderResult?.data?.id || null,
      paypal_checked_at: null,
      createdAt: createdAt,
      updatedAt: new Date()
    };

    const [order, created] = await Order.findOrCreate({
      where: { transaction_id: transactionId },
      defaults: orderData
    });

    if (!created) {
      await order.update(orderData);
      console.log(`Updated existing order for transaction ${transactionId}`);
    } else {
      console.log(`Created new order for transaction ${transactionId}`);
    }

    // Gửi order đến TrueStore ngay sau khi lưu
    try {
      await sendOrderToTrueStore(order, created ? 'new' : 'existing');
    } catch (trueStoreError) {
      console.error(`Error sending order to TrueStore for transaction ${transactionId}:`, trueStoreError);
    }

    return order;
  } catch (error) {
    console.error(`Error saving order for transaction ${transactionData.transactionId}:`, error);
    throw error;
  }
}

async function processTransactions(inputFile, domain) {
  const outputDir = path.join(path.dirname(inputFile), '0811');
  
  // Tạo thư mục output nếu nó chưa tồn tại
  try {
    await fs.mkdir(outputDir, { recursive: true });
  } catch (error) {
    if (error.code !== 'EEXIST') {
      console.error(`Error creating output directory: ${error.message}`);
      return;
    }
  }

  const allDataFile = path.join(outputDir, 'all_transactions.json');
  const allOrdersFile = path.join(outputDir, 'all_orders.json');
  const allShippingFile = path.join(outputDir, 'all_shipping.json');
  const allPaymentsFile = path.join(outputDir, 'all_payments.json');

  let allData = {};
  let allOrders = {};
  let allShipping = {};
  let allPayments = {};

  // Load existing data if files exist
  if (existsSync(allDataFile)) {
    allData = JSON.parse(await fs.readFile(allDataFile, 'utf8'));
  }
  if (existsSync(allOrdersFile)) {
    allOrders = JSON.parse(await fs.readFile(allOrdersFile, 'utf8'));
  }
  if (existsSync(allShippingFile)) {
    allShipping = JSON.parse(await fs.readFile(allShippingFile, 'utf8'));
  }
  if (existsSync(allPaymentsFile)) {
    allPayments = JSON.parse(await fs.readFile(allPaymentsFile, 'utf8'));
  }

  const accessTokens = {};
  for (const [key, account] of Object.entries(paypalAccounts)) {
    accessTokens[key] = await getAccessToken(account.client_id, account.secret_key);
  }

  const fileStream = createReadStream(inputFile);
  const rl = readline.createInterface({
    input: fileStream,
    crlfDelay: Infinity
  });

  const csvRecords = [];

  for await (const line of rl) {
    if (line.trim() === 'Transaction ID') continue;

    const transactionId = line.trim();
    let paymentResult = null;
    let successfulAccount = null;
    let successfulAccessToken = null;

    // Check cache first
    if (allPayments[transactionId]) {
      paymentResult = allPayments[transactionId];
      successfulAccount = paymentResult.account;
      successfulAccessToken = accessTokens[successfulAccount];
      console.log(`Payment details for transaction ${transactionId} found in cache. Used account: ${successfulAccount}`);
    } else {
      // Try each account until successful
      for (const [paypalAccount, accessToken] of Object.entries(accessTokens)) {
        paymentResult = await getPaymentDetails(transactionId, accessToken, paypalAccount);
        if (paymentResult.success) {
          successfulAccount = paypalAccount;
          successfulAccessToken = paymentResult.accessToken;
          paymentResult.account = paypalAccount;
          allPayments[transactionId] = paymentResult;
          await fs.writeFile(allPaymentsFile, JSON.stringify(allPayments, null, 2));
          console.log(`Successfully fetched payment details for transaction ${transactionId} using PayPal account: ${paypalAccount}`);
          break;
        }
      }
    }

    if (paymentResult && paymentResult.success) {
      let orderResult = null;
      let shippingResult = null;

      // Sử dụng lại accessToken thành công cho các API calls tiếp theo
      if (paymentResult.data.supplementary_data?.related_ids?.order_id) {
        const orderId = paymentResult.data.supplementary_data.related_ids.order_id;
        if (!allOrders[orderId]) {
          orderResult = await getOrderDetails(orderId, successfulAccessToken, successfulAccount);
          if (orderResult.success) {
            allOrders[orderId] = orderResult;
            await fs.writeFile(allOrdersFile, JSON.stringify(allOrders, null, 2));
          }
        } else {
          orderResult = allOrders[orderId];
        }
      }

      // Sử dụng lại accessToken cho shipping details
      if (!allShipping[transactionId]) {
        shippingResult = await getShippingDetails(transactionId, successfulAccessToken, successfulAccount);
        if (shippingResult.success) {
          allShipping[transactionId] = shippingResult;
          await fs.writeFile(allShippingFile, JSON.stringify(allShipping, null, 2));
        }
      } else {
        shippingResult = allShipping[transactionId];
      }

      // Sau khi có đủ thông tin, gọi hàm saveToOrder
      // try {
      //   const savedOrder = await saveToOrder({
      //     transactionId,
      //     paymentResult,
      //     orderResult,
      //     shippingResult,
      //     successfulAccount,
      //     domain
      //   });
      //   console.log(`Saved order to database for transaction ${transactionId}`);
      // } catch (saveError) {
      //   console.error(`Failed to save order for transaction ${transactionId}:`, saveError);
      // }

      // Create CSV record
      const csvRecord = {
        'Date': paymentResult.data.create_time ? new Date(paymentResult.data.create_time).toISOString().split('T')[0] : '',
        'Time': paymentResult.data.create_time ? new Date(paymentResult.data.create_time).toISOString().split('T')[1].split('.')[0] : '',
        'TimeZone': 'Z',
        'Name': orderResult?.data?.purchase_units?.[0]?.shipping?.name?.full_name || '',
        'Type': 'Payment',
        'Status': paymentResult.data.status || '',
        'Currency': paymentResult.data.amount?.currency_code || '',
        'Gross': paymentResult.data.amount?.value || '',
        'Fee': paymentResult.data.seller_receivable_breakdown?.paypal_fee?.value || '',
        'Net': paymentResult.data.seller_receivable_breakdown?.net_amount?.value || '',
        'From Email Address': orderResult?.data?.payer?.email_address || '',
        'To Email Address': orderResult?.data?.purchase_units?.[0]?.payee?.email_address || '',
        'Transaction ID': transactionId,
        'Shipping Address': `${orderResult?.data?.purchase_units?.[0]?.shipping?.address?.address_line_1 || ''}, ${orderResult?.data?.purchase_units?.[0]?.shipping?.address?.admin_area_2 || ''}, ${orderResult?.data?.purchase_units?.[0]?.shipping?.address?.admin_area_1 || ''}, ${orderResult?.data?.purchase_units?.[0]?.shipping?.address?.postal_code || ''}, ${orderResult?.data?.purchase_units?.[0]?.shipping?.address?.country_code || ''}`,
        'Address Status': '',
        'Item Title': orderResult?.data?.purchase_units?.[0]?.items?.[0]?.name || '',
        'Item ID': orderResult?.data?.purchase_units?.[0]?.items?.[0]?.sku || '',
        'Shipping and Handling Amount': orderResult?.data?.purchase_units?.[0]?.amount?.breakdown?.shipping?.value || '',
        'Insurance Amount': '',
        'Sales Tax': orderResult?.data?.purchase_units?.[0]?.items?.[0]?.tax?.value || '',
        'Option 1 Name': '',
        'Option 1 Value': '',
        'Option 2 Name': '',
        'Option 2 Value': '',
        'Reference Txn ID': orderResult?.data?.id || '',
        'Invoice Number': paymentResult.data.invoice_id || '',
        'Custom Number': '',
        'Quantity': orderResult?.data?.purchase_units?.[0]?.items?.[0]?.quantity || '',
        'Receipt ID': '',
        'Balance': '',
        'Address Line 1': orderResult?.data?.purchase_units?.[0]?.shipping?.address?.address_line_1 || '',
        'Address Line 2/District/Neighborhood': '',
        'Town/City': orderResult?.data?.purchase_units?.[0]?.shipping?.address?.admin_area_2 || '',
        'State/Province/Region/County/Territory/Prefecture/Republic': orderResult?.data?.purchase_units?.[0]?.shipping?.address?.admin_area_1 || '',
        'Zip/Postal Code': orderResult?.data?.purchase_units?.[0]?.shipping?.address?.postal_code || '',
        'Country': orderResult?.data?.purchase_units?.[0]?.shipping?.address?.country_code || '',
        'Contact Phone Number': orderResult?.data?.payer?.phone?.phone_number?.national_number || '',
        'Subject': '',
        'Note': orderResult?.data?.purchase_units?.[0]?.description || '',
        'Country Code': orderResult?.data?.payer?.address?.country_code || '',
        'Balance Impact': '',
        'Order ID': orderResult?.data?.id || '',
        'order_status': orderResult?.data?.status || '',
        'tracking_number': shippingResult?.data?.tracking_number || '',
        'tracking_carrier': shippingResult?.data?.carrier || '',
        'PayPal Account': successfulAccount, // Thêm tên cổng PayPal
      };

      csvRecords.push(csvRecord);

      // Update allData
      allData[transactionId] = {
        order_id: orderResult && orderResult.success ? orderResult.data.id : null,
        tracking_number: shippingResult && shippingResult.success ? shippingResult.data.tracking_number : null,
        tracking_carrier: shippingResult && shippingResult.success ? shippingResult.data.carrier : null,
        paypal_account: successfulAccount,
      };

      // Write updated data to file
      await fs.writeFile(allDataFile, JSON.stringify(allData, null, 2));

      console.log(`Processed transaction ${transactionId} with tracking number: ${allData[transactionId].tracking_number || 'Not available'} using PayPal account: ${successfulAccount}`);
    } else {
      console.log(`Failed to fetch payment details for transaction ${transactionId}. Tried all available PayPal accounts.`);
    }
  }

  // Write CSV file
  const csvWriter = createObjectCsvWriter({
    path: path.join(outputDir, 'all_transactions.csv'),
    header: [
      { id: 'Date', title: 'Date' },
      { id: 'Time', title: 'Time' },
      { id: 'TimeZone', title: 'TimeZone' },
      { id: 'Name', title: 'Name' },
      { id: 'Type', title: 'Type' },
      { id: 'Status', title: 'Status' },
      { id: 'Currency', title: 'Currency' },
      { id: 'Gross', title: 'Gross' },
      { id: 'Fee', title: 'Fee' },
      { id: 'Net', title: 'Net' },
      { id: 'From Email Address', title: 'From Email Address' },
      { id: 'To Email Address', title: 'To Email Address' },
      { id: 'Transaction ID', title: 'Transaction ID' },
      { id: 'Shipping Address', title: 'Shipping Address' },
      { id: 'Address Status', title: 'Address Status' },
      { id: 'Item Title', title: 'Item Title' },
      { id: 'Item ID', title: 'Item ID' },
      { id: 'Shipping and Handling Amount', title: 'Shipping and Handling Amount' },
      { id: 'Insurance Amount', title: 'Insurance Amount' },
      { id: 'Sales Tax', title: 'Sales Tax' },
      { id: 'Option 1 Name', title: 'Option 1 Name' },
      { id: 'Option 1 Value', title: 'Option 1 Value' },
      { id: 'Option 2 Name', title: 'Option 2 Name' },
      { id: 'Option 2 Value', title: 'Option 2 Value' },
      { id: 'Reference Txn ID', title: 'Reference Txn ID' },
      { id: 'Invoice Number', title: 'Invoice Number' },
      { id: 'Custom Number', title: 'Custom Number' },
      { id: 'Quantity', title: 'Quantity' },
      { id: 'Receipt ID', title: 'Receipt ID' },
      { id: 'Balance', title: 'Balance' },
      { id: 'Address Line 1', title: 'Address Line 1' },
      { id: 'Address Line 2/District/Neighborhood', title: 'Address Line 2/District/Neighborhood' },
      { id: 'Town/City', title: 'Town/City' },
      { id: 'State/Province/Region/County/Territory/Prefecture/Republic', title: 'State/Province/Region/County/Territory/Prefecture/Republic' },
      { id: 'Zip/Postal Code', title: 'Zip/Postal Code' },
      { id: 'Country', title: 'Country' },
      { id: 'Contact Phone Number', title: 'Contact Phone Number' },
      { id: 'Subject', title: 'Subject' },
      { id: 'Note', title: 'Note' },
      { id: 'Country Code', title: 'Country Code' },
      { id: 'Balance Impact', title: 'Balance Impact' },
      { id: 'Order ID', title: 'Order ID' },
      { id: 'order_status', title: 'Order Status' },
      { id: 'tracking_number', title: 'Tracking Number' },
      { id: 'tracking_carrier', title: 'Tracking Carrier' },
      { id: 'PayPal Account', title: 'PayPal Account' }, // Thêm cột mới
    ]
  });

  await csvWriter.writeRecords(csvRecords);
  console.log('CSV file has been written');
}

async function sendOrderToTrueStore(order, action) {
  try {
    const trueStoreResult = await sendNewOrderToTrueStore(order);
    logger.info(`Order ${action} sent to TrueStore`, {
      orderId: order.id,
      domain: order.domain,
      result: trueStoreResult,
    });
  } catch (trueStoreError) {
    logger.error(`Error sending ${action} order to TrueStore`, {
      orderId: order.id,
      domain: order.domain,
      error: trueStoreError.message,
      stack: trueStoreError.stack,
    });
  }
}

// Usage
const inputFile = path.join(process.cwd(), 'data', '0811.txt');
const domain = 'smartuscart.com';
processTransactions(inputFile, domain).catch(async (error) => {
  console.error(error);
  await logError(`Error in processTransactions: ${error.message}`, 'general');
});
