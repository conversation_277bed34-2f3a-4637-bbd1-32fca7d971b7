import axios from "axios";
import dotenv from "dotenv";
import paypalAccounts from "../../config/paypal.json" assert { type: "json" };

dotenv.config();

// const PAYPAL_API_BASE =
//   process.env.PAYPAL_API_BASE || "https://api.paypal.com";
// const PAYPAL_CLIENT_ID = paypalAccounts["003"].client_id;
// const PAYPAL_SECRET = paypalAccounts["003"].secret_key;

const PAYPAL_API_BASE = "https://api.sandbox.paypal.com";
const PAYPAL_CLIENT_ID = "AbGDiSDqoEj4eAGaTajmisiQGpbG1D38F9hv5W3Vu9_GeQpx8wMoThiSm8huWkE1SSzuZxDe9AeTn6HJ";
const PAYPAL_SECRET = "EJZOmFcZFQFhxOkoMxxux7aVNVUjpWCtko_AaFKyqENYWQf5FbgcWz08zjqs9pGUElp7vfGfhf1LoJbz";

async function getAccessToken() {
  const auth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_SECRET}`).toString(
    "base64"
  );
  const response = await axios.post(
    `${PAYPAL_API_BASE}/v1/oauth2/token`,
    "grant_type=client_credentials",
    {
      headers: {
        Authorization: `Basic ${auth}`,
        "Content-Type": "application/x-www-form-urlencoded",
      },
    }
  );
  return response.data.access_token;
}

async function getPaypalOrders(startDate, endDate) {
  const accessToken = await getAccessToken();
  console.log("startDate", accessToken);

  let orders = [];
  let hasMorePages = true;
  let page = 1;

  while (hasMorePages) {
    const response = await axios.get(`${PAYPAL_API_BASE}/v2/checkout/orders`, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
      params: {
        start_date: startDate,
        end_date: endDate,
        page_size: 100,
        page: page,
      },
    });

    orders = orders.concat(response.data.orders);

    hasMorePages = response.data.total_pages > page;
    page++;
  }

  return orders;
}

async function getShippingDetails(transactionId) {
  const accessToken = await getAccessToken();

  try {
    const response = await axios.get(
      `${PAYPAL_API_BASE}/v1/shipping/trackers`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        params: {
          transaction_id: transactionId
        }
      }
    );

    if (response.data && response.data.trackers && response.data.trackers.length > 0) {
      return response.data.trackers[0];
    } else {
      console.log(`No shipping details found for transaction ${transactionId}`);
      return null;
    }
  } catch (error) {
    console.error(
      "Error fetching shipping details:",
      error.response ? error.response.data : error.message
    );
    return null;
  }
}

async function getDisputeDetails(transactionId) {
  const accessToken = await getAccessToken();

  try {
    const response = await axios.get(
      `${PAYPAL_API_BASE}/v1/customer/disputes`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        params: {
          disputed_transaction_id: transactionId
        }
      }
    );

    if (response.data && response.data.items && response.data.items.length > 0) {
      return response.data.items[0];
    } else {
      console.log(`No dispute found for transaction ${transactionId}`);
      return null;
    }
  } catch (error) {
    console.error(
      "Error fetching dispute details:",
      error.response ? error.response.data : error.message
    );
    return null;
  }
}

async function getOrderDetails(transactionId) {
  const accessToken = await getAccessToken();

  try {
    // First, get payment details
    const paymentResponse = await axios.get(
      `${PAYPAL_API_BASE}/v2/payments/captures/${transactionId}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    const paymentData = paymentResponse.data;
    const orderId = paymentData.supplementary_data?.related_ids?.order_id;

    if (!orderId) {
      throw new Error("Order ID not found in payment data");
    }

    // Then, get order details using the order ID
    const orderResponse = await axios.get(
      `${PAYPAL_API_BASE}/v2/checkout/orders/${orderId}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    // Get shipping details
    const shippingDetails = await getShippingDetails(transactionId);

    // Get dispute details
    const disputeDetails = await getDisputeDetails(transactionId);

    return {
      paymentDetails: paymentData,
      orderDetails: orderResponse.data,
      shippingDetails: shippingDetails,
      disputeDetails: disputeDetails
    };
  } catch (error) {
    console.error(
      "Error fetching order details:",
      error.response ? error.response.data : error.message
    );
    throw error;
  }
}

async function getAllPaymentCaptures(startDate, endDate) {
  const accessToken = await getAccessToken();
  let captures = [];
  let hasMorePages = true;
  let page = 1;

  while (hasMorePages) {
    try {
      const response = await axios.get(`${PAYPAL_API_BASE}/v2/payments/captures`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        params: {
          start_date: startDate,
          end_date: endDate,
          page_size: 100,
          page: page,
        },
      });
      console.log("response", response.data);

      if (response.status === 200 && response.data.items) {
        captures = captures.concat(response.data.items);
        hasMorePages = response.data.links && response.data.links.some(link => link.rel === 'next');
        page++;
      } else {
        console.log('Unexpected response:', response.status, response.data);
        hasMorePages = false;
      }
    } catch (error) {
      console.error(
        "Error fetching payment captures:",
        error.response ? error.response.data : error.message
      );
      if (error.response && error.response.status === 404) {
        console.log("Endpoint not found. Please check the API documentation for the correct endpoint.");
      }
      hasMorePages = false;
    }
  }

  return captures;
}

async function getAllTransactions(startDate, endDate) {
  const accessToken = await getAccessToken();
  let transactions = [];
  let page = 1;
  let totalPages = 1;

  while (page <= totalPages) {
    try {
      const response = await axios.get(`${PAYPAL_API_BASE}/v1/reporting/transactions`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        params: {
          start_date: startDate, // Đảm bảo định dạng ISO 8601
          end_date: endDate,
          fields: "transaction_info,payer_info,shipping_info,cart_info", // Tùy chọn các trường bạn cần
          page_size: 100,
          page: page
        },
      });

      console.log("Response:", response);

      if (response.data.transaction_details) {
        transactions = transactions.concat(response.data.transaction_details);
        totalPages = response.data.total_pages;
        page++;
      } else {
        console.log('Unexpected response:', response.status, response.data);
        break;
      }
    } catch (error) {
      console.error(
        "Error fetching transactions:",
        error.response ? error.response.data : error.message
      );
      break;
    }
  }

  return transactions;
}

// Usage example
const startDate = "2024-10-01T00:00:00Z";
const endDate = "2024-10-19T23:59:59Z";

// getAllPaymentCaptures(startDate, endDate)
//   .then((captures) =>
//     console.log("All payment captures:", JSON.stringify(captures, null, 2))
//   )
//   .catch((error) => console.error("Error fetching payment captures:", error));

// getPaypalOrders(startDate, endDate)
//   .then((orders) => console.log("All orders:", JSON.stringify(orders, null, 2)))
//   .catch((error) => console.error("Error fetching orders:", error));

// const transactionId = "31J21345A2682501M";
// getOrderDetails(transactionId)
//   .then((details) =>
//     console.log("Order details:", JSON.stringify(details, null, 2))
//   )
//   .catch((error) => console.error("Error fetching order details:", error));

// Usage example
// const startDate = "2023-01-01T00:00:00Z";
// const endDate = "2023-12-31T23:59:59Z";

getAllTransactions(startDate, endDate)
  .then((transactions) =>
    console.log("All transactions:", JSON.stringify(transactions, null, 2))
  )
  .catch((error) => console.error("Error fetching transactions:", error));
