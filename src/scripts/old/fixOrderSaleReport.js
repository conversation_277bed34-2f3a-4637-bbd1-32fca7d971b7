import logger from "../../utils/logger.js";
import Order from "../../models/order.js";
import * as saleReportService from "../../services/saleReportService.js";
import * as domainReportService from "../../services/domainReportService.js";

async function recalculateReports(orderIds) {
  try {
    logger.info('Starting report recalculation for orders:', { orderIds });
    console.log('Starting report recalculation for orders:', { orderIds });
    // L<PERSON>y thông tin các orders
    const orders = await Order.findAll({
      where: {
        id: orderIds
      }
    });

    if (orders.length !== orderIds.length) {
      const foundIds = orders.map(order => order.id);
      const missingIds = orderIds.filter(id => !foundIds.includes(id));
      logger.warn('Some orders not found:', { missingIds });
      console.log('Some orders not found:', { missingIds });
    }

    // Tính lại report cho từng order
    for (const order of orders) {
      try {
        logger.info(`Processing order ${order.id}`, {
          domain: order.domain,
          transaction_id: order.transaction_id
        });

        // Update sale report
        await saleReportService.updateSaleReport(order);
        logger.info(`Updated sale report for order ${order.id}`);

        // Update domain report
        const stats = await domainReportService.updateDomainReport(order);
        logger.info(`Updated domain report for order ${order.id}`, { stats });

      } catch (error) {
        logger.error(`Error processing order ${order.id}`, {
          error: error.message,
          stack: error.stack
        });
      }
    }

    logger.info('Completed report recalculation', {
      total_processed: orders.length,
      order_ids: orders.map(o => o.id)
    });
    console.log('Completed report recalculation', {
      total_processed: orders.length,
      order_ids: orders.map(o => o.id)
    });

  } catch (error) {
    logger.error('Error in recalculateReports:', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

// Chạy script
const orderIds = [107527, 107522, 107469];

recalculateReports(orderIds)
  .then(() => {
    console.log('Report recalculation completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Error running script:', error);
    process.exit(1);
  });
