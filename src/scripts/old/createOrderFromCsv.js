import fs from "fs";
import csv from "csv-parser";
import path from "path";
import { fileURLToPath } from "url";
import Order from "../../models/order.js";
import { parse, subHours } from "date-fns";
import PQueue from "p-queue";
import { sendNewOrderToTrueStore } from "../../services/trueStoreService.js";
import logger from "../../utils/logger.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function createOrderFromCsv(filePath, domain, paypal_client_id) {
  const orders = [];
  let totalRows = 0;
  let createdOrders = 0;
  let updatedOrders = 0;
  let sentToTrueStore = 0;
  const queue = new PQueue({ concurrency: 1 });

  async function sendOrderToTrueStore(order, action) {
    try {
      const trueStoreResult = await sendNewOrderToTrueStore(order);
      logger.info(`Order ${action} sent to TrueStore`, {
        orderId: order.id,
        domain: order.domain,
        result: trueStoreResult,
      });
      sentToTrueStore++;
    } catch (trueStoreError) {
      logger.error(`Error sending ${action} order to TrueStore`, {
        orderId: order.id,
        domain: order.domain,
        error: trueStoreError.message,
        stack: trueStoreError.stack,
      });
    }
  }

  return new Promise((resolve, reject) => {
    fs.createReadStream(path.resolve(__dirname, "..", filePath))
      .pipe(csv())
      .on("data", (row) => {
        totalRows++;
        queue.add(async () => {
          try {
            const transaction_id = row["Transaction ID"];

            // Tạo createdAt là UTC-7
            const dateTimeString = `${row.Date} ${row.Time}`;
            console.log("Processing DateTime:", dateTimeString);

            let createdAt;
            try {
              createdAt = parse(dateTimeString, "M/d/yy HH:mm:ss", new Date());

              // Kiểm tra xem createdAt có phải là ngày hợp lệ không
              if (isNaN(createdAt.getTime())) {
                throw new Error("Invalid date");
              }

              // Trừ đi 7 giờ để có UTC-7
              createdAt = subHours(createdAt, 7);
            } catch (parseError) {
              console.error("Error parsing date:", parseError);
              return;
            }

            const orderData = {
              total: row.Gross,
              billing: {
                city: row["Town/City"],
                email: row["From Email Address"],
                phone: row["Contact Phone Number"],
                state:
                  row[
                    "State/Province/Region/County/Territory/Prefecture/Republic"
                  ],
                country: row["Country Code"],
                postcode: row["Zip/Postal Code"],
                address_1: row["Address Line 1"],
                address_2: row["Address Line 2/District/Neighborhood"],
                last_name: row.Name.split(" ").slice(1).join(" "),
                first_name: row.Name.split(" ")[0],
              },
              set_paid: true,
              shipping: {
                city: row["Town/City"],
                state:
                  row[
                    "State/Province/Region/County/Territory/Prefecture/Republic"
                  ],
                country: row["Country Code"],
                postcode: row["Zip/Postal Code"],
                address_1: row["Address Line 1"],
                address_2: row["Address Line 2/District/Neighborhood"],
                last_name: row.Name.split(" ").slice(1).join(" "),
                first_name: row.Name.split(" ")[0],
              },
              meta_data: [
                {
                  key: "invoice_id",
                  value: row["Invoice Number"],
                },
                {
                  key: "_ppcp_paypal_order_id",
                  value: transaction_id,
                },
                {
                  key: "_ppcp_paypal_payment_mode",
                  value: "live",
                },
              ],
              line_items: [
                {
                  sku: row["Item ID"],
                  name: row["Item Title"],
                  price: parseFloat(row["Gross"]),
                  total: row.Gross,
                  quantity: parseInt(row.Quantity),
                  product_id: row["Item ID"],
                },
              ],
              payment_method: "ppcp-gateway",
              shipping_lines: [
                {
                  total: row["Shipping and Handling Amount"],
                  method_id: "flat_rate",
                },
              ],
              shipping_total: row["Shipping and Handling Amount"],
              transaction_id: transaction_id,
              payment_method_title: row["Custom Number"],
            };

            // Check if order already exists
            const existingOrder = await Order.findOne({
              where: { transaction_id },
            });

            if (existingOrder) {
              console.log(
                `Updating order with transaction ID ${transaction_id}...`
              );
              try {
                await existingOrder.update({
                  domain,
                  paypal_client_id,
                  orderData,
                  createdAt,
                });
                console.log(
                  `Updated order with transaction ID ${transaction_id}`
                );
                orders.push(existingOrder);
                updatedOrders++;

                // Send existing order to TrueStore
                await sendOrderToTrueStore(existingOrder, 'existing');
              } catch (updateError) {
                console.error(
                  `Error updating order with transaction ID ${transaction_id}:`,
                  updateError
                );
              }
            } else {
              console.log(
                `Creating new order with transaction ID ${transaction_id}...`
              );
              try {
                const newOrder = await Order.create({
                  domain,
                  paypal_client_id,
                  transaction_id,
                  orderData,
                  createdAt,
                });
                console.log(
                  `Created new order with transaction ID ${transaction_id}`
                );
                orders.push(newOrder);
                createdOrders++;

                // Send new order to TrueStore
                await sendOrderToTrueStore(newOrder, 'new');
              } catch (createError) {
                console.error(
                  `Error creating order with transaction ID ${transaction_id}:`,
                  createError
                );
              }
            }
          } catch (error) {
            console.error("Error processing order", error);
          }
        });
      })
      .on("end", () => {
        queue.onIdle().then(() => {
          console.log(`Total rows processed: ${totalRows}`);
          console.log(`New orders created: ${createdOrders}`);
          console.log(`Existing orders updated: ${updatedOrders}`);
          console.log(`Orders sent to TrueStore: ${sentToTrueStore}`);
          console.log(`Total orders processed: ${orders.length}`);
          resolve(orders);
        });
      })
      .on("error", (error) => {
        console.error("Error reading CSV file", error);
        reject(error);
      });
  });
}

// Run the function
createOrderFromCsv(
  "./data/updated_transactions_22_10_2024.csv",
  "smartuscart.com"
)
  .then((orders) => console.log(`Created ${orders.length} new orders`))
  .catch((error) => console.error(error));

export default createOrderFromCsv;
