import { Op } from 'sequelize';
import Order from './models/order.js';
import HistoryOrder from './models/historyOrderRemote.js';
import DomainConfig from './models/domainConfig.js';
import { createOrder } from './config/woo.js';

export const compareOrdersHistoryImmediate = async () => {
  try {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setHours(startDate.getHours() - 200);

    const orders = await Order.findAll({
      where: {
        createdAt: {
          [Op.between]: [
            startDate,
            endDate.setMinutes(endDate.getMinutes() - 33),
          ],
        },
      },
      attributes: ['transaction_id', 'domain', 'id', 'orderData'],
    });

    const historyOrders = await HistoryOrder.findAll({
      where: {
        DateOrder: {
          [Op.between]: [startDate, new Date()],
        },
        TotalPrice: {
          [Op.gt]: 0,
        },
      },
      include: [
        {
          model: DomainConfig,
          where: {
            port: {
              [Op.ne]: 'cardshield',
            },
          },
        },
      ],
      attributes: ['TransactionIds', 'Domain'],
    });

    const missingInHistory = orders.filter(order => {
      return !historyOrders.some(historyOrder =>
        historyOrder.TransactionIds === order.transaction_id &&
        historyOrder.Domain === order.domain
      );
    });

    for (let orderMissing of missingInHistory) {
      await createOrder(orderMissing.orderData, orderMissing.domain);
      console.log('Import Order Success', orderMissing.domain, orderMissing.transaction_id);
      await Order.update({ importedAt: new Date() }, {
        where: { id: orderMissing.id }
      });
    }

    console.log('Missing orders check completed and logged.');
  } catch (error) {
    console.error('Error comparing orders:', JSON.stringify(error));
  }
};

// Run the function immediately
compareOrdersHistoryImmediate();
