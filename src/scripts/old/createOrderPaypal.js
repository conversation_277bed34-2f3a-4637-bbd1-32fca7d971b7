import axios from "axios";

const PAYPAL_API_BASE = "https://api.sandbox.paypal.com";
const PAYPAL_CLIENT_ID = "AbGDiSDqoEj4eAGaTajmisiQGpbG1D38F9hv5W3Vu9_GeQpx8wMoThiSm8huWkE1SSzuZxDe9AeTn6HJ";
const PAYPAL_SECRET = "EJZOmFcZFQFhxOkoMxxux7aVNVUjpWCtko_AaFKyqENYWQf5FbgcWz08zjqs9pGUElp7vfGfhf1LoJbz";

async function getAccessToken() {
    const auth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_SECRET}`).toString('base64');
    const response = await axios.post(
        `${PAYPAL_API_BASE}/v1/oauth2/token`,
        'grant_type=client_credentials',
        {
            headers: {
                'Authorization': `Basic ${auth}`,
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        }
    );
    return response.data.access_token;
}

async function createTestOrder() {
    try {
        const accessToken = await getAccessToken();
        
        const orderData = {
            intent: 'CAPTURE',
            purchase_units: [{
                amount: {
                    currency_code: 'USD',
                    value: '100.00',
                    breakdown: {
                        item_total: {
                            currency_code: 'USD',
                            value: '100.00'
                        }
                    }
                },
                items: [
                    {
                        name: "Test Product 1",
                        description: "Test Product Description",
                        quantity: "1",
                        unit_amount: {
                            currency_code: "USD",
                            value: "50.00"
                        }
                    },
                    {
                        name: "Test Product 2",
                        description: "Test Product Description 2",
                        quantity: "1",
                        unit_amount: {
                            currency_code: "USD",
                            value: "50.00"
                        }
                    }
                ]
            }],
            application_context: {
                brand_name: "My Store",
                landing_page: "NO_PREFERENCE",
                user_action: "PAY_NOW",
                return_url: "https://example.com/success",
                cancel_url: "https://example.com/cancel"
            }
        };

        const response = await axios.post(
            `${PAYPAL_API_BASE}/v2/checkout/orders`,
            orderData,
            {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json',
                }
            }
        );
        
        return response.data;
    } catch (error) {
        console.error('Error creating PayPal order:', 
            error.response ? error.response.data : error.message
        );
        throw error;
    }
}

async function capturePayment(orderId) {
    try {
        const accessToken = await getAccessToken();
        
        const response = await axios.post(
            `${PAYPAL_API_BASE}/v2/checkout/orders/${orderId}/capture`,
            {}, // Empty body
            {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json',
                }
            }
        );
        
        return response.data;
    } catch (error) {
        console.error('Error capturing PayPal payment:', 
            error.response ? error.response.data : error.message
        );
        throw error;
    }
}

// Test thử hàm
// createTestOrder()
//     .then(order => {
//         console.log('Order created successfully!');
//         console.log('Order ID:', order.id);
//         console.log('Order Status:', order.status);
//         console.log('Links:', order.links);
//         console.log('\nFull order details:', JSON.stringify(order, null, 2));
//     })
//     .catch(error => {
//         console.error('Failed to create order:', error);
//     });

// Test thử hàm
const testOrderId = "1VE27713YE242871V"; // Thay thế bằng order ID thật
capturePayment(testOrderId)
    .then(captureData => {
        console.log('Payment captured successfully!');
        console.log('Capture ID:', captureData.purchase_units[0].payments.captures[0].id);
        console.log('Capture Status:', captureData.status);
        console.log('\nFull capture details:', JSON.stringify(captureData, null, 2));
    })
    .catch(error => {
        console.error('Failed to capture payment:', error);
    });
