import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __dirname = dirname(fileURLToPath(import.meta.url));

// Đọc file cấu hình
const rabbitmqConfig = JSON.parse(readFileSync(join(__dirname, 'config', 'rabbitmq.json'), 'utf8'));

import amqp from 'amqplib';

async function testRabbitMQ() {
  let connection;
  try {
    console.log('Connecting to RabbitMQ...');
    console.log('URL:', rabbitmqConfig.url);
    connection = await amqp.connect(rabbitmqConfig.url);
    console.log('Connected to RabbitMQ');

    const channel = await connection.createChannel();
    console.log('Channel created');

    await channel.assertQueue(rabbitmqConfig.queue, rabbitmqConfig.options);
    console.log(`Queue '${rabbitmqConfig.queue}' asserted`);

    const message = 'Hello, RabbitMQ!';
    channel.sendToQueue(rabbitmqConfig.queue, Buffer.from(message));
    console.log(`Sent message: ${message}`);

    console.log('Waiting for messages...');
    channel.consume(rabbitmqConfig.queue, (msg) => {
      if (msg !== null) {
        console.log(`Received message: ${msg.content.toString()}`);
        channel.ack(msg);

        setTimeout(() => {
          connection.close();
          console.log('Connection closed');
          process.exit(0);
        }, 500);
      }
    });
  } catch (error) {
    console.error('Error details:', error);
    if (error.code) {
      console.error('Error code:', error.code);
    }
    if (error.errno) {
      console.error('Error number:', error.errno);
    }
    if (error.syscall) {
      console.error('System call:', error.syscall);
    }
    if (error.address) {
      console.error('Address:', error.address);
    }
    if (error.port) {
      console.error('Port:', error.port);
    }
    if (connection) {
      try {
        await connection.close();
        console.log('Connection closed due to error');
      } catch (closeError) {
        console.error('Error closing connection:', closeError);
      }
    }
    process.exit(1);
  }
}

testRabbitMQ();