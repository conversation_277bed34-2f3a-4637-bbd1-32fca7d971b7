import logger from "../../utils/logger.js";
import Order from "../../models/order.js";
import * as saleReportService from "../../services/saleReportService.js";
import * as domainReportService from "../../services/domainReportService.js";
import { convertToClientTimezone } from "../../utils/utils.js";
import { Op } from "sequelize";

async function recalculateReports(orders) {
  try {
    logger.info('Starting report recalculation for orders:', { 
      count: orders.length,
      domains: [...new Set(orders.map(o => o.domain))]
    });

    // Tính lại report cho từng order
    for (const order of orders) {
      try {
        logger.info(`Processing order ${order.id}`, {
          domain: order.domain,
          transaction_id: order.transaction_id,
          createdAt: order.createdAt
        });

        // Update sale report
        await saleReportService.updateSaleReport(order);
        logger.info(`Updated sale report for order ${order.id}`);

        // Update domain report
        const stats = await domainReportService.updateDomainReport(order);
        logger.info(`Updated domain report for order ${order.id}`, { stats });

      } catch (error) {
        logger.error(`Error processing order ${order.id}`, {
          error: error.message,
          stack: error.stack
        });
      }
    }

    logger.info('Completed report recalculation', {
      total_processed: orders.length,
      domains: [...new Set(orders.map(o => o.domain))],
      date_range: {
        min: orders.length ? orders.reduce((min, o) => o.createdAt < min ? o.createdAt : min, orders[0].createdAt) : null,
        max: orders.length ? orders.reduce((max, o) => o.createdAt > max ? o.createdAt : max, orders[0].createdAt) : null
      }
    });

  } catch (error) {
    logger.error('Error in recalculateReports:', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

async function main() {
  try {
    // Convert timezone for October 28
    const { startDate, endDate } = convertToClientTimezone(
      '28-10-2024',  // start date
      '28-10-2024'   // end date (next day to include full day)
    );

    // Find all orders for specified domains and date
    const orders = await Order.findAll({
      where: {
        domain: {
          [Op.in]: ['boldbazaarus.com', 'zipdawn.com']
        },
        createdAt: {
          [Op.between]: [startDate, endDate]
        }
      },
      order: [['createdAt', 'ASC']]
    });

    logger.info('Found orders:', {
      total: orders.length,
      by_domain: {
        'boldbazaarus.com': orders.filter(o => o.domain === 'boldbazaarus.com').length,
        'zipdawn.com': orders.filter(o => o.domain === 'zipdawn.com').length
      },
      date_range: {
        start: startDate,
        end: endDate
      }
    });

    if (orders.length === 0) {
      logger.warn('No orders found for the specified criteria');
      return;
    }

    await recalculateReports(orders);

  } catch (error) {
    logger.error('Error in main:', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

// Chạy script
main()
  .then(() => {
    console.log('Script completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
