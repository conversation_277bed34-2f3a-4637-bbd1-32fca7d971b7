import logger from "../utils/logger.js";
import Order from "../models/order.js";
import SaleReport from "../models/saleReport.js";
import DomainReport from "../models/domainReport.js";
import * as saleReportService from "../services/saleReportService.js";
import * as domainReportService from "../services/domainReportService.js";
import { convertToClientTimezone, adjustDate } from "../utils/utils.js";
import { Op } from "sequelize";

async function resetReports(domain, dateString) {
  logger.info('Resetting reports to default values', { domain, date: dateString });
  
  // Reset sale report
  await SaleReport.update({
    orders: 0,
    money: 0.0,
    Stats: {},
    StatsUTM: {},
    orderIds: []
  }, {
    where: {
      domain,
      date: dateString
    }
  });
  logger.info('Reset sale report to default values');

  // Reset domain report
  await DomainReport.update({
    Stats: [{
      total_sales: 0,
      total_orders: 0,
      total_shipping: 0,
      items: []
    }],
    orderIds: []
  }, {
    where: {
      domain,
      date: dateString
    }
  });
  logger.info('Reset domain report to default values');
}

async function recalculateReports(orders, dateString) {
  try {
    logger.info('Starting report recalculation for orders:', { 
      count: orders.length,
      domain: orders.length > 0 ? orders[0].domain : null,
      date: dateString
    });

    // Tính lại report cho từng order
    for (const order of orders) {
      try {
        logger.info(`Processing order ${order.id}`, {
          domain: order.domain,
          transaction_id: order.transaction_id,
          createdAt: order.createdAt
        });

        // Update sale report
        await saleReportService.updateSaleReport(order);
        logger.info(`Updated sale report for order ${order.id}`);

        // Update domain report
        const stats = await domainReportService.updateDomainReport(order);
        logger.info(`Updated domain report for order ${order.id}`, { stats });

      } catch (error) {
        logger.error(`Error processing order ${order.id}`, {
          error: error.message,
          stack: error.stack
        });
      }
    }

    logger.info('Completed report recalculation', {
      total_processed: orders.length,
      domain: orders.length > 0 ? orders[0].domain : null,
      date: dateString
    });

  } catch (error) {
    logger.error('Error in recalculateReports:', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

async function main() {
  try {
    // Lấy tham số từ command line
    const args = process.argv.slice(2);
    if (args.length !== 2) {
      console.error('Usage: node syncSaleReportByDate.js <date> <domain>');
      console.error('Example: node syncSaleReportByDate.js 12-02-2025 mysticraftus.com');
      process.exit(1);
    }

    const [date, domain] = args;

    // Convert timezone for the specified date
    const { startDate, endDate } = convertToClientTimezone(
      date,  // start date
      date   // end date (same day)
    );

    // Format date string for report tables
    const dateString = adjustDate(startDate);

    // Find all orders for specified domain and date
    const orders = await Order.findAll({
      where: {
        domain: domain,
        createdAt: {
          [Op.between]: [startDate, endDate]
        },
        status: "processing"
      },
      order: [['createdAt', 'ASC']]
    });

    logger.info('Found orders:', {
      total: orders.length,
      domain: domain,
      date_range: {
        start: startDate,
        end: endDate
      },
      status: "processing"
    });

    if (orders.length === 0) {
      logger.warn('No orders found for the specified criteria');
      return;
    }

    // Reset reports to default values first
    await resetReports(domain, dateString);

    // Then recalculate
    await recalculateReports(orders, dateString);

  } catch (error) {
    logger.error('Error in main:', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

// Chạy script
main()
  .then(() => {
    console.log('Script completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  }); 