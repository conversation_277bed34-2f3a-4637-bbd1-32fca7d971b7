import { 
  sendInfoMessage, 
  sendErrorAlert, 
  sendPayPalErrorAlert,
  sendOrderStatusUpdate 
} from '../services/telegramService.js';
import logger from '../utils/logger.js';

async function testTelegramMessages() {
  try {
    // Test Info Message
    await sendInfoMessage('🧪 Test Info Message', {
      type: 'test',
      environment: process.env.NODE_ENV || 'development',
      timestamp: new Date().toISOString()
    });
    console.log('✅ Sent info message');

    // Test Error Alert
    await sendErrorAlert('🧪 Test Error Alert', {
      type: 'test_error',
      code: 'TEST_001',
      details: 'This is a test error message'
    });
    console.log('✅ Sent error alert');

    // Test PayPal Error
    const testPayPalError = new Error('Test PayPal Error');
    testPayPalError.response = {
      data: {
        error: 'INVALID_REQUEST',
        error_description: 'This is a test PayPal error response'
      }
    };
    await sendPayPalErrorAlert(testPayPalError, {
      orderId: 'TEST123',
      clientId: 'TEST_CLIENT',
      action: 'capture'
    });
    console.log('✅ Sent PayPal error alert');

    // Test Order Status Update
    const testOrder = {
      id: 'TEST123',
      domain: 'example.com',
      transaction_id: 'TX_TEST123'
    };
    await sendOrderStatusUpdate(testOrder, 'processing', {
      payment_method: 'PayPal',
      amount: '$99.99',
      customer: 'Test Customer'
    });
    console.log('✅ Sent order status update');

    console.log('✨ All test messages sent successfully');
  } catch (error) {
    logger.error('Error testing Telegram messages:', {
      error: error.message,
      stack: error.stack
    });
    console.error('❌ Error:', error.message);
  }
}

// Chạy test
testTelegramMessages()
  .then(() => {
    console.log('🎉 Test completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Test failed:', error);
    process.exit(1);
  }); 