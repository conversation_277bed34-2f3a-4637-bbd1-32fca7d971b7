import dotenv from 'dotenv';
import axios from 'axios';
import { parseISO, addHours, parse, subHours } from 'date-fns';
import { Op } from 'sequelize';
// import { PayPalTransaction, Order } from '../models/index.js';
import Order from '../models/order.js';
import { formatDate, convertToClientTimezone } from '../utils/utils.js';
import logger from '../utils/logger.js';
import fs from 'fs';
import path from 'path';
import paypalConfig from '../config/paypal.json' with { type: 'json' };
import { writeFile, mkdir } from 'fs/promises';  // Thay đổi import
import { createObjectCsvWriter } from 'csv-writer';
import { sendNewOrderToTrueStore } from "../services/trueStoreService.js";
import config from "../config/config.js";

dotenv.config();

// Các giá trị cố định cho test
const TEST_CONFIG = {
  START_DATE: '2025-01-22T00:00:00Z', // Format ISO string
  END_DATE: '2025-01-23T23:59:59Z',   // Format ISO string
  CLIENT_ID: 'Af1xjXlzIzTv0gUrVCwawkCyemtJTKYifiO4kPs83Cmzj96CFbxTRmCSaRlSpg3xT2JtM3Jl5r-F-QpF',
  CLIENT_SECRET: 'EGsjKvg4y7GacEt5Qtv6uoMsw5zp1JfYvxXoh9AF__k049-urRi2OvPLJosr2jarBMibbkXTCjQGM_Ky',
  PAYPAL_API_URL: 'https://api.paypal.com'
};

// Lọc accounts có client_id_2
const accountsWithSecondary = Object.entries(paypalConfig)
  .filter(([port, _]) => port === '003') // Chỉ lấy account 003
  .reduce((acc, [port, config]) => {
    acc[port] = config;
    return acc;
  }, {});

console.log('Selected account:', accountsWithSecondary);

// Kiểm tra nếu không tìm thấy account
if (Object.keys(accountsWithSecondary).length === 0) {
  console.error('Account 003 not found in config');
  process.exit(1);
}

// Hàm lấy access token
async function getAccessToken(clientId, secretKey) {
  try {
    const auth = Buffer.from(`${clientId}:${secretKey}`).toString('base64');
    
    const response = await axios.post(
      `${TEST_CONFIG.PAYPAL_API_URL}/v1/oauth2/token`,
      'grant_type=client_credentials',
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${auth}`
        }
      }
    );
    console.log("login success ", clientId);

    return response.data.access_token;
  } catch (error) {
    console.error('Error getting access token:', error.message);
    throw error;
  }
}

// Hàm lấy transactions từ Reporting API
async function getPayPalTransactions(startTime, endTime, accessToken, port) {
  try {
    let allTransactions = [];
    let page = 1;
    let hasMore = true;
    while (hasMore) {
      try {
        const response = await axios.get(
          `${TEST_CONFIG.PAYPAL_API_URL}/v1/reporting/transactions`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            },
            params: {
              start_date: startTime.toISOString(),
              end_date: endTime.toISOString(),
              fields: 'transaction_info,payer_info,shipping_info,cart_info',
              page_size: 100,
              page: page
            }
          }
        );

        const transactions = response.data.transaction_details || [];
        allTransactions = allTransactions.concat(transactions);
        
        console.log(`[${port}] Fetched page ${page}, got ${transactions.length} transactions`);
        
        hasMore = transactions.length === 100;
        page++;
      } catch (error) {
        if (error.response?.status === 403) {
          console.log(`[${port}] Access denied (403) - Skipping this account`);
          return null; // Return null để biết là bị 403
        }
        throw error; // Ném các lỗi khác
      }
    }

    return allTransactions;
  } catch (error) {
    console.error(`[${port}] Error fetching PayPal transactions:`, error.message);
    throw error;
  }
}

// Hàm đếm orders
async function countOrders(startDate, endDate) {
  try {
    const count = await Order.count({
      where: {
        // paypal_client_id: 'AYik9q9KjVgRJNhYWSrGdC9GYG1cmdaKFCQ6NwMimVUu6hB-QCI58j1gd9RYPm8E4xTvYmlgrFZIRfe2',
        createdAt: {
          [Op.between]: [startDate, endDate]
        }
      }
    });

    console.log('\n=== Orders Count ===');
    console.log(`Orders with PayPal in date range: ${count}`);
    console.log(`DB Date range: ${formatDate(startDate, 'yyyy-MM-dd HH:mm:ss')} to ${formatDate(endDate, 'yyyy-MM-dd HH:mm:ss')}`);

    return count;
  } catch (error) {
    console.error('Error counting orders:', error.message);
    throw error;
  }
}

// Thêm hàm ghi log
async function writeLog(message, uploadsDir, timestamp) {
  const logPath = path.join(uploadsDir, `fetch_transactions_${timestamp}.log`);
  await writeFile(logPath, message + '\n', { flag: 'a' }); // 'a' để append vào file
  console.log(message); // Vẫn giữ console.log
}

// Hàm kiểm tra invoice_id format
function isValidInvoiceFormat(invoiceId) {
  if (!invoiceId) return false;
  const parts = invoiceId.split('-');
  return parts.length === 3;
}

// Sửa lại fetchTransactions để sử dụng writeLog
const fetchTransactions = async () => {
  try {
    // Initialize uploads directory
    const timestamp = formatDate(new Date(), 'yyyyMMdd_HHmmss');
    const uploadsDir = path.join(process.cwd(), 'uploads');
    await mkdir(uploadsDir, { recursive: true });

    await writeLog('\n=== Starting PayPal Transaction Fetch ===\n', uploadsDir, timestamp);

    const allMissingTransactions = []; // Thêm mảng này để collect tất cả missing transactions

    // Convert ISO string dates to Date objects
    const startDate = new Date(TEST_CONFIG.START_DATE);
    const endDate = new Date(TEST_CONFIG.END_DATE);
    
    await writeLog(`Date Range: ${startDate.toISOString()} to ${endDate.toISOString()}`, uploadsDir, timestamp);

    for (const [port, account] of Object.entries(accountsWithSecondary)) {
      await writeLog(`\n=== Processing Account: ${port} ===`, uploadsDir, timestamp);
      
      try {
        // Lấy access token và transactions như cũ
        const accessToken = await getAccessToken(account.client_id_2, account.secret_key_2);
        const transactions = await getPayPalTransactions(startDate, endDate, accessToken, port);

        // Export to CSV
        await writeTransactionsCsv(transactions, uploadsDir, timestamp, port);

        // Lưu toàn bộ transactions
        await writeFile(
          path.join(uploadsDir, `${port}_total_transactions_${timestamp}.json`),
          JSON.stringify({
            port,
            email: account.email,
            date_range: {
              start: startDate.toISOString(),
              end: endDate.toISOString()
            },
            total_count: transactions.length,
            transactions: transactions.map(tx => ({
              transaction_id: tx.transaction_info.transaction_id,
              invoice_id: tx.transaction_info.invoice_id,
              amount: tx.transaction_info.transaction_amount.value,
              currency: tx.transaction_info.transaction_amount.currency_code,
              status: tx.transaction_info.transaction_status,
              event_code: tx.transaction_info.transaction_event_code,
              event_type: tx.transaction_info.transaction_event_type,
              payer_email: tx.payer_info?.email_address,
              payer_name: tx.payer_info?.payer_name,
              transaction_time: tx.transaction_info.transaction_initiation_date,
              transaction_updated_date: tx.transaction_info.transaction_updated_date
            }))
          }, null, 2)
        );

        // Lọc successful payments
        const successfulPayments = transactions.filter(tx => 
          tx.transaction_info.transaction_event_code === 'T0006' &&
          tx.transaction_info.transaction_status === 'S' &&
          tx.transaction_info.transaction_amount.value > 0
        );

        // Lọc các transactions có invoice_id đúng format
        const validInvoiceTransactions = successfulPayments.filter(tx => 
          isValidInvoiceFormat(tx.transaction_info.invoice_id)
        );

        await writeLog(`\n=== Transactions Analysis for ${port} ===`, uploadsDir, timestamp);
        await writeLog(`Total transactions: ${transactions.length}`, uploadsDir, timestamp);
        await writeLog(`Successful payments: ${successfulPayments.length}`, uploadsDir, timestamp);
        await writeLog(`With valid invoice format: ${validInvoiceTransactions.length}`, uploadsDir, timestamp);

        // Arrays để track các loại transactions
        const unmatchedClientIds = [];
        const missingTransactions = [];
        const matchedOrders = [];

        // Chỉ check missing cho những transactions có invoice_id đúng format
        for (const tx of validInvoiceTransactions) {
          const order = await Order.findOne({
            where: { transaction_id: tx.transaction_info.transaction_id },
            attributes: ['id', 'transaction_id', 'createdAt', 'status', 'paypal_client_id']
          });

          if (!order) {
            const transactionId = tx.transaction_info.transaction_id;
            const [shippingResult, disputeResult] = await Promise.all([
              getShippingDetails(transactionId, accessToken, account),
              getDisputeDetails(transactionId, accessToken, account)
            ]);

            if (!shippingResult.success && !disputeResult.success) {
              missingTransactions.push(tx);
            }
          } else {
            const clientId = order.paypal_client_id;
            if (clientId !== account.client_id && clientId !== null) {
              unmatchedClientIds.push({
                order,  // Lưu order data
                transaction: tx  // Lưu transaction data
              });
            } else {
              matchedOrders.push({
                order,
                transaction: tx
              });
            }
          }
        }

        // 1. All Successful Transactions
        await writeFile(
          path.join(uploadsDir, `${port}_successful_transactions_${timestamp}.json`),
          JSON.stringify({
            port,
            email: account.email,
            date_range: {
              start: startDate.toISOString(),
              end: endDate.toISOString()
            },
            total_count: successfulPayments.length,
            transactions: successfulPayments.map(tx => ({
              transaction_id: tx.transaction_info.transaction_id,
              amount: tx.transaction_info.transaction_amount.value,
              currency: tx.transaction_info.transaction_amount.currency_code,
              status: tx.transaction_info.transaction_status,
              payer_email: tx.payer_info?.email_address,
              transaction_time: tx.transaction_info.transaction_initiation_date
            }))
          }, null, 2)
        );

        // 2. Unmatched Client ID (from successful transactions)
        await writeFile(
          path.join(uploadsDir, `${port}_unmatched_clientid_${timestamp}.json`),
          JSON.stringify({
            port,
            email: account.email,
            date_range: {
              start: startDate.toISOString(),
              end: endDate.toISOString()
            },
            expected_client_id: account.client_id,
            total_count: unmatchedClientIds.length,
            transactions: unmatchedClientIds.map(({ order, transaction }) => ({
              order_info: {
                order_id: order.id,
                transaction_id: order.transaction_id,
                actual_client_id: order.paypal_client_id,
                created_at: formatDate(order.createdAt, 'yyyy-MM-dd HH:mm:ss'),
                status: order.status
              },
              transaction_info: {
                amount: transaction.transaction_info.transaction_amount.value,
                currency: transaction.transaction_info.transaction_amount.currency_code,
                payer_email: transaction.payer_info?.email_address,
                transaction_time: transaction.transaction_info.transaction_initiation_date
              }
            }))
          }, null, 2)
        );

        // 3. Missing Transactions (from successful transactions)
        if (missingTransactions.length > 0) {
          // Fetch complete details for missing transactions
          const completeDetails = await fetchCompleteTransactionDetails(
            missingTransactions, 
            accessToken, 
            port
          );

          // Lọc ra các transactions thực sự missing (không có shipping và dispute)
          const realMissingTransactions = completeDetails.filter(detail => !detail.status.isExcluded);
          const excludedTransactions = completeDetails.filter(detail => detail.status.isExcluded);

          // Lưu missing transactions vào hệ thống
          await writeLog(`\nProcessing ${realMissingTransactions.length} missing transactions for ${port}...`, uploadsDir, timestamp);
          
          let savedCount = 0;
          for (const detail of realMissingTransactions) {
            try {
              const transactionData = {
                transactionId: detail.transaction.transaction_info.transaction_id,
                paymentResult: {
                  data: detail.payment
                },
                orderResult: detail.order ? { data: detail.order } : null,
                shippingResult: detail.shipping ? { data: detail.shipping } : null,
                successfulAccount: port,
                domain: 'smartuscart.com'
              };

              await saveToOrder(transactionData);
              savedCount++;
              await writeLog(`Saved order for transaction ${detail.transaction.transaction_info.transaction_id}`, uploadsDir, timestamp);
            } catch (error) {
              await writeLog(`Error saving order for transaction ${detail.transaction.transaction_info.transaction_id}: ${error.message}`, uploadsDir, timestamp);
              console.error(`Error saving missing order:`, error);
            }
          }

          await writeLog(`\nSaved ${savedCount} out of ${realMissingTransactions.length} missing orders`, uploadsDir, timestamp);

          // Write missing transactions file
          await writeFile(
            path.join(uploadsDir, `${port}_missing_transactions_${timestamp}.json`),
            JSON.stringify({
              port,
              email: account.email,
              date_range: {
                start: startDate.toISOString(),
                end: endDate.toISOString()
              },
              total_missing: missingTransactions.length,
              real_missing: realMissingTransactions.length,
              excluded: excludedTransactions.length,
              saved_to_system: savedCount,
              transactions: completeDetails
            }, null, 2)
          );

          // Chỉ thêm vào CSV những transaction thực sự missing
          allMissingTransactions.push(...realMissingTransactions.map(detail => ({
            detail,
            port
          })));

          // Log kết quả
          await writeLog(`\nResults for ${port}:
            Total transactions checked: ${completeDetails.length}
            Actually missing: ${realMissingTransactions.length}
            With shipping: ${completeDetails.filter(d => d.status.hasShipping).length}
            With dispute: ${completeDetails.filter(d => d.status.hasDispute).length}
          `, uploadsDir, timestamp);
        }

        // Log kết quả files
        await writeLog(`\nResults for ${port} saved to uploads/:`, uploadsDir, timestamp);
        await writeLog(`- Successful Payments (${successfulPayments.length}): ${port}_successful_transactions_${timestamp}.json`, uploadsDir, timestamp);
        await writeLog(`- Valid Invoice Format (${validInvoiceTransactions.length})`, uploadsDir, timestamp);
        await writeLog(`- Unmatched Client IDs (${unmatchedClientIds.length}): ${port}_unmatched_clientid_${timestamp}.json`, uploadsDir, timestamp);
        await writeLog(`- Missing Transactions (${missingTransactions.length}): ${port}_missing_transactions_${timestamp}.json`, uploadsDir, timestamp);
        await writeLog(`- Total Transactions (${transactions.length}): ${port}_total_transactions_${timestamp}.json`, uploadsDir, timestamp);

        // Log warnings
        if (unmatchedClientIds.length > 0) {
          await writeLog(`\nWarning: Found ${unmatchedClientIds.length} successful transactions with incorrect client ID`, uploadsDir, timestamp);
        }
        
        if (missingTransactions.length > 0) {
          await writeLog(`\nWarning: Found ${missingTransactions.length} successful transactions missing from DB`, uploadsDir, timestamp);
        }
      } catch (error) {
        await writeLog(`[${port}] Error: ${error.message}`, uploadsDir, timestamp);
      }
    }

    // Sau khi xử lý xong tất cả các port, ghi file CSV tổng hợp
    if (allMissingTransactions.length > 0) {
      await writeMissingTransactionsCsv(allMissingTransactions, uploadsDir, timestamp);
      await writeLog(`\nCreated consolidated CSV file with ${allMissingTransactions.length} missing transactions`, uploadsDir, timestamp);
    }

    await writeLog('\nScript completed successfully!', uploadsDir, timestamp);

  } catch (error) {
    const errorMessage = `Error in fetchTransactions: ${error.message}`;
    console.error(errorMessage);
    await writeLog(errorMessage, uploadsDir, timestamp);
    throw error;
  }
};

// Hàm phân tích orders
async function analyzeOrders(startDate, endDate, paypalTransactions) {
  try {
    const query = {
      where: {
        createdAt: {
          [Op.between]: [startDate, endDate]
        }
      },
      attributes: ['id', 'transaction_id', 'createdAt', 'status', 'paypal_client_id']
    };

    console.log('\nOrder Query:', JSON.stringify(query, null, 2));
    console.log('Query Date Range:', {
      start: startDate instanceof Date ? startDate.toISOString() : startDate,
      end: endDate instanceof Date ? endDate.toISOString() : endDate
    });

    const dbOrders = await Order.findAll(query);

    // Thống kê chi tiết
    const analysis = {
      totalOrders: dbOrders.length,
      ordersWithTxId: 0,
      ordersWithoutTxId: 0,
      ordersWithMatchingTx: 0,
      ordersWithUnmatchedTx: 0,
      clientIdStats: {
        valid: 0,
        invalid: 0,
        missing: 0,
        breakdown: {}
      },
      unmatchedOrders: [],
      invalidClientIdOrders: []
    };

    for (const order of dbOrders) {
      // Thống kê client ID
      const clientId = order.paypal_client_id;
      if (!clientId) {
        analysis.clientIdStats.missing++;
      } else if (clientId === TEST_CONFIG.CLIENT_ID) {
        analysis.clientIdStats.valid++;
      } else {
        analysis.clientIdStats.invalid++;
        analysis.invalidClientIdOrders.push({
          orderId: order.id,
          invalidClientId: clientId,
          createdAt: formatDate(order.createdAt, 'yyyy-MM-dd HH:mm:ss'),
          status: order.status
        });
      }
      
      // Cập nhật breakdown
      analysis.clientIdStats.breakdown[clientId || 'NO_CLIENT_ID'] = 
        (analysis.clientIdStats.breakdown[clientId || 'NO_CLIENT_ID'] || 0) + 1;

      // Thống kê transaction
      if (order.transaction_id) {
        analysis.ordersWithTxId++;
        const matchingTx = paypalTransactions.find(tx => 
          tx.transaction_info.transaction_id === order.transaction_id
        );
        if (matchingTx) {
          analysis.ordersWithMatchingTx++;
        } else {
          analysis.ordersWithUnmatchedTx++;
          analysis.unmatchedOrders.push({
            orderId: order.id,
            transactionId: order.transaction_id,
            createdAt: formatDate(order.createdAt, 'yyyy-MM-dd HH:mm:ss'),
            status: order.status,
            paypal_client_id: clientId || 'NO_CLIENT_ID'
          });
        }
      } else {
        analysis.ordersWithoutTxId++;
      }
    }

    // In kết quả phân tích
    console.log('\n=== Analysis Results ===');
    console.log(`Total Orders: ${analysis.totalOrders}`);
    console.log('\nClient ID Statistics:');
    console.log(`- Valid Client ID: ${analysis.clientIdStats.valid}`);
    console.log(`- Invalid Client ID: ${analysis.clientIdStats.invalid}`);
    console.log(`- Missing Client ID: ${analysis.clientIdStats.missing}`);
    
    console.log('\nClient ID Breakdown:');
    Object.entries(analysis.clientIdStats.breakdown)
      .sort((a, b) => b[1] - a[1])  // Sort by count descending
      .forEach(([clientId, count]) => {
        console.log(`${clientId}: ${count} orders`);
      });

    console.log('\nTransaction Statistics:');
    console.log(`- With Transaction ID: ${analysis.ordersWithTxId}`);
    console.log(`- Without Transaction ID: ${analysis.ordersWithoutTxId}`);
    console.log(`- Matching PayPal Transaction: ${analysis.ordersWithMatchingTx}`);
    console.log(`- Unmatched Transaction: ${analysis.ordersWithUnmatchedTx}`);

    // Ghi file các orders có invalid client ID
    if (analysis.invalidClientIdOrders.length > 0) {
      const timestamp = formatDate(new Date(), 'yyyyMMdd_HHmmss');
      const fileName = `invalid_client_id_orders_${timestamp}.json`;
      fs.writeFileSync(
        path.join(process.cwd(), fileName),
        JSON.stringify(analysis.invalidClientIdOrders, null, 2)
      );
      console.log(`\nSaved ${analysis.invalidClientIdOrders.length} orders with invalid client ID to ${fileName}`);
    }

    return analysis;
  } catch (error) {
    console.error('Error analyzing orders:', error.message);
    throw error;
  }
}

// Add these functions after getAccessToken
async function getPaymentDetails(transactionId, accessToken, account) {
  try {
    const response = await axios.get(
      `${TEST_CONFIG.PAYPAL_API_URL}/v2/payments/captures/${transactionId}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );
    return { success: true, data: response.data };
  } catch (error) {
    console.error(`Error fetching payment details for ${transactionId}:`, error.message);
    return { success: false, error: error.message };
  }
}

async function getOrderDetails(orderId, accessToken, account) {
  try {
    const response = await axios.get(
      `${TEST_CONFIG.PAYPAL_API_URL}/v2/checkout/orders/${orderId}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );
    return { success: true, data: response.data };
  } catch (error) {
    console.error(`Error fetching order details for ${orderId}:`, error.message);
    return { success: false, error: error.message };
  }
}

// Sửa lại hàm fetchCompleteTransactionDetails
async function fetchCompleteTransactionDetails(missingTransactions, accessToken, port) {
  const completeDetails = [];

  for (const tx of missingTransactions) {
    const transactionId = tx.transaction_info.transaction_id;
    console.log(`Fetching complete details for transaction ${transactionId}`);

    // Get payment details
    const paymentResult = await getPaymentDetails(transactionId, accessToken, port);
    
    let orderResult = null;
    if (paymentResult.success && 
        paymentResult.data.supplementary_data?.related_ids?.order_id) {
      // Get order details if available
      const orderId = paymentResult.data.supplementary_data.related_ids.order_id;
      orderResult = await getOrderDetails(orderId, accessToken, port);
    }

    // Get shipping and dispute details
    const [shippingResult, disputeResult] = await Promise.all([
      getShippingDetails(transactionId, accessToken, port),
      getDisputeDetails(transactionId, accessToken, port)
    ]);

    completeDetails.push({
      transaction: tx,
      payment: paymentResult.success ? paymentResult.data : null,
      order: orderResult?.success ? orderResult.data : null,
      shipping: shippingResult.success ? shippingResult.data : null,
      dispute: disputeResult.success ? disputeResult.data : null,
      status: {
        hasShipping: shippingResult.success,
        hasDispute: disputeResult.success,
        isExcluded: shippingResult.success || disputeResult.success
      }
    });
  }

  return completeDetails;
}

// Xóa writeMissingTransactionsCsv cũ và thay bằng version mới này
async function writeMissingTransactionsCsv(allTransactions, uploadsDir, timestamp) {
  const csvWriter = createObjectCsvWriter({
    path: path.join(uploadsDir, `all_missing_transactions_${timestamp}.csv`),
    header: [
      { id: 'Date', title: 'Date' },
      { id: 'Time', title: 'Time' },
      { id: 'TimeZone', title: 'TimeZone' },
      { id: 'Name', title: 'Name' },
      { id: 'Type', title: 'Type' },
      { id: 'Status', title: 'Status' },
      { id: 'Currency', title: 'Currency' },
      { id: 'Gross', title: 'Gross' },
      { id: 'Fee', title: 'Fee' },
      { id: 'Net', title: 'Net' },
      { id: 'From Email Address', title: 'From Email Address' },
      { id: 'To Email Address', title: 'To Email Address' },
      { id: 'Transaction ID', title: 'Transaction ID' },
      { id: 'Shipping Address', title: 'Shipping Address' },
      { id: 'Address Status', title: 'Address Status' },
      { id: 'Item Title', title: 'Item Title' },
      { id: 'Item ID', title: 'Item ID' },
      { id: 'Shipping and Handling Amount', title: 'Shipping and Handling Amount' },
      { id: 'Insurance Amount', title: 'Insurance Amount' },
      { id: 'Sales Tax', title: 'Sales Tax' },
      { id: 'Option 1 Name', title: 'Option 1 Name' },
      { id: 'Option 1 Value', title: 'Option 1 Value' },
      { id: 'Option 2 Name', title: 'Option 2 Name' },
      { id: 'Option 2 Value', title: 'Option 2 Value' },
      { id: 'Reference Txn ID', title: 'Reference Txn ID' },
      { id: 'Invoice Number', title: 'Invoice Number' },
      { id: 'Custom Number', title: 'Custom Number' },
      { id: 'Quantity', title: 'Quantity' },
      { id: 'Receipt ID', title: 'Receipt ID' },
      { id: 'Balance', title: 'Balance' },
      { id: 'Address Line 1', title: 'Address Line 1' },
      { id: 'Address Line 2/District/Neighborhood', title: 'Address Line 2/District/Neighborhood' },
      { id: 'Town/City', title: 'Town/City' },
      { id: 'State/Province/Region/County/Territory/Prefecture/Republic', title: 'State/Province/Region/County/Territory/Prefecture/Republic' },
      { id: 'Zip/Postal Code', title: 'Zip/Postal Code' },
      { id: 'Country', title: 'Country' },
      { id: 'Contact Phone Number', title: 'Contact Phone Number' },
      { id: 'Subject', title: 'Subject' },
      { id: 'Note', title: 'Note' },
      { id: 'Country Code', title: 'Country Code' },
      { id: 'Balance Impact', title: 'Balance Impact' },
      { id: 'Order ID', title: 'Order ID' },
      { id: 'order_status', title: 'Order Status' },
      { id: 'PayPal Account', title: 'PayPal Account' }
    ]
  });

  const records = allTransactions.map(({ detail, port }) => ({
    'Date': detail.payment?.create_time ? new Date(detail.payment.create_time).toISOString().split('T')[0] : '',
    'Time': detail.payment?.create_time ? new Date(detail.payment.create_time).toISOString().split('T')[1].split('.')[0] : '',
    'TimeZone': 'Z',
    'Name': detail.order?.purchase_units?.[0]?.shipping?.name?.full_name || '',
    'Type': 'Payment',
    'Status': detail.payment?.status || '',
    'Currency': detail.payment?.amount?.currency_code || '',
    'Gross': detail.payment?.amount?.value || '',
    'Fee': detail.payment?.seller_receivable_breakdown?.paypal_fee?.value || '',
    'Net': detail.payment?.seller_receivable_breakdown?.net_amount?.value || '',
    'From Email Address': detail.order?.payer?.email_address || '',
    'To Email Address': detail.order?.purchase_units?.[0]?.payee?.email_address || '',
    'Transaction ID': detail.transaction.transaction_info.transaction_id,
    'Shipping Address': detail.order?.purchase_units?.[0]?.shipping?.address ? 
      `${detail.order.purchase_units[0].shipping.address.address_line_1 || ''}, ${detail.order.purchase_units[0].shipping.address.admin_area_2 || ''}, ${detail.order.purchase_units[0].shipping.address.admin_area_1 || ''}, ${detail.order.purchase_units[0].shipping.address.postal_code || ''}, ${detail.order.purchase_units[0].shipping.address.country_code || ''}` : '',
    'Address Status': '',
    'Item Title': detail.order?.purchase_units?.[0]?.items?.[0]?.name || '',
    'Item ID': detail.order?.purchase_units?.[0]?.items?.[0]?.sku || '',
    'Shipping and Handling Amount': detail.order?.purchase_units?.[0]?.amount?.breakdown?.shipping?.value || '',
    'Insurance Amount': '',
    'Sales Tax': detail.order?.purchase_units?.[0]?.items?.[0]?.tax?.value || '',
    'Option 1 Name': '',
    'Option 1 Value': '',
    'Option 2 Name': '',
    'Option 2 Value': '',
    'Reference Txn ID': detail.order?.id || '',
    'Invoice Number': detail.payment?.invoice_id || '',
    'Custom Number': '',
    'Quantity': detail.order?.purchase_units?.[0]?.items?.[0]?.quantity || '',
    'Receipt ID': '',
    'Balance': '',
    'Address Line 1': detail.order?.purchase_units?.[0]?.shipping?.address?.address_line_1 || '',
    'Address Line 2/District/Neighborhood': detail.order?.purchase_units?.[0]?.shipping?.address?.address_line_2 || '',
    'Town/City': detail.order?.purchase_units?.[0]?.shipping?.address?.admin_area_2 || '',
    'State/Province/Region/County/Territory/Prefecture/Republic': detail.order?.purchase_units?.[0]?.shipping?.address?.admin_area_1 || '',
    'Zip/Postal Code': detail.order?.purchase_units?.[0]?.shipping?.address?.postal_code || '',
    'Country': detail.order?.purchase_units?.[0]?.shipping?.address?.country_code || '',
    'Contact Phone Number': detail.order?.payer?.phone?.phone_number?.national_number || '',
    'Subject': '',
    'Note': detail.order?.purchase_units?.[0]?.description || '',
    'Country Code': detail.order?.payer?.address?.country_code || '',
    'Balance Impact': '',
    'Order ID': detail.order?.id || '',
    'order_status': detail.order?.status || '',
    'PayPal Account': port
  }));

  await csvWriter.writeRecords(records);
  console.log(`CSV file created: all_missing_transactions_${timestamp}.csv`);
}

// Thêm 2 hàm mới
async function getShippingDetails(transactionId, accessToken, account) {
  try {
    const response = await axios.get(
      `${TEST_CONFIG.PAYPAL_API_URL}/v1/shipping/trackers`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        params: {
          transaction_id: transactionId
        }
      }
    );

    if (response.data && response.data.trackers && response.data.trackers.length > 0) {
      return { success: true, data: response.data.trackers[0] };
    } else {
      console.log(`No shipping details found for transaction ${transactionId}`);
      return { success: false, error: 'No shipping details found' };
    }
  } catch (error) {
    console.error(`Error fetching shipping details for ${transactionId}:`, error.message);
    return { success: false, error: error.message };
  }
}

async function getDisputeDetails(transactionId, accessToken, account) {
  try {
    const response = await axios.get(
      `${TEST_CONFIG.PAYPAL_API_URL}/v1/customer/disputes`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
        params: {
          disputed_transaction_id: transactionId
        }
      }
    );

    if (response.data && response.data.items && response.data.items.length > 0) {
      return { success: true, data: response.data.items[0] };
    } else {
      console.log(`No dispute found for transaction ${transactionId}`);
      return { success: false, error: 'No dispute found' };
    }
  } catch (error) {
    console.error(`Error fetching dispute details for ${transactionId}:`, error.message);
    return { success: false, error: error.message };
  }
}

// Thêm hàm saveToOrder
async function saveToOrder(transactionData) {
  try {
    const {
      transactionId,
      paymentResult,
      orderResult,
      shippingResult,
      successfulAccount,
      domain
    } = transactionData;

    // Điều chỉnh thời gian tạo (trừ đi 7 giờ)
    const createdAt = subHours(new Date(paymentResult.data.create_time), 7);

    const orderData = {
      domain,
      transaction_id: transactionId,
      paypal_client_id: accountsWithSecondary[successfulAccount].client_id,
      orderData: {
        total: paymentResult.data.amount?.value,
        billing: {
          city: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.admin_area_2,
          email: orderResult?.data?.payer?.email_address,
          phone: orderResult?.data?.payer?.phone?.phone_number?.national_number,
          state: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.admin_area_1,
          country: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.country_code,
          postcode: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.postal_code,
          address_1: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.address_line_1,
          address_2: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.address_line_2,
          last_name: orderResult?.data?.purchase_units?.[0]?.shipping?.name?.surname,
          first_name: orderResult?.data?.purchase_units?.[0]?.shipping?.name?.given_name
        },
        set_paid: true,
        shipping: {
          city: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.admin_area_2,
          state: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.admin_area_1,
          country: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.country_code,
          postcode: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.postal_code,
          address_1: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.address_line_1,
          address_2: orderResult?.data?.purchase_units?.[0]?.shipping?.address?.address_line_2,
          last_name: orderResult?.data?.purchase_units?.[0]?.shipping?.name?.surname,
          first_name: orderResult?.data?.purchase_units?.[0]?.shipping?.name?.given_name
        },
        meta_data: [
          {
            key: "invoice_id",
            value: paymentResult.data.invoice_id
          },
          {
            key: "_ppcp_paypal_order_id",
            value: transactionId
          },
          {
            key: "_ppcp_paypal_payment_mode",
            value: "live"
          }
        ],
        line_items: orderResult?.data?.purchase_units?.[0]?.items?.map(item => ({
          sku: item.sku,
          name: item.name,
          price: parseFloat(item.unit_amount.value),
          total: (parseFloat(item.unit_amount.value) * parseInt(item.quantity)).toString(),
          quantity: parseInt(item.quantity),
          product_id: item.sku
        })) || [],
        payment_method: "ppcp-gateway",
        shipping_lines: [
          {
            total: orderResult?.data?.purchase_units?.[0]?.amount?.breakdown?.shipping?.value,
            method_id: "flat_rate"
          }
        ],
        shipping_total: orderResult?.data?.purchase_units?.[0]?.amount?.breakdown?.shipping?.value,
        transaction_id: transactionId,
        payment_method_title: "Paypal"
      },
      status: "processing",
      tracking_id: shippingResult?.data?.tracking_number || null,
      port: null,
      paypal_order_id: orderResult?.data?.id || null,
      paypal_checked_at: new Date(),
      createdAt: createdAt,
      updatedAt: new Date()
    };

    const [order, created] = await Order.findOrCreate({
      where: { transaction_id: transactionId },
      defaults: orderData
    });

    if (!created) {
      await order.update(orderData);
      console.log(`Updated existing order for transaction ${transactionId}`);
    } else {
      console.log(`Created new order for transaction ${transactionId}`);
    }

    // Sau khi lưu order, gửi đến TrueStore
    if (!config.disableTrueStoreSync) {
        try {
        await sendNewOrderToTrueStore(order);
        logger.info(`Order sent to TrueStore`, {
            orderId: order.id,
            domain: order.domain,
            action: created ? 'new' : 'existing'
        });
        } catch (trueStoreError) {
        logger.error(`Error sending order to TrueStore`, {
            orderId: order.id,
            domain: order.domain,
            transactionId,
            error: trueStoreError.message,
            stack: trueStoreError.stack
        });
        console.error(`Error sending order to TrueStore for transaction ${transactionId}:`, trueStoreError);
        }
    }

    return order;
  } catch (error) {
    console.error(`Error saving order for transaction ${transactionData.transactionId}:`, error);
    throw error;
  }
}

// Sửa lại hàm writeTransactionsCsv
async function writeTransactionsCsv(transactions, uploadsDir, timestamp, port) {
  const csvWriter = createObjectCsvWriter({
    path: path.join(uploadsDir, `${port}_transactions_${timestamp}.csv`),
    header: [
      { id: 'Date', title: 'Date' },
      { id: 'Time', title: 'Time' },
      { id: 'Type', title: 'Type' },
      { id: 'Status', title: 'Status' },
      { id: 'Currency', title: 'Currency' },
      { id: 'Amount', title: 'Amount' },
      { id: 'FromEmail', title: 'From Email' },
      { id: 'TransactionID', title: 'Transaction ID' },
      { id: 'InvoiceID', title: 'Invoice ID' },
      { id: 'PayerName', title: 'Payer Name' }
    ]
  });

  const records = transactions.map(tx => ({
    'Date': new Date(tx.transaction_info.transaction_initiation_date).toISOString().split('T')[0],
    'Time': new Date(tx.transaction_info.transaction_initiation_date).toISOString().split('T')[1].split('.')[0],
    'Type': tx.transaction_info.transaction_event_type,
    'Status': tx.transaction_info.transaction_status,
    'Currency': tx.transaction_info.transaction_amount.currency_code,
    'Amount': tx.transaction_info.transaction_amount.value,
    'FromEmail': tx.payer_info?.email_address || '',
    'TransactionID': tx.transaction_info.transaction_id,
    'InvoiceID': tx.transaction_info.invoice_id || '',
    'PayerName': tx.payer_info?.payer_name?.given_name + ' ' + tx.payer_info?.payer_name?.surname || ''
  }));

  await csvWriter.writeRecords(records);
  console.log(`CSV file created: ${port}_transactions_${timestamp}.csv`);
}

// Chạy script
const run = async () => {
  try {
    await fetchTransactions();
    console.log('\nScript completed successfully!');
  } catch (error) {
    console.error('Script failed:', error);
  } finally {
    process.exit();
  }
};

run();
