import { readFileSync, existsSync, writeFileSync, mkdirSync } from 'fs';
import fetch from 'node-fetch';
import { join } from 'path';

const API_URL = 'http://45.77.102.50:3006/api/update-tracking-without-email';
const AUTH = 'Basic ' + Buffer.from('devtruestore:pas72ns2ws5ord').toString('base64');

async function updateTracking(tracking, trackingNumber) {
    try {
        const response = await fetch(API_URL, {
            method: 'POST',
            headers: {
                'Authorization': AUTH,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                transaction_id: tracking,
                tracking_number: trackingNumber
            })
        });

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error updating tracking:', error);
        return null;
    }
}

async function processTrackingData(trackingData) {
    const date = new Date().toISOString().split('T')[0];
    const logDir = './data/updateTracking_08_11';
    const successLog = join(logDir, `success_${date}.log`);
    const errorLog = join(logDir, `error_${date}.log`);

    // Tạo thư mục nếu chưa tồn tại
    if (!existsSync(logDir)) {
        mkdirSync(logDir, { recursive: true });
    }

    for (const item of trackingData) {
        console.log(`Processing: ${item.tracking} - ${item.trackingNumber}`);
        const result = await updateTracking(item.tracking, item.trackingNumber);
        
        const logEntry = `${new Date().toISOString()} - ${item.tracking} - ${item.trackingNumber} - ${JSON.stringify(result)}\n`;
        
        if (result && result.port) {
            // Ghi log thành công
            writeFileSync(successLog, logEntry, { flag: 'a' });
            console.log('Success:', result);
        } else {
            // Ghi log thất bại
            writeFileSync(errorLog, logEntry, { flag: 'a' });
            console.log('Error:', result);
        }

        await new Promise(resolve => setTimeout(resolve, 5000));
    }
}

function parseTrackingFile(filePath) {
    // Kiểm tra file có tồn tại không
    if (!existsSync(filePath)) {
        console.error('File not found:', filePath);
        return [];
    }

    try {
        // Đọc nội dung file
        const fileContent = readFileSync(filePath, 'utf8');
        
        // Tách thành từng dòng
        const lines = fileContent.split('\n');
        
        // Mảng để lưu kết quả
        const trackingData = [];
        
        // Xử lý từng dòng
        lines.forEach(line => {
            // Bỏ qua dòng trống
            if (line.trim() === '') return;
            
            // Tách dòng thành 2 phần bằng tab hoặc khoảng trắng
            const [tracking, trackingNumber] = line.trim().split(/\s+/);
            
            // Chỉ thêm vào mảng nếu có cả 2 giá trị
            if (tracking && trackingNumber) {
                trackingData.push({
                    tracking,
                    trackingNumber
                });
            }
        });

        return trackingData;
        
    } catch (error) {
        console.error('Error reading file:', error);
        return [];
    }
}

// Sử dụng function
const filePath = './data/updateTracking_08_11.txt';

if (existsSync(filePath)) {
    const result = parseTrackingFile(filePath);
    console.log('Total records:', result.length);
    processTrackingData(result).then(() => {
        console.log('All records processed');
    });
} else {
    console.error('File not found:', filePath);
}