#!/usr/bin/env node

import Redis from 'ioredis';
import dotenv from 'dotenv';

dotenv.config();

async function checkRedisConnection() {
  console.log('🔍 Checking Redis Connection Information');
  console.log('=====================================');
  
  const redisUrl = process.env.REDIS_URL;
  const enableRedis = process.env.ENABLE_REDIS === "1";
  
  console.log(`📋 Environment Variables:`);
  console.log(`   REDIS_URL: ${redisUrl || 'undefined'}`);
  console.log(`   ENABLE_REDIS: ${enableRedis}`);
  
  if (!enableRedis) {
    console.log('❌ Redis is disabled in environment');
    return;
  }
  
  try {
    // Create Redis client (same as in cache.js)
    const redis = new Redis(redisUrl);
    
    redis.on('connect', async () => {
      console.log('\n✅ Successfully connected to Redis!');
      
      // Get connection info
      console.log(`📍 Connection Details:`);
      console.log(`   Host: ${redis.options.host}`);
      console.log(`   Port: ${redis.options.port}`);
      console.log(`   Database: ${redis.options.db}`);
      console.log(`   Family: ${redis.options.family}`);
      
      // Get server info
      try {
        const serverInfo = await redis.info('server');
        const lines = serverInfo.split('\r\n');
        
        console.log(`\n🖥️  Server Information:`);
        lines.forEach(line => {
          if (line.includes('redis_version:')) {
            console.log(`   Redis Version: ${line.split(':')[1]}`);
          }
          if (line.includes('tcp_port:')) {
            console.log(`   TCP Port: ${line.split(':')[1]}`);
          }
          if (line.includes('uptime_in_seconds:')) {
            const uptime = parseInt(line.split(':')[1]);
            const hours = Math.floor(uptime / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            console.log(`   Uptime: ${hours}h ${minutes}m`);
          }
        });
        
        // Test performance
        console.log(`\n⚡ Performance Test:`);
        const testKey = 'connection_test_key';
        const testValue = { test: 'data', timestamp: Date.now() };
        
        const setStart = Date.now();
        await redis.setex(testKey, 60, JSON.stringify(testValue));
        const setDuration = Date.now() - setStart;
        
        const getStart = Date.now();
        const result = await redis.get(testKey);
        const getDuration = Date.now() - getStart;
        
        await redis.del(testKey);
        
        console.log(`   SET operation: ${setDuration}ms`);
        console.log(`   GET operation: ${getDuration}ms`);
        
        if (setDuration > 50 || getDuration > 50) {
          console.warn('   ⚠️  High latency detected!');
        } else {
          console.log('   ✅ Performance is good');
        }
        
        // Check memory usage
        const memoryInfo = await redis.info('memory');
        const memoryLines = memoryInfo.split('\r\n');
        
        console.log(`\n💾 Memory Information:`);
        memoryLines.forEach(line => {
          if (line.includes('used_memory_human:')) {
            console.log(`   Used Memory: ${line.split(':')[1]}`);
          }
          if (line.includes('used_memory_peak_human:')) {
            console.log(`   Peak Memory: ${line.split(':')[1]}`);
          }
        });
        
        // Check database size
        const dbSize = await redis.dbsize();
        console.log(`   Total Keys: ${dbSize}`);
        
      } catch (infoError) {
        console.error('❌ Error getting server info:', infoError.message);
      }
      
      redis.disconnect();
      process.exit(0);
    });
    
    redis.on('error', (err) => {
      console.error('❌ Redis connection error:', err.message);
      process.exit(1);
    });
    
  } catch (error) {
    console.error('❌ Failed to create Redis client:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  checkRedisConnection();
}

export { checkRedisConnection };
