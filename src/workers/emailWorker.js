import amqp from 'amqplib';
import { sendOrderConfirmationEmail, sendShippingConfirmationEmail } from '../services/emailServiceAws.js';
import logger from "../utils/logger.js";
import { QUEUES } from '../config/rabbitmq.js';
import 'dotenv/config';

const RABBITMQ_URL = 'amqp://devtruestore:vippro2024%401212@*************:5673';
const INITIAL_RETRY_DELAY = 10000; // 10 seconds
const MAX_RETRY_DELAY = 60000; // 1 minute
const MAX_RETRIES = 5;

async function startEmailWorker() {
  try {
    const connection = await amqp.connect(RABBITMQ_URL);
    const channel = await connection.createChannel();
    await channel.assertQueue(QUEUES.EMAIL, { durable: true });
    logger.info(`Email worker is waiting for messages on queue: ${QUEUES.EMAIL}`);

    await channel.prefetch(1);

    channel.consume(QUEUES.EMAIL, async (msg) => {
      if (msg !== null) {
        const emailData = JSON.parse(msg.content.toString());
        logger.info('Received email task:', { type: emailData.type });

        let retryCount = msg.properties.headers['x-retry-count'] || 0;
        let retryDelay = INITIAL_RETRY_DELAY;

        const sendEmail = async () => {
          try {
            // Validate emailData structure
            if (!emailData) {
              throw new Error("Email data is missing");
            }
            
            if (!emailData.data) {
              throw new Error("Email data payload is missing");
            }
            
            switch (emailData.type) {
              case 'order_confirmation':
                await sendOrderConfirmationEmail(emailData.data);
                break;
              case 'shipping_confirmation':
                await sendShippingConfirmationEmail(emailData.data);
                break;
              default:
                logger.warn('Unknown email type received', { type: emailData.type });
                channel.ack(msg); // Remove unknown type from queue
                return;
            }
            logger.info('Email sent successfully for:', { 
              type: emailData.type,
              recipient: emailData.data?.email || emailData.data?.to,
              orderId: emailData.data?.orderId || emailData.data?.order?.id
            });
            channel.ack(msg);
          } catch (error) {
            // Handle validation errors (permanent failures)
            if (error.message.includes('data structure is invalid') || 
                error.message.includes('is missing') ||
                error.message.includes('Email data')) {
              logger.error('Data validation error, removing from queue:', { 
                error: error.message, 
                stack: error.stack,
                type: emailData.type,
                recipient: emailData.data?.email || emailData.data?.to,
                orderId: emailData.data?.orderId || emailData.data?.order?.id,
                emailData: emailData
              });
              channel.ack(msg); // Remove from queue as it's a permanent failure
              return;
            }
            
            if (error.name === 'ThrottlingException') {
              if (retryCount < MAX_RETRIES) {
                retryCount++;
                logger.warn('AWS SES rate limit reached, retrying', { 
                  type: emailData.type, 
                  retryCount, 
                  retryDelay,
                  recipient: emailData.data?.email || emailData.data?.to,
                  orderId: emailData.data?.orderId || emailData.data?.order?.id,
                  error: error.message,
                  errorName: error.name
                });
                
                setTimeout(() => {
                  sendEmail();
                }, retryDelay);

                // Increase retry delay for next attempt, but cap it
                retryDelay = Math.min(retryDelay * 2, MAX_RETRY_DELAY);
              } else {
                logger.error('Max retries reached, removing from queue', { 
                  type: emailData.type, 
                  retryCount,
                  recipient: emailData.data?.email || emailData.data?.to,
                  orderId: emailData.data?.orderId || emailData.data?.order?.id,
                  error: error.message,
                  errorName: error.name
                });
                channel.ack(msg);
              }
            } else if (error.name === 'MessageRejected') {
              logger.error('Email rejected by AWS SES', { 
                error: error.message, 
                stack: error.stack,
                type: emailData.type,
                recipient: emailData.data?.email || emailData.data?.to,
                orderId: emailData.data?.orderId || emailData.data?.order?.id,
                errorName: error.name
              });
              channel.ack(msg); // Acknowledge and remove from queue as it's a permanent failure
            } else {
              logger.error('Error sending email:', { 
                error: error.message, 
                stack: error.stack,
                type: emailData.type,
                recipient: emailData.data?.email || emailData.data?.to,
                orderId: emailData.data?.orderId || emailData.data?.order?.id,
                errorName: error.name
              });
              channel.nack(msg, false, true);
            }
          }
        };

        sendEmail();
      }
    });
  } catch (error) {
    logger.error('Error starting email worker:', { error: error.message, stack: error.stack });
  }
}

startEmailWorker();
