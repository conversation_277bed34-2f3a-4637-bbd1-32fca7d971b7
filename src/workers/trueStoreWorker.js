import amqp from 'amqplib';
import { sendNewOrderToTrueStore } from '../services/trueStoreService.js';
import logger from "../utils/logger.js";
import { QUEUES } from '../config/rabbitmq.js';
import 'dotenv/config';

const RABBITMQ_URL = 'amqp://devtruestore:vippro2024%401212@*************:5673';
const INITIAL_RETRY_DELAY = 10000; // 10 seconds
const MAX_RETRY_DELAY = 60000; // 1 minute
const MAX_RETRIES = 5;

async function startTrueStoreWorker() {
  try {
    const connection = await amqp.connect(RABBITMQ_URL);
    const channel = await connection.createChannel();
    await channel.assertQueue(QUEUES.TRUESTORE, { durable: true });
    logger.info(`TrueStore worker is waiting for messages on queue: ${QUEUES.TRUESTORE}`);

    await channel.prefetch(1);

    channel.consume(QUEUES.TRUESTORE, async (msg) => {
      if (msg !== null) {
        const trueStoreData = JSON.parse(msg.content.toString());
        logger.info('Received TrueStore task:', { type: trueStoreData.type });

        let retryCount = msg.properties.headers['x-retry-count'] || 0;
        let retryDelay = INITIAL_RETRY_DELAY;

        const sendToTrueStore = async () => {
          try {
            // Validate trueStoreData structure
            if (!trueStoreData) {
              throw new Error("TrueStore data is missing");
            }
            
            if (!trueStoreData.data) {
              throw new Error("TrueStore data payload is missing");
            }
            
            switch (trueStoreData.type) {
              case 'new_order':
                await sendNewOrderToTrueStore(trueStoreData.data.order, trueStoreData.data.stats);
                break;
              default:
                logger.warn('Unknown TrueStore type received', { type: trueStoreData.type });
                channel.ack(msg); // Remove unknown type from queue
                return;
            }
            logger.info('Order sent to TrueStore successfully for:', { 
              type: trueStoreData.type,
              orderId: trueStoreData.data.order?.id,
              orderKey: trueStoreData.data.order?.order_key,
              domain: trueStoreData.data.order?.domain
            });
            channel.ack(msg);
          } catch (error) {
            // Handle validation errors (permanent failures)
            if (error.message.includes('data structure is invalid') || 
                error.message.includes('is missing') ||
                error.message.includes('TrueStore data')) {
              logger.error('Data validation error, removing from queue:', { 
                error: error.message, 
                stack: error.stack,
                type: trueStoreData.type,
                orderId: trueStoreData.data?.order?.id,
                trueStoreData: trueStoreData
              });
              channel.ack(msg); // Remove from queue as it's a permanent failure
              return;
            }
            
            // Handle network/temporary errors with retry logic
            if (error.code === 'ECONNRESET' || 
                error.code === 'ETIMEDOUT' || 
                error.code === 'ENOTFOUND' ||
                error.message.includes('fetch')) {
              if (retryCount < MAX_RETRIES) {
                retryCount++;
                logger.warn('Network error, retrying TrueStore sync', { 
                  type: trueStoreData.type, 
                  retryCount, 
                  retryDelay,
                  orderId: trueStoreData.data?.order?.id,
                  orderKey: trueStoreData.data?.order?.order_key,
                  error: error.message,
                  errorCode: error.code
                });
                
                setTimeout(() => {
                  sendToTrueStore();
                }, retryDelay);

                // Increase retry delay for next attempt, but cap it
                retryDelay = Math.min(retryDelay * 2, MAX_RETRY_DELAY);
              } else {
                logger.error('Max retries reached, removing from queue', { 
                  type: trueStoreData.type, 
                  retryCount,
                  orderId: trueStoreData.data?.order?.id,
                  orderKey: trueStoreData.data?.order?.order_key,
                  error: error.message,
                  errorCode: error.code
                });
                channel.ack(msg);
              }
            } else {
              logger.error('Error sending to TrueStore:', { 
                error: error.message, 
                stack: error.stack,
                type: trueStoreData.type,
                orderId: trueStoreData.data?.order?.id,
                orderKey: trueStoreData.data?.order?.order_key,
                errorCode: error.code
              });
              channel.nack(msg, false, true);
            }
          }
        };

        sendToTrueStore();
      }
    });
  } catch (error) {
    logger.error('Error starting TrueStore worker:', { error: error.message, stack: error.stack });
  }
}

startTrueStoreWorker(); 