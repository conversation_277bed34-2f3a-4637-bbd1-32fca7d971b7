import express from "express";
import dotenv from "dotenv";
import cors from "cors"; // Import cors
import { fetchData } from "./utils/httpClient.js";
import cacheRoutes from "./routes/cacheRoutes.js";
import logger from "./utils/logger.js";

dotenv.config();

const app = express();
const port = process.env.LOCAL_PORT || 3006;

app.use(cors()); // Use cors middleware to allow all origins
app.use(express.json());

// Use cache routes
app.use(cacheRoutes);

app.get("/proxy", async (req, res) => {
  const { url } = req.query;
  const domain = req.headers["x-domain"];
  if (!url) {
    return res.status(400).send("url is required");
  }

  try {
    const options = {};
    if (domain) {
      options.headers = { "x-domain": domain };
    }
    const data = await fetchData(url, options);
    res.json(data);
  } catch (error) {
    console.log(error);
    logger.error("Error fetching data from proxy", {
      message: error.message,
      stack: error.stack,
    });
    res.status(500).send("Internal Server Error");
  }
});



app.listen(port, async () => {
  try {
    console.log(
      "Connection to the local database has been established successfully."
    );
    console.log(`Local server is running on http://localhost:${port}`);
  } catch (error) {
    logger.error("Unable to connect to the local database", {
      message: error.message,
      stack: error.stack,
    });
    console.error("Unable to connect to the local database:", error);
  }
});
