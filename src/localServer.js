import express from "express";
import dotenv from "dotenv";
import sequelize from "./config/db.js";
import orderRoutes from "./routes/orderRoutes.js";
import orderRoutesV2 from "./routes/orderRoutesV2.js";
import routesRemote from "./routes/routesRemote.js";
import { fetchData } from "./utils/httpClient.js";
import { clearCacheByTag, getCacheByTag } from "./config/cache.js";
import logger from "./utils/logger.js"; // Import logger

dotenv.config();

const app = express();
const port = process.env.LOCAL_PORT || 3006;

app.use(express.json());

app.use("/api", orderRoutes);
app.use("/", orderRoutesV2); // V2 routes already include full path
app.use("/api", routesRemote);

app.listen(port, async () => {
  try {
    await sequelize.authenticate();
    // await sequelize.sync({ alter: true });
    console.log(
      "Connection to the local database has been established successfully.",
    );
    console.log(`Local server is running on http://localhost:${port}`);
  } catch (error) {
    logger.error("Unable to connect to the local database", {
      message: error.message,
      stack: error.stack,
    });
    console.error("Unable to connect to the local database:", error);
  }
});
