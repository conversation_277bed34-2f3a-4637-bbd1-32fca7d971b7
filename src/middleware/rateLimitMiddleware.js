import rateLimit from 'express-rate-limit';
import logger from '../utils/logger.js';

// Rate limiting for public API endpoints
export const publicApiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP',
    details: 'Please try again later. Rate limit: 100 requests per 15 minutes.'
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  handler: (req, res) => {
    logger.warn('Rate limit exceeded for public API', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
      method: req.method
    });
    
    res.status(429).json({
      error: 'Too many requests from this IP',
      details: 'Please try again later. Rate limit: 100 requests per 15 minutes.',
      retryAfter: Math.round(req.rateLimit.resetTime / 1000)
    });
  },
  skip: (req) => {
    // Skip rate limiting for localhost in development
    if (process.env.NODE_ENV === 'development' && req.ip === '127.0.0.1') {
      return true;
    }
    return false;
  }
});

// More restrictive rate limiting for order check endpoint specifically
export const orderCheckRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 20, // Limit each IP to 20 order checks per 5 minutes
  message: {
    error: 'Too many order check requests from this IP',
    details: 'Please try again later. Rate limit: 20 requests per 5 minutes.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn('Order check rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      orderKey: req.params.orderKey,
      domain: req.get('x-domain')
    });
    
    res.status(429).json({
      error: 'Too many order check requests from this IP',
      details: 'Please try again later. Rate limit: 20 requests per 5 minutes.',
      retryAfter: Math.round(req.rateLimit.resetTime / 1000)
    });
  },
  skip: (req) => {
    // Skip rate limiting for localhost in development
    if (process.env.NODE_ENV === 'development' && req.ip === '127.0.0.1') {
      return true;
    }
    return false;
  }
});
