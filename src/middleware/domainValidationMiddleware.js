import Order from '../models/order.js';
import logger from '../utils/logger.js';

/**
 * Middleware to validate x-domain header against order's domain
 * This ensures that only the correct domain can access order information
 */
export const validateDomainForOrder = async (req, res, next) => {
  try {
    const { orderKey } = req.params;
    const requestDomain = req.get('x-domain');

    // Check if x-domain header is provided
    if (!requestDomain) {
      logger.warn('Missing x-domain header in public order request', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        orderKey,
        path: req.path
      });
      
      return res.status(400).json({
        error: 'Missing required header',
        details: 'x-domain header is required to access order information'
      });
    }

    // Check if orderKey is provided
    if (!orderKey) {
      return res.status(400).json({
        error: 'Missing order key',
        details: 'Order key is required in the URL path'
      });
    }

    // // Find the order by order_key
    // const order = await Order.findOne({
    //   where: { order_key: orderKey },
    //   attributes: ['id', 'domain', 'order_key'] // Only select necessary fields for validation
    // });

    // if (!order) {
    //   logger.warn('Order not found for public request', {
    //     ip: req.ip,
    //     userAgent: req.get('User-Agent'),
    //     orderKey,
    //     requestDomain,
    //     path: req.path
    //   });
      
    //   return res.status(404).json({
    //     error: 'Order not found',
    //     details: 'No order found with the provided order key'
    //   });
    // }

    // // Validate domain match
    // if (order.domain !== requestDomain) {
    //   logger.warn('Domain mismatch in public order request', {
    //     ip: req.ip,
    //     userAgent: req.get('User-Agent'),
    //     orderKey,
    //     requestDomain,
    //     orderDomain: order.domain,
    //     path: req.path
    //   });
      
    //   return res.status(403).json({
    //     error: 'Domain access denied',
    //     details: 'The provided domain does not match the order domain'
    //   });
    // }

    // // Store order info in request for use in controller
    // req.validatedOrder = {
    //   id: order.id,
    //   domain: order.domain,
    //   order_key: order.order_key
    // };

    // logger.info('Domain validation successful for public order request', {
    //   ip: req.ip,
    //   orderKey,
    //   domain: requestDomain,
    //   orderId: order.id
    // });

    next();
  } catch (error) {
    logger.error('Error in domain validation middleware', {
      message: error.message,
      stack: error.stack,
      orderKey: req.params.orderKey,
      requestDomain: req.get('x-domain'),
      ip: req.ip
    });
    
    res.status(500).json({
      error: 'Internal server error',
      details: 'An error occurred while validating the request'
    });
  }
};
