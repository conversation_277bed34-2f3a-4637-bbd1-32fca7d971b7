import logger from '../utils/logger.js';

// Performance metrics collector
class PerformanceMetrics {
  constructor() {
    this.metrics = new Map();
    this.startTimes = new Map();
  }

  startTimer(key) {
    this.startTimes.set(key, Date.now());
  }

  endTimer(key, metadata = {}) {
    const startTime = this.startTimes.get(key);
    if (!startTime) return null;

    const duration = Date.now() - startTime;
    this.startTimes.delete(key);

    // Store metric
    if (!this.metrics.has(key)) {
      this.metrics.set(key, []);
    }
    
    this.metrics.get(key).push({
      duration,
      timestamp: Date.now(),
      metadata,
      process: process.pid
    });

    // Keep only last 100 measurements per key
    const measurements = this.metrics.get(key);
    if (measurements.length > 100) {
      measurements.splice(0, measurements.length - 100);
    }

    return duration;
  }

  getStats(key) {
    const measurements = this.metrics.get(key);
    if (!measurements || measurements.length === 0) {
      return null;
    }

    const durations = measurements.map(m => m.duration);
    const avg = durations.reduce((a, b) => a + b, 0) / durations.length;
    const min = Math.min(...durations);
    const max = Math.max(...durations);
    const recent = measurements.slice(-10).map(m => m.duration);
    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;

    return {
      count: measurements.length,
      average: Math.round(avg),
      min,
      max,
      recentAverage: Math.round(recentAvg),
      lastMeasurement: measurements[measurements.length - 1]
    };
  }

  getAllStats() {
    const stats = {};
    for (const [key, _] of this.metrics) {
      stats[key] = this.getStats(key);
    }
    return stats;
  }

  logSlowOperations(threshold = 1000) {
    for (const [key, measurements] of this.metrics) {
      const slowOps = measurements.filter(m => m.duration > threshold);
      if (slowOps.length > 0) {
        logger.warn(`Slow operations detected for ${key}:`, {
          count: slowOps.length,
          threshold,
          slowest: Math.max(...slowOps.map(op => op.duration)),
          recent: slowOps.slice(-5)
        });
      }
    }
  }
}

// Global metrics instance
const performanceMetrics = new PerformanceMetrics();

// Express middleware for request timing
export const requestTimingMiddleware = (req, res, next) => {
  const requestId = `${req.method}_${req.path}_${Date.now()}_${Math.random()}`;
  
  performanceMetrics.startTimer(`request_${req.method}_${req.path}`);
  performanceMetrics.startTimer(requestId);

  // Override res.end to capture timing
  const originalEnd = res.end;
  res.end = function(...args) {
    const duration = performanceMetrics.endTimer(requestId, {
      method: req.method,
      path: req.path,
      statusCode: res.statusCode,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });

    performanceMetrics.endTimer(`request_${req.method}_${req.path}`, {
      statusCode: res.statusCode
    });

    // Log slow requests
    if (duration > 5000) {
      logger.warn('Slow request detected:', {
        method: req.method,
        path: req.path,
        duration: `${duration}ms`,
        statusCode: res.statusCode,
        process: process.pid
      });
    }

    originalEnd.apply(this, args);
  };

  next();
};

// PayPal operation timing wrapper
export const timePayPalOperation = (operationName) => {
  return (target, propertyKey, descriptor) => {
    const originalMethod = descriptor.value;

    descriptor.value = async function(...args) {
      const timerId = `paypal_${operationName}_${Date.now()}`;
      performanceMetrics.startTimer(timerId);
      performanceMetrics.startTimer(`paypal_${operationName}`);

      try {
        const result = await originalMethod.apply(this, args);
        
        const duration = performanceMetrics.endTimer(timerId, {
          operation: operationName,
          success: true,
          args: args.length
        });

        performanceMetrics.endTimer(`paypal_${operationName}`, {
          success: true
        });

        // Log slow PayPal operations
        if (duration > 3000) {
          logger.warn(`Slow PayPal ${operationName} operation:`, {
            duration: `${duration}ms`,
            process: process.pid
          });
        }

        return result;
      } catch (error) {
        const duration = performanceMetrics.endTimer(timerId, {
          operation: operationName,
          success: false,
          error: error.message
        });

        performanceMetrics.endTimer(`paypal_${operationName}`, {
          success: false
        });

        logger.error(`PayPal ${operationName} operation failed:`, {
          duration: `${duration}ms`,
          error: error.message,
          process: process.pid
        });

        throw error;
      }
    };

    return descriptor;
  };
};

// Function to manually time operations
export const timeOperation = async (operationName, operation, metadata = {}) => {
  const timerId = `${operationName}_${Date.now()}`;
  performanceMetrics.startTimer(timerId);
  performanceMetrics.startTimer(operationName);

  try {
    const result = await operation();
    
    const duration = performanceMetrics.endTimer(timerId, {
      ...metadata,
      success: true
    });

    performanceMetrics.endTimer(operationName, {
      success: true
    });

    return { result, duration };
  } catch (error) {
    const duration = performanceMetrics.endTimer(timerId, {
      ...metadata,
      success: false,
      error: error.message
    });

    performanceMetrics.endTimer(operationName, {
      success: false
    });

    throw error;
  }
};

// Performance report generator
export const generatePerformanceReport = () => {
  const stats = performanceMetrics.getAllStats();
  const report = {
    timestamp: new Date().toISOString(),
    process: process.pid,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    operations: stats
  };

  // Identify performance issues
  const issues = [];
  for (const [operation, stat] of Object.entries(stats)) {
    if (stat.average > 2000) {
      issues.push({
        operation,
        issue: 'High average response time',
        average: stat.average,
        max: stat.max
      });
    }
    
    if (stat.max > 10000) {
      issues.push({
        operation,
        issue: 'Very slow operation detected',
        max: stat.max
      });
    }
  }

  report.issues = issues;
  return report;
};

// Periodic performance logging
export const startPerformanceLogging = (intervalMs = 60000) => {
  setInterval(() => {
    const report = generatePerformanceReport();
    
    if (report.issues.length > 0) {
      logger.warn('Performance issues detected:', report);
    } else {
      logger.info('Performance report:', {
        process: report.process,
        uptime: `${Math.round(report.uptime)}s`,
        operationCount: Object.keys(report.operations).length,
        memoryUsage: `${Math.round(report.memory.heapUsed / 1024 / 1024)}MB`
      });
    }

    // Log slow operations
    performanceMetrics.logSlowOperations(2000);
  }, intervalMs);
};

export { performanceMetrics };
