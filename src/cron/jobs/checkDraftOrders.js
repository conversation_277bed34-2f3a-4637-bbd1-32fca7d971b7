import { Op } from "sequelize";
import { subHours } from "date-fns";
import Order from "../../models/order.js";
import OrderCheck from "../../models/orderCheck.js";

import { checkPaymentStatus } from "../../services/payPalService.js";
import { sendToQueue } from "../../config/rabbitmq.js";
import config from "../../config/config.js";
import logger from "../../utils/logger.js";
import Sequelize from "sequelize";

export async function checkDraftOrders() {
  try {
    const now = new Date();
    const utcMinus7 = subHours(now, 7);
    const twentyFourHoursAgo = subHours(utcMinus7, 24);

    logger.info("Checking orders from:", {
      fromTime: twentyFourHoursAgo.toISOString(),
      toTime: utcMinus7.toISOString(),
    });
    console.log("Checking orders from:", {
      fromTime: twentyFourHoursAgo.toISOString(),
      toTime: utcMinus7.toISOString(),
    });

    const ordersToCheck = await Order.findAll({
      where: {
        status: 'draft',
        paypal_order_id: {
          [Op.ne]: null
        },
        createdAt: {
          [Op.between]: [twentyFourHoursAgo, utcMinus7]
        }
      },
      include: [{
        model: OrderCheck,
        as: 'OrderCheck',
        required: false
      }],
      having: Sequelize.literal('OrderCheck.id IS NULL')  // Chỉ lấy những đơn chưa có OrderCheck
    });

    logger.info(`Found ${ordersToCheck.length} draft orders to check`, {
      timeRange: {
        from: twentyFourHoursAgo.toISOString(),
        to: utcMinus7.toISOString()
      }
    });

    for (const order of ordersToCheck) {
      let orderCheck = order.OrderCheck;

      if (!orderCheck) {
        orderCheck = await OrderCheck.create({
          orderId: order.id,
          paypal_order_id: order.paypal_order_id,
          status: "pending",
        });
      }

      try {
        const paymentStatus = await checkPaymentStatus(
          order.paypal_order_id,
          order.paypal_client_id
        );

        if (paymentStatus === "COMPLETED") {
          // Cập nhật trạng thái đơn hàng
          const updatedOrder = await order.update({ status: "processing" });
          await orderCheck.update({
            status: "completed",
            paypal_status: paymentStatus,
            check_count: orderCheck.check_count + 1,
          });

          logger.info(`Updated order ${order.id} status to processing`);

          // Gửi email xác nhận đơn hàng
          if (!config.disableEmailSending) {
            try {
              await sendToQueue("EMAIL", "order_confirmation", {
                id: updatedOrder.id,
                order_key: updatedOrder.order_key,
                domain: updatedOrder.domain,
                transaction_id: updatedOrder.transaction_id,
                orderData: updatedOrder.orderData,
                status: updatedOrder.status,
                createdAt: updatedOrder.createdAt,
                updatedAt: updatedOrder.updatedAt,
              });
              logger.info("Order confirmation email queued", {
                orderId: updatedOrder.id,
              });
            } catch (emailError) {
              logger.error("Failed to queue order confirmation email", {
                error: emailError.message,
                stack: emailError.stack,
                orderId: updatedOrder.id,
              });
            }
          }

          // Gửi đơn hàng đến TrueStore
          if (!config.disableTrueStoreSync) {
            try {
              await sendToQueue("TRUESTORE", "new_order", {
                order: updatedOrder,
                stats: null // stats not available in cron job
              });
              logger.info("Order queued for TrueStore", {
                orderId: updatedOrder.id,
                orderKey: updatedOrder.order_key,
                domain: updatedOrder.domain,
              });
            } catch (trueStoreError) {
              logger.error("Error queueing order for TrueStore", {
                orderId: updatedOrder.id,
                orderKey: updatedOrder.order_key,
                domain: updatedOrder.domain,
                error: trueStoreError.message,
                stack: trueStoreError.stack,
              });
            }
          }
        } else {
          await orderCheck.update({
            paypal_status: paymentStatus,
            check_count: orderCheck.check_count + 1,
          });

          logger.info(`Order ${order.id} payment status: ${paymentStatus}`);
        }
      } catch (error) {
        // Xử lý các loại lỗi khác nhau
        if (error.name === "RESOURCE_NOT_FOUND") {
          logger.warn(`PayPal order not found`, {
            orderId: order.id,
            paypalOrderId: order.paypal_order_id,
            clientId: order.paypal_client_id,
          });

          // Cập nhật orderCheck để không check lại nữa
          await orderCheck.update({
            status: "failed",
            error: "PayPal order not found",
            paypal_status: "NOT_FOUND",
            check_count: orderCheck.check_count + 1,
          });

          // Có thể cập nhật status của order nếu cần
          await order.update({
            status: "cancelled",
            orderData: {
              ...order.orderData,
              status_note: "PayPal order not found",
            },
          });
        } else {
          // Xử lý các lỗi khác
          logger.error(`Error processing order ${order.id}:`, {
            error: error.message,
            stack: error.stack,
            orderId: order.id,
            paypalOrderId: order.paypal_order_id,
            clientId: order.paypal_client_id,
          });

          await orderCheck.update({
            status: "failed",
            error: error.message,
            check_count: orderCheck.check_count + 1,
          });
        }
      }
    }

    logger.info("Finished checking draft orders");
  } catch (error) {
    logger.error("Error in checkDraftOrders:", {
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
}
