import dotenv from 'dotenv';
import cron from 'node-cron';
import { checkDraftOrders } from './jobs/checkDraftOrders.js';
import logger from '../utils/logger.js';

dotenv.config();

// Log khi file được thực thi
console.log('Worker file is being executed');
logger.info('Cron worker starting...', {
  time: new Date().toISOString(),
  nodeEnv: process.env.NODE_ENV
});

// Chạy mỗi 5 phút
const cronJob = cron.schedule('*/1 * * * *', async () => {
  const currentTime = new Date().toISOString();
  console.log(`Cron triggered at: ${currentTime}`);
  logger.info('Starting draft orders check cron job', { 
    triggerTime: currentTime 
  });

  try {
    await checkDraftOrders();
  } catch (error) {
    console.error('Cron job error:', error);
    logger.error('Error in draft orders check cron job', {
      error: error.message,
      stack: error.stack
    });
  }
}, {
  scheduled: true,
  timezone: "UTC"
});

// Log trạng thái của cron job
logger.info('Cron job status:', {
  running: cronJob.running
});

// Handlers cho process
process.on('SIGTERM', () => {
  logger.info('SIGTERM received. Shutting down cron worker gracefully');
  cronJob.stop();
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received. Shutting down cron worker gracefully');
  cronJob.stop();
  process.exit(0);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  logger.error('Uncaught Exception:', {
    error: error.message,
    stack: error.stack
  });
  process.exit(1);
});

// Log khi process exit
process.on('exit', (code) => {
  console.log(`Worker process exiting with code: ${code}`);
  logger.info(`Worker process exiting`, { exitCode: code });
});
