'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Orders', 'tracking_id', {
      type: Sequelize.STRING,
      allowNull: true,
      after: 'transaction_id'
    });
    await queryInterface.addColumn('Orders', 'port', {
      type: Sequelize.STRING,
      allowNull: true,
      after: 'tracking_id'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Orders', 'transaction_id');
    await queryInterface.removeColumn('Orders', 'tracking_id');
  }
};
