'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('Orders', 'paypal_client_id', {
      type: Sequelize.STRING,
      allowNull: true,
      after: 'transaction_id'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('Orders', 'paypal_client_id');
  }
};