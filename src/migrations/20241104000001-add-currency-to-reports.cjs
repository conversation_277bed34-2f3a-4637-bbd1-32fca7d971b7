'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add currency column to SaleReports table
    await queryInterface.addColumn('SaleReports', 'currency', {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: 'USD'
    });

    // Add currency column to DomainReports table
    await queryInterface.addColumn('DomainReports', 'currency', {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: 'USD'
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove currency column from SaleReports table
    await queryInterface.removeColumn('SaleReports', 'currency');
    
    // Remove currency column from DomainReports table
    await queryInterface.removeColumn('DomainReports', 'currency');
  }
}; 