module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Add unique index on order_key field
      await queryInterface.addIndex('Orders', ['order_key'], {
        unique: true,
        name: 'Orders_order_key_unique'
      });
      console.log('✅ Added unique index on order_key field');
    } catch (error) {
      console.error('❌ Error adding order_key index:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Remove the unique index
      await queryInterface.removeIndex('Orders', 'Orders_order_key_unique');
      console.log('✅ Removed unique index on order_key field');
    } catch (error) {
      console.error('❌ Error removing order_key index:', error);
      throw error;
    }
  }
}; 