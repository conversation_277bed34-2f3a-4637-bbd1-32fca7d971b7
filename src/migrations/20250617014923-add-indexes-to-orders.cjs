'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addIndex('Orders', ['domain', 'status', 'createdAt'], {
      name: 'orders_domain_status_createdAt_idx'
    });
    await queryInterface.addIndex('Orders', ['createdAt'], {
      name: 'orders_createdAt_idx'
    });
    await queryInterface.addIndex('Orders', ['paypal_client_id'], {
      name: 'orders_paypal_client_id_idx'
    });
    await queryInterface.addIndex('Orders', ['paypal_order_id'], {
      name: 'orders_paypal_order_id_idx'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex('Orders', 'orders_domain_status_createdAt_idx');
    await queryInterface.removeIndex('Orders', 'orders_createdAt_idx');
    await queryInterface.removeIndex('Orders', 'orders_paypal_client_id_idx');
    await queryInterface.removeIndex('Orders', 'orders_paypal_order_id_idx');
  }
};
