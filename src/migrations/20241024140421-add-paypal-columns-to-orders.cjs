'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Orders', 'paypal_order_id', {
      type: Sequelize.STRING,
      allowNull: true
    });
    await queryInterface.addColumn('Orders', 'paypal_checked_at', {
      type: Sequelize.DATE,
      allowNull: true
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Orders', 'paypal_order_id');
    await queryInterface.removeColumn('Orders', 'paypal_checked_at');
  }
};
