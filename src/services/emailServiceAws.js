import fs from "fs/promises";
import path from "path";
import Handlebars from "handlebars";
import axios from "axios";
import { SESClient, SendEmailCommand } from "@aws-sdk/client-ses";
import dotenv from "dotenv";
import { fileURLToPath } from "url";
import logger from "../utils/logger.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config();

// Cấu hình AWS SES client
const sesClient = new SESClient({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

// Hàm helper đ<PERSON> lấy địa chỉ email người nhận
function getRecipientEmail(orderData) {
  if (process.env.NODE_ENV === "local") {
    return "<EMAIL>";
  }
  
  // Validate cấu trúc dữ liệu trước khi truy cập
  if (!orderData) {
    throw new Error("Order data is missing");
  }
  
  if (!orderData.orderData) {
    throw new Error("Order data structure is invalid: orderData property is missing");
  }
  
  if (!orderData.orderData.billing) {
    throw new Error("Order data structure is invalid: billing information is missing");
  }
  
  if (!orderData.orderData.billing.email) {
    throw new Error("Order data structure is invalid: billing email is missing");
  }
  
  return orderData.orderData.billing.email;
}

// Hàm để xác định carrier dựa trên tracking_id
function getCarrier(tracking_id) {
  if (tracking_id.startsWith("9")) {
    return "JS EXPRESS";
  }
  if (tracking_id.startsWith("Y")) {
    return "YUNEXPRESS";
  }
  return "Unknown Carrier";
}

// Hàm để tạo URL tracking dựa trên tracking_id
function getTrackingUrl(tracking_id) {
  const baseUrl = "https://t.17track.net/en#nums=";
  if (tracking_id.startsWith("9")) {
    return `${baseUrl}${tracking_id}&fc=190199`;
  }
  return `${baseUrl}${tracking_id}`;
}

// Đọc template
async function getTemplate(templateName) {
  const templatePath = path.join(
    process.cwd(),
    "src",
    "templates",
    "emails",
    `${templateName}.html`
  );
  return await fs.readFile(templatePath, "utf-8");
}

// Fetch dữ liệu từ Firebase
async function fetchShopData(domain) {
  try {
    const shopKey = domain.replace(/\./g, "");
    const url = `https://king-fruit-slot.firebaseio.com/PXTRUE2/${shopKey}/.json`;
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    logger.error("Error fetching shop data:", error);
    return null;
  }
}

// Hàm helper để format tiền tệ
Handlebars.registerHelper("currency", (value) =>
  new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(value)
);

// Hàm gửi email qua AWS SES
async function sendEmailViaAWS(params) {
  try {
    const command = new SendEmailCommand(params);
    const result = await sesClient.send(command);
    return result;
  } catch (error) {
    logger.error("Error sending email via AWS SES:", error);
    throw error;
  }
}

export async function sendOrderConfirmationEmail(orderData) {
  try {
    const template = await getTemplate("orderConfirmation");
    const compiledTemplate = Handlebars.compile(template);
    const shopData = await fetchShopData(orderData.domain);

    if (!shopData) {
      throw new Error("Failed to fetch shop data");
    }

    const shopEmail = shopData.email || "<EMAIL>";
    const recipientEmail = getRecipientEmail(orderData);

    const templateData = {
      ...orderData,
      shopEmail,
    };

    const html = compiledTemplate(templateData);

    const params = {
      Destination: {
        ToAddresses: [recipientEmail],
      },
      Message: {
        Body: {
          Html: {
            Charset: "UTF-8",
            Data: html,
          },
        },
        Subject: {
          Charset: "UTF-8",
          Data: `Your order confirmation from ${orderData.domain}`,
        },
      },
      Source: `Order Confirmation #${orderData.id} <<EMAIL>>`,
      ReplyToAddresses: [shopEmail],
    };

    const result = await sendEmailViaAWS(params);
    logger.info("Order confirmation email sent:", { result, recipientEmail });
    return result;
  } catch (error) {
    logger.error("Error sending order confirmation email:", error);
    throw error;
  }
}

export async function sendShippingConfirmationEmail(orderData) {
  try {
    const template = await getTemplate("shippingConfirmation");
    const compiledTemplate = Handlebars.compile(template);
    const shopData = await fetchShopData(orderData.domain);

    if (!shopData) {
      throw new Error("Failed to fetch shop data");
    }

    const shopEmail = shopData.email || "<EMAIL>";
    const recipientEmail = getRecipientEmail(orderData);
    const carrier = getCarrier(orderData.tracking_id);
    const tracking_url = getTrackingUrl(orderData.tracking_id);

    const templateData = {
      ...orderData,
      shopEmail,
      carrier,
      tracking_url,
    };

    const html = compiledTemplate(templateData);

    const params = {
      Destination: {
        ToAddresses: [recipientEmail],
      },
      Message: {
        Body: {
          Html: {
            Charset: "UTF-8",
            Data: html,
          },
        },
        Subject: {
          Charset: "UTF-8",
          Data: `Your order from ${orderData.domain} has been shipped!`,
        },
      },
      Source: `Shipping Confirmation #${orderData.id} <<EMAIL>>`,
      ReplyToAddresses: [shopEmail],
    };

    const result = await sendEmailViaAWS(params);
    logger.info("Shipping confirmation email sent:", {
      result,
      recipientEmail,
    });
    return result;
  } catch (error) {
    logger.error("Error sending shipping confirmation email:", error);
    throw error;
  }
}
