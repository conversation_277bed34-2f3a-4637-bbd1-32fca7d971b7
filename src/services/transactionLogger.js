import TransactionEventLog from "../models/transactionEventLog.js";
import Order from "../models/order.js";
import sequelize from "../config/db.js";
import logger from "../utils/logger.js";

class TransactionLogger {
  static extractMetadata(order) {
    return order?.orderData?.meta_data || null;
  }

  static async logFinalOutcome({
    orderId,
    eventType, // Only: CAPTURE_SUCCESS | CAPTURE_PENDING | CAPTURE_FAILED | CAPTURE_ERROR
    paypalOrderId = null,
    transactionId = null,
    oldStatus = null,
    newStatus = null,
    paypalApiResponse = null,
    notes = null,
    order = null
  }) {
    try {
      let metadata = null;
      if (order) {
        metadata = this.extractMetadata(order);
      }
      const eventLog = await TransactionEventLog.create({
        order_id: orderId,
        event_type: eventType,
        paypal_order_id: paypalOrderId,
        transaction_id: transactionId,
        old_status: oldStatus,
        new_status: newStatus,
        paypal_api_response: paypalApiResponse,
        notes: notes,
        metadata: metadata
      });
      logger.info("Transaction event logged", {
        eventLogId: eventLog.id,
        orderId,
        eventType,
        transactionId
      });
      return eventLog;
    } catch (error) {
      logger.error("Failed to log transaction event", {
        error: error.message,
        orderId,
        eventType
      });
    }
  }

  static async getOrderHistory(orderId) {
    return await TransactionEventLog.findAll({
      where: { order_id: orderId },
      order: [["created_at", "ASC"]]
    });
  }

  static async findByTransactionId(transactionId) {
    return await TransactionEventLog.findAll({
      where: { transaction_id: transactionId },
      include: [{ model: Order }],
      order: [["created_at", "DESC"]]
    });
  }

  static async findByMetadata(searchKey, searchValue) {
    return await TransactionEventLog.findAll({
      where: sequelize.literal(`JSON_EXTRACT(metadata, '$.${searchKey}') = '${searchValue}'`),
      include: [{ model: Order }],
      order: [["created_at", "DESC"]]
    });
  }
}

export default TransactionLogger; 