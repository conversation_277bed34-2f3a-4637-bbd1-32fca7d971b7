import formData from "form-data";
import Mailgun from "mailgun.js";
import fs from "fs/promises";
import path from "path";
import Handlebars from "handlebars";
import axios from "axios";
import logger from "../utils/logger.js";

const mailgun = new Mailgun(formData);
const MAILGUN_API_KEY = "**************************************************";
const MAILGUN_DOMAIN = "true-tracking.com";

const mg = mailgun.client({
  username: "api",
  key: MAILGUN_API_KEY,
});

// Hàm helper để lấy địa chỉ email người nhận
function getRecipientEmail(orderData) {
  if (process.env.NODE_ENV === "local") {
    return "<EMAIL>";
  }
  return orderData.orderData.billing.email;
}

// Hàm để xác định carrier dựa trên tracking_id
function getCarrier(tracking_id) {
  if (tracking_id.startsWith("9")) {
    return "JS EXPRESS";
  }
  if (tracking_id.startsWith("Y")) {
    return "YUNEXPRESS";
  }
  return "Unknown Carrier";
}

// Hàm để tạo URL tracking dựa trên tracking_id
function getTrackingUrl(tracking_id) {
  const baseUrl = "https://t.17track.net/en#nums=";
  if (tracking_id.startsWith("9")) {
    return `${baseUrl}${tracking_id}&fc=190199`;
  }
  return `${baseUrl}${tracking_id}`;
}

// Đọc template
async function getTemplate(templateName) {
  const templatePath = path.join(
    process.cwd(),
    "templates",
    "emails",
    `${templateName}.html`,
  );
  return await fs.readFile(templatePath, "utf-8");
}

// Fetch dữ liệu từ Firebase
async function fetchShopData(domain) {
  try {
    const shopKey = domain.replace(/\./g, "");
    const url = `https://king-fruit-slot.firebaseio.com/PXTRUE2/${shopKey}/.json`;
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    logger.error("Error fetching shop data:", error);
    return null;
  }
}

// Hàm helper để format tiền tệ
Handlebars.registerHelper("currency", (value) =>
  new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(value),
);

export async function sendOrderConfirmationEmail(orderData) {
  try {
    const template = await getTemplate("orderConfirmation");
    const compiledTemplate = Handlebars.compile(template);
    const shopData = await fetchShopData(orderData.domain);

    if (!shopData) {
      throw new Error("Failed to fetch shop data");
    }

    const shopEmail = shopData.email || "<EMAIL>";
    const recipientEmail = getRecipientEmail(orderData);

    const templateData = {
      ...orderData,
      shopEmail,
    };

    const html = compiledTemplate(templateData);

    const result = await mg.messages.create(MAILGUN_DOMAIN, {
      from: `Order Confirmation #${orderData.id} <<EMAIL>>`,
      "h:Reply-To": shopEmail,
      to: [recipientEmail],
      subject: `Your order confirmation from ${orderData.domain}`,
      html,
    });

    logger.info("Order confirmation email sent:", { result, recipientEmail });
    return result;
  } catch (error) {
    logger.error("Error sending order confirmation email:", error);
    throw error;
  }
}

export async function sendShippingConfirmationEmail(orderData) {
  try {
    const template = await getTemplate("shippingConfirmation");
    const compiledTemplate = Handlebars.compile(template);
    const shopData = await fetchShopData(orderData.domain);

    if (!shopData) {
      throw new Error("Failed to fetch shop data");
    }

    const shopEmail = shopData.email || "<EMAIL>";
    const recipientEmail = getRecipientEmail(orderData);
    const carrier = getCarrier(orderData.tracking_id);
    const tracking_url = getTrackingUrl(orderData.tracking_id);

    const templateData = {
      ...orderData,
      shopEmail,
      carrier,
      tracking_url,
    };

    const html = compiledTemplate(templateData);

    const result = await mg.messages.create(MAILGUN_DOMAIN, {
      from: `Shipping Confirmation #${orderData.id} <<EMAIL>>`,
      "h:Reply-To": shopEmail,
      to: [recipientEmail],
      subject: `Your order from ${orderData.domain} has been shipped!`,
      html,
    });

    logger.info("Shipping confirmation email sent:", {
      result,
      recipientEmail,
    });
    return result;
  } catch (error) {
    logger.error("Error sending shipping confirmation email:", error);
    throw error;
  }
}
