import currency from "currency.js";
import SaleReport from "../models/saleReport.js";
import { adjustDate } from "../utils/utils.js";

export const updateSaleReport = async (newOrder) => {
  const dateString = adjustDate(newOrder.createdAt);
  const orderCurrency = newOrder.orderData.currency || "USD";
  
  const [report, created] = await SaleReport.findOrCreate({
    where: {
      domain: newOrder.domain,
      date: dateString,
    },
    defaults: {
      orders: 0,
      money: 0.0,
      currency: orderCurrency,
      Stats: {},
      StatsUTM: {},
      orderIds: [],
    },
  });

  if (!report.orderIds.includes(newOrder.id)) {
    const updatedOrderIds = [...report.orderIds, newOrder.id];
    report.orderIds = updatedOrderIds;
    report.orders += 1;
    report.money = currency(report.money).add(newOrder.orderData.total).value;
    
    report.currency = orderCurrency;

    const updatedStats = JSON.parse(JSON.stringify(report.Stats));
    const productSale = newOrder.orderData.line_items[0];
    if (!updatedStats[productSale.product_id]) {
      updatedStats[productSale.product_id] = {
        quantity: 0,
        revenue: 0,
        name: productSale.name,
        link: productSale.product_link,
        thumbnail: productSale.image,
      };
    }
    updatedStats[productSale.product_id].quantity += 1;
    updatedStats[productSale.product_id].revenue = currency(
      updatedStats[productSale.product_id].revenue,
    ).add(newOrder.orderData.total).value;

    report.set("Stats", updatedStats);
    await report.save();
  }
};
