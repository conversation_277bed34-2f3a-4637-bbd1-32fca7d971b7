import axios from "axios";
import dotenv from "dotenv";

dotenv.config();

const baseURL =
  process.env.SANDBOX_MODE === "true"
    ? "https://api.sandbox.paypal.com"
    : "https://api.paypal.com";



export async function createTrackers(trackersData, accessToken) {
  const config = {
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  };

  try {
    const response = await axios.post(
      `${baseURL}/v1/shipping/trackers-batch`,
      trackersData,
      config,
    );
    console.log("update trackers success");
    return response.data;
  } catch (error) {
    console.error(
      "Error creating trackers:",
      error.response ? error.response.data : error.message,
    );
    throw error;
  }
}
