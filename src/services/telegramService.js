import axios from 'axios';
import { telegramConfig } from '../config/telegram.js';
import logger from '../utils/logger.js';

// Rate limiting setup
let messageCount = 0;
let lastResetTime = Date.now();
const RATE_LIMIT = 20; // messages per minute
const RESET_INTERVAL = 60 * 1000; // 1 minute in milliseconds

const checkRateLimit = () => {
  const now = Date.now();
  
  // Reset counter if a minute has passed
  if (now - lastResetTime >= RESET_INTERVAL) {
    messageCount = 0;
    lastResetTime = now;
  }

  // Check if we're under the limit
  if (messageCount >= RATE_LIMIT) {
    return false;
  }

  messageCount++;
  return true;
};

const sendMessage = async (chatId, message) => {
  try {
    // Check rate limit before sending
    if (!checkRateLimit()) {
      logger.warn('Telegram rate limit exceeded, skipping message', {
        chatId,
        messageLength: message.length,
        currentCount: messageCount
      });
      return null;
    }

    const url = `https://api.telegram.org/bot${telegramConfig.botToken}/sendMessage`;
    const response = await axios.post(url, {
      chat_id: chatId,
      text: message,
      parse_mode: 'HTML'
    });

    return response.data;
  } catch (error) {
    logger.error('Error sending telegram message:', {
      error: error.message,
      chatId,
      messageLength: message.length
    });
    return null;
  }
};

export const sendErrorAlert = async (message, details = {}) => {
  const formattedMessage = `
🔴 <b>ERROR ALERT</b>
${message}

<b>Details:</b>
${Object.entries(details)
  .map(([key, value]) => `${key}: ${value}`)
  .join('\n')}

Time: ${new Date().toISOString()}
`;

  return sendMessage(telegramConfig.chatIds.error, formattedMessage);
};

export const sendInfoMessage = async (message, details = {}) => {
  const formattedMessage = `
ℹ️ <b>INFO</b>
${message}

${Object.entries(details)
  .map(([key, value]) => `${key}: ${value}`)
  .join('\n')}

Time: ${new Date().toISOString()}
`;

  return sendMessage(telegramConfig.chatIds.info, formattedMessage);
};

export const sendPayPalErrorAlert = async (error, context = {}) => {
  // const skipErrorCodes = ['INSTRUMENT_DECLINED', 'PAYMENT_SOURCE_DECLINED'];
  const skipErrorCodes = [];
  const errorCode = error.response?.data?.details?.[0]?.issue;
  
  if (skipErrorCodes.includes(errorCode)) {
    return null;
  }

  // Extract metaData and format it
  const metaData = context.metaData;
  let metaDataString = 'No meta data';
  if (metaData && Array.isArray(metaData)) {
    metaDataString = metaData.map(item => `  ${item.key}: ${item.value}`).join('\n');
  }

  // Remove metaData from context to avoid duplication in the context output
  const contextWithoutMetaData = { ...context };
  delete contextWithoutMetaData.metaData;

  const formattedMessage = `
🔴 <b>PAYPAL ERROR</b>

<b>Error:</b> ${error.message || error}
${error.response?.data ? `<b>Response:</b> ${JSON.stringify(error.response.data, null, 2)}` : ''}

<b>Context:</b>
${Object.entries(contextWithoutMetaData)
  .map(([key, value]) => `${key}: ${value}`)
  .join('\n')}

<b>Meta Data:</b>
${metaDataString}

Time: ${new Date().toISOString()}
`;

  return sendMessage(telegramConfig.chatIds.error, formattedMessage);
};

// Hàm gửi thông báo order status
export const sendOrderStatusUpdate = async (order, status, details = {}) => {
  const formattedMessage = `
📦 <b>ORDER STATUS UPDATE</b>

<b>Order ID:</b> ${order.id}
<b>Status:</b> ${status}
<b>Domain:</b> ${order.domain}
<b>Transaction ID:</b> ${order.transaction_id}

${Object.entries(details)
  .map(([key, value]) => `<b>${key}:</b> ${value}`)
  .join('\n')}

Time: ${new Date().toISOString()}
`;

  return sendMessage(telegramConfig.chatIds.info, formattedMessage);
}; 