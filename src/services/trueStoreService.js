import fetch from "node-fetch";
import querystring from "querystring";
import { format } from "date-fns";
import { normalizeString } from "../utils/utils.js";

export async function sendNewOrderToTrueStore(newOrder, stats) {
  const baseFormData = {
    insertorders: "1",
    Domain: String(newOrder.domain),
    PaypalClientID: String(newOrder.paypal_client_id || ""),
    Stats: JSON.stringify(stats),
    ShipPrice: String(newOrder.orderData.shipping_total),
    Date: format(new Date(newOrder.createdAt), "dd-MM-yyyy"),
    ShippingName: `${String(newOrder.orderData.shipping.first_name)} ${String(
      newOrder.orderData.shipping.last_name
    )}`,
    ShippingAddress1: String(newOrder.orderData.shipping.address_1),
    ShippingAddress2: String(newOrder.orderData.shipping.address_2),
    ShippingCompany: String(newOrder.orderData.shipping.company || ""),
    ShippingCity: String(newOrder.orderData.shipping.city),
    ShippingZip: String(newOrder.orderData.shipping.postcode),
    ShippingProvince: String(newOrder.orderData.shipping.state),
    ShippingCountry: String(newOrder.orderData.shipping.country),
    ShippingPhone: String(newOrder.orderData.billing.phone),
    PaymentAccount: String(newOrder.orderData.payment_method_title),
    TransactionIds: String(newOrder.transaction_id),
    CustomerEmail: String(newOrder.orderData.billing.email),
    Status: "processing",
    OrderID: String(newOrder.id),
    DateOrder: format(new Date(newOrder.createdAt), "yyyy-MM-dd HH:mm:ss"),
    IP: String(getMetaDataValue(newOrder.orderData.meta_data, "ip")),
    UA: String(getMetaDataValue(newOrder.orderData.meta_data, "UA")),
    QUERY: String(getMetaDataValue(newOrder.orderData.meta_data, "QUERY")),
  };

  const results = [];

  for (let i = 0; i < newOrder.orderData.line_items.length; i++) {
    const item = newOrder.orderData.line_items[i];
    const isFirstItem = i === 0;

    const itemFormData = {
      ...baseFormData,
      LineitemQuantity: String(item.quantity),
      ItemName: String(item.name),
      Image: String(item.image),
      Sku: String(item.sku),
      LinkProduct: item.product_link
        ? String(item.product_link.replace("admin.", ""))
        : "",
      TotalPrice: isFirstItem ? String(newOrder.orderData.total) : "0",
      Variation: item.meta_data
        ? String(
            item.meta_data
              .map((attr) => `${attr.name} ${attr.option}`)
              .join(" - ")
          )
        : "",
      Noti: isFirstItem
        ? `[${String(
            newOrder.domain
          )}] has a new order with a total amount of ${String(
            newOrder.orderData.total
          )}`
        : "",
    };
    // return true;
    try {
      const response = await fetch("https://truestore.us/api", {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: querystring.stringify(itemFormData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseText = await response.text();
      results.push({ item: item.name, response: responseText });
      console.log(
        `[US] Order sent successfully for item ${item.name}. Response:`,
        responseText
      );
    } catch (error) {
      console.error(`[US] Error sending order for item ${item.name}:`, error);
      results.push({ item: item.name, error: error.message });
    }

    try {
      const response = await fetch("https://truestore.us/api", {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: querystring.stringify(itemFormData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseText = await response.text();
      results.push({ item: item.name, response: responseText });
      console.log(
        `[EU] Order sent successfully for item ${item.name}. Response:`,
        responseText
      );
    } catch (error) {
      console.error(`[EU] Error sending order for item ${item.name}:`, error);
      results.push({ item: item.name, error: error.message });
    }
  }

  return results;
}

function getMetaDataValue(metaData, key) {
  const item = metaData.find((item) => item.key === key);
  return item ? item.value : "";
}
