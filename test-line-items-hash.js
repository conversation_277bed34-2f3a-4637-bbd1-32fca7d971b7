import { generateOrderDataHash, generateDraftCacheKey } from './src/utils/keyGenerator.js';

/**
 * Test script để kiểm tra line items hash với các trường hợp khác nhau
 */

console.log('🧪 Testing generateOrderDataHash function after fix\n');

// Test case 1: Products with SKU
console.log('TEST 1: Products with SKU');
const products1 = [
  { sku: "PROD_001", name: "Product 1", quantity: 1, price: "19.99" },
  { sku: "PROD_002", name: "Product 2", quantity: 2, price: "29.99" }
];
const hash1 = generateOrderDataHash(products1, []);
console.log('Hash:', hash1);

// Test case 2: Products without SKU (chỉ có name)
console.log('\nTEST 2: Products without SKU (only name)');
const products2 = [
  { name: "Product 1", quantity: 1, price: "19.99" },
  { name: "Product 2", quantity: 2, price: "29.99" }
];
const hash2 = generateOrderDataHash(products2, []);
console.log('Hash:', hash2);

// Test case 3: Same products, different order (should be same hash)
console.log('\nTEST 3: Same products, different order');
const products3 = [
  { sku: "PROD_002", name: "Product 2", quantity: 2, price: "29.99" },
  { sku: "PROD_001", name: "Product 1", quantity: 1, price: "19.99" }
];
const hash3 = generateOrderDataHash(products3, []);
console.log('Hash:', hash3);
console.log('Same as TEST 1?', hash1 === hash3 ? '✅ YES' : '❌ NO');

// Test case 4: Different products
console.log('\nTEST 4: Different products');
const products4 = [
  { sku: "PROD_003", name: "Product 3", quantity: 1, price: "39.99" }
];
const hash4 = generateOrderDataHash(products4, []);
console.log('Hash:', hash4);
console.log('Different from TEST 1?', hash1 !== hash4 ? '✅ YES' : '❌ NO');

// Test case 5: Same products but different quantities
console.log('\nTEST 5: Same products but different quantities');
const products5 = [
  { sku: "PROD_001", name: "Product 1", quantity: 2, price: "19.99" },
  { sku: "PROD_002", name: "Product 2", quantity: 1, price: "29.99" }
];
const hash5 = generateOrderDataHash(products5, []);
console.log('Hash:', hash5);
console.log('Different from TEST 1?', hash1 !== hash5 ? '✅ YES' : '❌ NO');

// Test case 6: Products with same name but different SKU
console.log('\nTEST 6: Products with same name but different SKU');
const products6 = [
  { sku: "SKU_A", name: "Same Product", quantity: 1, price: "19.99" },
  { sku: "SKU_B", name: "Same Product", quantity: 1, price: "19.99" }
];
const hash6 = generateOrderDataHash(products6, []);
console.log('Hash:', hash6);

// Test case 7: Products with same name, no SKU (should be same hash)
console.log('\nTEST 7: Products with same name, no SKU (duplicate names)');
const products7 = [
  { name: "Same Product", quantity: 1, price: "19.99" },
  { name: "Same Product", quantity: 1, price: "19.99" }
];
const hash7 = generateOrderDataHash(products7, []);
console.log('Hash:', hash7);

// Test full cache key generation
console.log('\n🔑 Testing full cache key generation:');
const metaData = [
  { key: "ip", value: "127.0.0.1" },
  { key: "UA", value: "Mozilla/5.0 (Test Browser)" }
];

const draftKey1 = generateDraftCacheKey('example.com', metaData, products1, []);
const draftKey2 = generateDraftCacheKey('example.com', metaData, products2, []);
const draftKey4 = generateDraftCacheKey('example.com', metaData, products4, []);

console.log('Draft key (products with SKU):', draftKey1);
console.log('Draft key (products without SKU):', draftKey2);
console.log('Draft key (different products):', draftKey4);

console.log('\n📊 Summary:');
console.log('- Different line_items → Different cache keys ✅');
console.log('- Same line_items (different order) → Same cache key ✅');
console.log('- Products without SKU use name as fallback ✅'); 