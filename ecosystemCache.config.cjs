module.exports = {
  apps: [
    {
      name: "cache-server",
      script: "./src/cacheServer.js",
      env: {
        NODE_ENV: "production",
        PORT: 3006,
      },
      instances: 6,
      exec_mode: "cluster",
      autorestart: true,
      watch: false,
      max_memory_restart: "500M",
      exp_backoff_restart_delay: 100,
      merge_logs: true,
      log_date_format: "YYYY-MM-DD HH:mm:ss",
      error_file: "logs/cache-server-error.log",
      out_file: "logs/cache-server-out.log",
      node_args: "--max-old-space-size=1024",
    }
  ],
  deploy: {
    production: {
      user: "root",
      host: "***************",
      ref: "origin/main",
      repo: "**************:thanhnhoncntt/truestore-be-proxy.git",
      path: "/root/be-proxy",
      "pre-deploy-local": "",
      "post-deploy":
        "source ~/.nvm/nvm.sh && nvm use 20.15.0 && npm install && pm2 reload ecosystem.config.js --env production",
      "pre-setup": "",
      env: {
        NODE_ENV: "production",
      },
    },
  },
};