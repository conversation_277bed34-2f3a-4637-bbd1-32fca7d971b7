# API Documentation - TrueStore Order Service

## Authentication

Tất cả các endpoints (trừ public routes) đều yêu cầu Basic Authentication:
- **Username**: `devtruestore`
- **Password**: `pas72ns2ws5ord`

## Order Routes (`/api`)

### Public API Endpoints

#### 1. Check Order by Order Key (Public)
```
GET /api/public/v3/orders/check/:orderKey
```
**Authentication**: None (Public endpoint)
**Rate Limiting**: 20 requests per 5 minutes per IP
**Description**: Ki<PERSON>m tra thông tin order bằng order key với xác thực domain
**Required Headers**:
- `x-domain`: Domain của order (phải khớp với domain trong database)

**URL Parameters**:
- `orderKey`: Order key được tạo khi tạo order

**Response Format**:
```json
{
  "success": true,
  "data": {
    "id": 778471,
    "order_key": "1750016047691ae1wM2jA",
    "domain": "oplexuk.com",
    "status": "shipped",
    "tracking_id": "SDH0072287226",
    "createdAt": "2025-06-15T12:34:08.000Z",
    "updatedAt": "2025-06-17T01:37:18.000Z",
    "total": 76.98,
    "billing": {
      "city": "Brandon ",
      "email": "<EMAIL>",
      "phone": "",
      "state": "FL",
      "country": "US",
      "postcode": "33511",
      "address_1": "2219 Malibu drive ",
      "address_2": "",
      "last_name": "Vognet",
      "first_name": "Frank"
    },
    "currency": "USD",
    "shipping": {
      "city": "Brandon ",
      "state": "FL",
      "country": "US",
      "postcode": "33511",
      "address_1": "2219 Malibu drive ",
      "address_2": "",
      "last_name": "Vognet",
      "first_name": "Frank"
    },
    "sub_total": 69.99,
    "tip_total": 0,
    "line_items": [
      {
        "sku": "20250611-n176-13-01-TORQ6S",
        "name": "🎁 Hot Sale 50% OFF 🔥 Solar Pool Fountain with Lights Dual Arc - Above/Inground Pool Fountain Lights with Remote Control-Mode",
        "image": "https://admin.trueinvestz.com/wp-content/uploads/2025/05/89c79e53-403e-4aa2-b1f9-dcad3d9469e6.png",
        "price": 69.99,
        "total": "69.99",
        "quantity": 1,
        "meta_data": [
          {
            "key": "Model",
            "display_key": "Model",
            "display_value": "Solar LED Light Models"
          }
        ],
        "product_id": 41928,
        "product_link": "https://oplexuk.com/product/poolfountain176ts",
        "variation_id": 524923
      }
    ],
    "discount_total": 0,
    "shipping_lines": [
      {
        "total": "6.99",
        "method_id": "flat_rate"
      }
    ],
    "shipping_total": 6.99
  }
}
```

**Error Responses**:
- `400`: Missing x-domain header hoặc order key
- `403`: Domain không khớp với order domain
- `404`: Order không tồn tại
- `429`: Vượt quá rate limit
- `500`: Lỗi server

**Usage Example**:
```bash
curl -H "x-domain: example.com" \
     https://api-order-service.truestore.us/api/public/v3/orders/check/1703123456789abcd1234
```

### Orders Management

#### 1. Tạo Order Mới
```
POST /api/orders
```
**Authentication**: Required  
**Description**: Tạo một order mới  
**Required Params**: `domain`, `transaction_id`, `orderData`

#### 2. Lấy Tổng Orders
```
GET /api/orders/sum
```
**Authentication**: Required  
**Description**: Lấy tổng số orders theo domain và khoảng thời gian  
**Required Params**: `domain`, `startdate`, `enddate`

#### 3. Phân Tích Orders
```
GET /api/orders/analyzer
```
**Authentication**: Required  
**Description**: Phân tích orders trong một khoảng thời gian  
**Required Params**: `startdate`, `enddate`

#### 4. Lấy Danh Sách Orders
```
GET /api/orders
```
**Authentication**: Required  
**Description**: Lấy danh sách orders với phân trang  
**Required Params**: `startdate`, `enddate`  
**Optional Params**: `domain`, `perPage`, `page`

#### 5. Lấy Chi Tiết Order
```
GET /api/orders/:orderId
```
**Authentication**: Required  
**Description**: Lấy thông tin chi tiết của một order

#### 6. Cập Nhật Order
```
POST /api/orders/:orderId
```
**Authentication**: Required  
**Description**: Cập nhật thông tin order

### Order Notes

#### 7. Thêm Ghi Chú
```
POST /api/orders/:orderId/notes
```
**Authentication**: Required  
**Description**: Thêm ghi chú cho order

#### 8. Lấy Ghi Chú
```
GET /api/orders/:orderId/notes
```
**Authentication**: Required  
**Description**: Lấy tất cả ghi chú của order

### Tracking

#### 9. Cập Nhật Tracking (Có Email)
```
POST /api/update-tracking
```
**Authentication**: None  
**Description**: Cập nhật tracking number và gửi email thông báo

#### 10. Cập Nhật Tracking (Không Email)
```
POST /api/update-tracking-without-email
```
**Authentication**: None  
**Description**: Cập nhật tracking number không gửi email

### Public Routes

#### 11. Lấy Order (Public)
```
GET /api/public/v3/orders/:orderId
```
**Authentication**: None  
**Description**: Lấy thông tin order công khai

### Payment (PayPal)

#### 12. Tạo PayPal Order
```
POST /api/payments/paypal/create
```
**Authentication**: None  
**Description**: Tạo draft order cho PayPal

#### 13. Capture PayPal Payment
```
POST /api/payments/paypal/capture/:orderId
```
**Authentication**: None  
**Description**: Xử lý capture payment từ PayPal

### Reports

#### 14. Sale Report
```
GET /api/sale-report
```
**Authentication**: Required  
**Description**: Tạo báo cáo bán hàng  
**Optional Params**: `date`

#### 15. Domain Report
```
GET /api/domain-report
```
**Authentication**: Required  
**Description**: Tạo báo cáo theo domain  
**Optional Params**: `date`

#### 16. V1 Domain Report
```
GET /api/v1-domain-report
```
**Authentication**: Required  
**Description**: Tạo báo cáo domain (phiên bản 1)

#### 17. V1 Domain Product Report
```
GET /api/v1-domain-product-report
```
**Authentication**: Required  
**Description**: Tạo báo cáo sản phẩm theo domain (phiên bản 1)

#### 18. Domain Configs
```
GET /api/domain-configs
```
**Authentication**: Required  
**Description**: Lấy cấu hình domains

#### 19. API Info
```
GET /api/
```
**Authentication**: Required  
**Description**: Lấy thông tin tất cả APIs có sẵn

---

## Remote Routes

### Middleware
- **checkTunnel**: Tự động setup tunnel trước khi xử lý request
- **auth**: Basic authentication tương tự như order routes

### History Orders

#### 20. Lấy History Orders
```
GET /history-orders
```
**Authentication**: Required  
**Description**: Lấy danh sách history orders với filter  
**Optional Params**: `perPage`, `page`, `domain`, `startdate`, `enddate`  
**Features**:
- Phân trang (max 100 items/page)
- Filter theo domain
- Filter theo khoảng thời gian
- Loại trừ port "cardshield"
- Chỉ lấy orders có totalPrice > 0

### Domain Configuration

#### 21. Lấy Domain Config
```
GET /domain-config
```
**Authentication**: Required  
**Description**: Lấy cấu hình domains  
**Optional Params**: `perPage`, `page`  
**Features**:
- Phân trang (max 100 items/page)
- Chỉ lấy domains có port = "cardshield"

### Data Integrity

#### 22. Tìm Orders Thiếu
```
GET /orders-missing
```
**Authentication**: Required  
**Description**: Tìm orders có trong bảng Order nhưng không có trong HistoryOrder  
**Required Params**: `startdate`, `enddate`  
**Features**:
- So sánh theo transaction_id
- Loại trừ cardshield domains
- Chỉ kiểm tra orders có totalPrice > 0

#### 23. So Sánh Orders & History
```
GET /compare-orders-history
```
**Authentication**: Required  
**Description**: So sánh toàn diện giữa Orders và HistoryOrders  
**Required Params**: `startdate`, `enddate`  
**Features**:
- Group theo domain
- Tìm orders thiếu ở cả 2 bảng
- Trả về integrity check report

#### 24. Sync Orders & History
```
GET /sync-orders-history
```
**Authentication**: Required  
**Description**: Đồng bộ dữ liệu giữa Orders và HistoryOrders  
**Required Params**: `startdate`, `enddate`  
**Features**:
- Tương tự compare-orders-history
- Chuẩn bị cho việc đồng bộ dữ liệu

---

## Models Được Sử Dụng

### Order Model
- Chứa thông tin orders chính
- Fields: `transaction_id`, `domain`, `id`, `orderData`, `createdAt`

### HistoryOrder Model  
- Chứa lịch sử orders đã xử lý
- Fields: `TransactionIds`, `Domain`, `OrderID`, `TotalPrice`, `DateOrder`, etc.

### DomainConfig Model
- Cấu hình domains
- Fields: `domain`, `port`, etc.

---

## Utilities

### Time Conversion
- `convertToClientTimezone()`: Chuyển đổi timezone cho queries

### Tunnel Setup
- `setupTunnel()`: Thiết lập tunnel connection

### CSV Export
- Sử dụng `csv-writer` để export missing orders (hiện tại bị comment)

---

## Notes

1. **Security**: Tất cả routes đều dùng chung basic auth credentials
2. **Pagination**: Giới hạn tối đa 100 items per page
3. **Error Handling**: Tất cả routes đều có error handling cơ bản
4. **Date Filtering**: Sử dụng `date-fns` cho date operations
5. **Database**: Sử dụng Sequelize ORM với PostgreSQL
6. **Tunnel**: Remote routes yêu cầu tunnel connection để hoạt động 