```
orders-002.com
orders-003.com
orders-222.com
orders-666.com
orders-888.com
orders-dth.com
orders-nkh.com
orders-novapeak.com
orders-emberedge.com
orders-swifthive.com
orders-skybloom.com
```

```
Sandbox URL
https://sandbox.paypal.com

Sandbox Region
US
Email
<EMAIL>

Password
bLZ96+]m
```

```
<EMAIL>
Im5=xj^M
```


```
CREATE USER 'usertruestore_v_4a482'@'%' IDENTIFIED BY 'k23k@hab129BB';
GRANT SELECT ON truestore_v_4a482.wp_danhsachdomain TO 'usertruestore_v_4a482'@'%';
FLUSH PRIVILEGES;
```

```
ALTER TABLE truestore.Orders
ADD COLUMN order_key VARCHAR(255) NULL AFTER id;
```



pm2 start src/workers/emailWorker.js --name "email-worker"



```
AWS : <EMAIL>
```

```
npx sequelize-cli db:migrate
```


```
pm2 start ecosystem.config.cjs
```

``` 
pm2 start ecosystem.config.cjs --only order-check-worker
```


node src/scripts/syncSaleReportByDate.js 12-02-2025 mysticraftus.com

Tìm tất cả orders của domain mysticraftus.com trong ngày 12-02-2025
Tính lại sale report và domain report cho từng order
Log chi tiết quá trình xử lý và kết quả
Các cải tiến so với script cũ:
Dễ sử dụng hơn với tham số command line
Log rõ ràng hơn về domain và ngày đang xử lý
Tập trung vào một domain và một ngày cụ thể
Báo lỗi rõ ràng nếu sử dụng sai cách