{"/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/utils.js": {"path": "/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/utils.js", "statementMap": {"0": {"start": {"line": 3, "column": 29}, "end": {"line": 3, "column": 35}}, "1": {"start": {"line": 6, "column": 26}, "end": {"line": 6, "column": 68}}, "2": {"start": {"line": 7, "column": 24}, "end": {"line": 7, "column": 64}}, "3": {"start": {"line": 9, "column": 30}, "end": {"line": 9, "column": 60}}, "4": {"start": {"line": 11, "column": 29}, "end": {"line": 11, "column": 71}}, "5": {"start": {"line": 12, "column": 33}, "end": {"line": 12, "column": 92}}, "6": {"start": {"line": 13, "column": 31}, "end": {"line": 13, "column": 86}}, "7": {"start": {"line": 15, "column": 2}, "end": {"line": 18, "column": 4}}, "8": {"start": {"line": 20, "column": 26}, "end": {"line": 24, "column": 1}}, "9": {"start": {"line": 21, "column": 15}, "end": {"line": 21, "column": 34}}, "10": {"start": {"line": 22, "column": 23}, "end": {"line": 22, "column": 40}}, "11": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 50}}}, "fnMap": {"0": {"name": "convertToClientTimezone", "decl": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 39}}, "loc": {"start": {"line": 5, "column": 60}, "end": {"line": 19, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 20, "column": 26}, "end": {"line": 20, "column": 27}}, "loc": {"start": {"line": 20, "column": 41}, "end": {"line": 24, "column": 1}}, "line": 20}}, "branchMap": {}, "s": {"0": 1, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 1, "9": 1, "10": 1, "11": 1}, "f": {"0": 0, "1": 1}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "3171995ccc09e87d75c62bdb9d8b5cf525c84c2a"}, "/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/config/db.js": {"path": "/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/config/db.js", "statementMap": {"0": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 16}}, "1": {"start": {"line": 6, "column": 18}, "end": {"line": 25, "column": 1}}, "2": {"start": {"line": 17, "column": 12}, "end": {"line": 19, "column": 13}}, "3": {"start": {"line": 18, "column": 16}, "end": {"line": 18, "column": 38}}, "4": {"start": {"line": 20, "column": 12}, "end": {"line": 20, "column": 26}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 16, "column": 18}, "end": {"line": 16, "column": 19}}, "loc": {"start": {"line": 16, "column": 41}, "end": {"line": 21, "column": 9}}, "line": 16}}, "branchMap": {"0": {"loc": {"start": {"line": 17, "column": 12}, "end": {"line": 19, "column": 13}}, "type": "if", "locations": [{"start": {"line": 17, "column": 12}, "end": {"line": 19, "column": 13}}, {"start": {}, "end": {}}], "line": 17}}, "s": {"0": 2, "1": 2, "2": 0, "3": 0, "4": 0}, "f": {"0": 0}, "b": {"0": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "e1989f82785d3b3571c6a2d5649af17be08f266b"}, "/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/controllers/order.js": {"path": "/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/controllers/order.js", "statementMap": {"0": {"start": {"line": 9, "column": 34}, "end": {"line": 80, "column": 1}}, "1": {"start": {"line": 10, "column": 21}, "end": {"line": 10, "column": 51}}, "2": {"start": {"line": 11, "column": 28}, "end": {"line": 28, "column": 4}}, "3": {"start": {"line": 30, "column": 26}, "end": {"line": 30, "column": 59}}, "4": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": 36}}, "5": {"start": {"line": 34, "column": 23}, "end": {"line": 34, "column": 63}}, "6": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": 36}}, "7": {"start": {"line": 36, "column": 2}, "end": {"line": 38, "column": 10}}, "8": {"start": {"line": 39, "column": 2}, "end": {"line": 41, "column": 49}}, "9": {"start": {"line": 43, "column": 2}, "end": {"line": 70, "column": 5}}, "10": {"start": {"line": 44, "column": 23}, "end": {"line": 46, "column": 5}}, "11": {"start": {"line": 45, "column": 13}, "end": {"line": 45, "column": 45}}, "12": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 49}}, "13": {"start": {"line": 50, "column": 4}, "end": {"line": 69, "column": 5}}, "14": {"start": {"line": 51, "column": 6}, "end": {"line": 57, "column": 9}}, "15": {"start": {"line": 59, "column": 6}, "end": {"line": 62, "column": 8}}, "16": {"start": {"line": 63, "column": 6}, "end": {"line": 65, "column": 30}}, "17": {"start": {"line": 66, "column": 6}, "end": {"line": 68, "column": 69}}, "18": {"start": {"line": 78, "column": 2}, "end": {"line": 78, "column": 36}}, "19": {"start": {"line": 79, "column": 2}, "end": {"line": 79, "column": 22}}, "20": {"start": {"line": 82, "column": 32}, "end": {"line": 124, "column": 1}}, "21": {"start": {"line": 83, "column": 21}, "end": {"line": 83, "column": 51}}, "22": {"start": {"line": 84, "column": 28}, "end": {"line": 96, "column": 4}}, "23": {"start": {"line": 97, "column": 26}, "end": {"line": 97, "column": 59}}, "24": {"start": {"line": 99, "column": 2}, "end": {"line": 99, "column": 36}}, "25": {"start": {"line": 101, "column": 2}, "end": {"line": 101, "column": 21}}, "26": {"start": {"line": 102, "column": 2}, "end": {"line": 102, "column": 76}}, "27": {"start": {"line": 104, "column": 23}, "end": {"line": 104, "column": 63}}, "28": {"start": {"line": 105, "column": 2}, "end": {"line": 120, "column": 5}}, "29": {"start": {"line": 106, "column": 4}, "end": {"line": 114, "column": 5}}, "30": {"start": {"line": 107, "column": 6}, "end": {"line": 113, "column": 8}}, "31": {"start": {"line": 116, "column": 4}, "end": {"line": 116, "column": 60}}, "32": {"start": {"line": 117, "column": 4}, "end": {"line": 119, "column": 28}}, "33": {"start": {"line": 122, "column": 2}, "end": {"line": 122, "column": 36}}, "34": {"start": {"line": 123, "column": 2}, "end": {"line": 123, "column": 22}}, "35": {"start": {"line": 126, "column": 27}, "end": {"line": 155, "column": 1}}, "36": {"start": {"line": 127, "column": 2}, "end": {"line": 154, "column": 3}}, "37": {"start": {"line": 128, "column": 50}, "end": {"line": 128, "column": 58}}, "38": {"start": {"line": 129, "column": 4}, "end": {"line": 133, "column": 5}}, "39": {"start": {"line": 130, "column": 6}, "end": {"line": 132, "column": 68}}, "40": {"start": {"line": 135, "column": 21}, "end": {"line": 139, "column": 6}}, "41": {"start": {"line": 141, "column": 4}, "end": {"line": 141, "column": 37}}, "42": {"start": {"line": 142, "column": 4}, "end": {"line": 142, "column": 39}}, "43": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 35}}, "44": {"start": {"line": 146, "column": 4}, "end": {"line": 152, "column": 7}}, "45": {"start": {"line": 153, "column": 4}, "end": {"line": 153, "column": 50}}, "46": {"start": {"line": 157, "column": 27}, "end": {"line": 187, "column": 1}}, "47": {"start": {"line": 158, "column": 41}, "end": {"line": 158, "column": 50}}, "48": {"start": {"line": 160, "column": 2}, "end": {"line": 162, "column": 3}}, "49": {"start": {"line": 161, "column": 4}, "end": {"line": 161, "column": 78}}, "50": {"start": {"line": 164, "column": 2}, "end": {"line": 186, "column": 3}}, "51": {"start": {"line": 165, "column": 35}, "end": {"line": 165, "column": 78}}, "52": {"start": {"line": 167, "column": 18}, "end": {"line": 174, "column": 6}}, "53": {"start": {"line": 176, "column": 4}, "end": {"line": 176, "column": 20}}, "54": {"start": {"line": 178, "column": 4}, "end": {"line": 184, "column": 7}}, "55": {"start": {"line": 185, "column": 4}, "end": {"line": 185, "column": 50}}, "56": {"start": {"line": 189, "column": 29}, "end": {"line": 224, "column": 1}}, "57": {"start": {"line": 190, "column": 33}, "end": {"line": 190, "column": 42}}, "58": {"start": {"line": 192, "column": 2}, "end": {"line": 194, "column": 3}}, "59": {"start": {"line": 193, "column": 4}, "end": {"line": 193, "column": 70}}, "60": {"start": {"line": 196, "column": 2}, "end": {"line": 223, "column": 3}}, "61": {"start": {"line": 197, "column": 35}, "end": {"line": 197, "column": 78}}, "62": {"start": {"line": 199, "column": 19}, "end": {"line": 211, "column": 6}}, "63": {"start": {"line": 213, "column": 4}, "end": {"line": 213, "column": 21}}, "64": {"start": {"line": 215, "column": 4}, "end": {"line": 221, "column": 7}}, "65": {"start": {"line": 222, "column": 4}, "end": {"line": 222, "column": 50}}, "66": {"start": {"line": 226, "column": 25}, "end": {"line": 273, "column": 1}}, "67": {"start": {"line": 227, "column": 65}, "end": {"line": 227, "column": 74}}, "68": {"start": {"line": 229, "column": 2}, "end": {"line": 231, "column": 3}}, "69": {"start": {"line": 230, "column": 4}, "end": {"line": 230, "column": 70}}, "70": {"start": {"line": 233, "column": 21}, "end": {"line": 233, "column": 70}}, "71": {"start": {"line": 234, "column": 18}, "end": {"line": 234, "column": 49}}, "72": {"start": {"line": 236, "column": 2}, "end": {"line": 272, "column": 3}}, "73": {"start": {"line": 237, "column": 35}, "end": {"line": 237, "column": 78}}, "74": {"start": {"line": 239, "column": 24}, "end": {"line": 243, "column": 5}}, "75": {"start": {"line": 245, "column": 4}, "end": {"line": 247, "column": 5}}, "76": {"start": {"line": 246, "column": 6}, "end": {"line": 246, "column": 34}}, "77": {"start": {"line": 249, "column": 19}, "end": {"line": 254, "column": 6}}, "78": {"start": {"line": 256, "column": 4}, "end": {"line": 262, "column": 7}}, "79": {"start": {"line": 264, "column": 4}, "end": {"line": 270, "column": 7}}, "80": {"start": {"line": 271, "column": 4}, "end": {"line": 271, "column": 50}}, "81": {"start": {"line": 275, "column": 33}, "end": {"line": 318, "column": 1}}, "82": {"start": {"line": 276, "column": 45}, "end": {"line": 276, "column": 54}}, "83": {"start": {"line": 278, "column": 21}, "end": {"line": 278, "column": 70}}, "84": {"start": {"line": 279, "column": 18}, "end": {"line": 279, "column": 49}}, "85": {"start": {"line": 281, "column": 2}, "end": {"line": 317, "column": 3}}, "86": {"start": {"line": 282, "column": 24}, "end": {"line": 282, "column": 26}}, "87": {"start": {"line": 284, "column": 4}, "end": {"line": 286, "column": 5}}, "88": {"start": {"line": 285, "column": 6}, "end": {"line": 285, "column": 34}}, "89": {"start": {"line": 288, "column": 19}, "end": {"line": 299, "column": 6}}, "90": {"start": {"line": 301, "column": 4}, "end": {"line": 307, "column": 7}}, "91": {"start": {"line": 309, "column": 4}, "end": {"line": 315, "column": 7}}, "92": {"start": {"line": 316, "column": 4}, "end": {"line": 316, "column": 50}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 34}, "end": {"line": 9, "column": 35}}, "loc": {"start": {"line": 9, "column": 54}, "end": {"line": 80, "column": 1}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 43, "column": 40}, "end": {"line": 43, "column": 41}}, "loc": {"start": {"line": 43, "column": 50}, "end": {"line": 70, "column": 3}}, "line": 43}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 7}}, "loc": {"start": {"line": 45, "column": 13}, "end": {"line": 45, "column": 45}}, "line": 45}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 82, "column": 32}, "end": {"line": 82, "column": 33}}, "loc": {"start": {"line": 82, "column": 52}, "end": {"line": 124, "column": 1}}, "line": 82}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 105, "column": 40}, "end": {"line": 105, "column": 41}}, "loc": {"start": {"line": 105, "column": 50}, "end": {"line": 120, "column": 3}}, "line": 105}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 126, "column": 27}, "end": {"line": 126, "column": 28}}, "loc": {"start": {"line": 126, "column": 47}, "end": {"line": 155, "column": 1}}, "line": 126}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 157, "column": 27}, "end": {"line": 157, "column": 28}}, "loc": {"start": {"line": 157, "column": 47}, "end": {"line": 187, "column": 1}}, "line": 157}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 189, "column": 29}, "end": {"line": 189, "column": 30}}, "loc": {"start": {"line": 189, "column": 49}, "end": {"line": 224, "column": 1}}, "line": 189}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 226, "column": 25}, "end": {"line": 226, "column": 26}}, "loc": {"start": {"line": 226, "column": 45}, "end": {"line": 273, "column": 1}}, "line": 226}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 275, "column": 33}, "end": {"line": 275, "column": 34}}, "loc": {"start": {"line": 275, "column": 53}, "end": {"line": 318, "column": 1}}, "line": 275}}, "branchMap": {"0": {"loc": {"start": {"line": 50, "column": 4}, "end": {"line": 69, "column": 5}}, "type": "if", "locations": [{"start": {"line": 50, "column": 4}, "end": {"line": 69, "column": 5}}, {"start": {"line": 58, "column": 11}, "end": {"line": 69, "column": 5}}], "line": 50}, "1": {"loc": {"start": {"line": 106, "column": 4}, "end": {"line": 114, "column": 5}}, "type": "if", "locations": [{"start": {"line": 106, "column": 4}, "end": {"line": 114, "column": 5}}, {"start": {}, "end": {}}], "line": 106}, "2": {"loc": {"start": {"line": 129, "column": 4}, "end": {"line": 133, "column": 5}}, "type": "if", "locations": [{"start": {"line": 129, "column": 4}, "end": {"line": 133, "column": 5}}, {"start": {}, "end": {}}], "line": 129}, "3": {"loc": {"start": {"line": 129, "column": 8}, "end": {"line": 129, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 129, "column": 8}, "end": {"line": 129, "column": 15}}, {"start": {"line": 129, "column": 19}, "end": {"line": 129, "column": 34}}, {"start": {"line": 129, "column": 38}, "end": {"line": 129, "column": 48}}], "line": 129}, "4": {"loc": {"start": {"line": 160, "column": 2}, "end": {"line": 162, "column": 3}}, "type": "if", "locations": [{"start": {"line": 160, "column": 2}, "end": {"line": 162, "column": 3}}, {"start": {}, "end": {}}], "line": 160}, "5": {"loc": {"start": {"line": 160, "column": 6}, "end": {"line": 160, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 160, "column": 6}, "end": {"line": 160, "column": 13}}, {"start": {"line": 160, "column": 17}, "end": {"line": 160, "column": 27}}, {"start": {"line": 160, "column": 31}, "end": {"line": 160, "column": 39}}], "line": 160}, "6": {"loc": {"start": {"line": 192, "column": 2}, "end": {"line": 194, "column": 3}}, "type": "if", "locations": [{"start": {"line": 192, "column": 2}, "end": {"line": 194, "column": 3}}, {"start": {}, "end": {}}], "line": 192}, "7": {"loc": {"start": {"line": 192, "column": 6}, "end": {"line": 192, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 192, "column": 6}, "end": {"line": 192, "column": 16}}, {"start": {"line": 192, "column": 20}, "end": {"line": 192, "column": 28}}], "line": 192}, "8": {"loc": {"start": {"line": 227, "column": 38}, "end": {"line": 227, "column": 50}}, "type": "default-arg", "locations": [{"start": {"line": 227, "column": 48}, "end": {"line": 227, "column": 50}}], "line": 227}, "9": {"loc": {"start": {"line": 227, "column": 52}, "end": {"line": 227, "column": 60}}, "type": "default-arg", "locations": [{"start": {"line": 227, "column": 59}, "end": {"line": 227, "column": 60}}], "line": 227}, "10": {"loc": {"start": {"line": 229, "column": 2}, "end": {"line": 231, "column": 3}}, "type": "if", "locations": [{"start": {"line": 229, "column": 2}, "end": {"line": 231, "column": 3}}, {"start": {}, "end": {}}], "line": 229}, "11": {"loc": {"start": {"line": 229, "column": 6}, "end": {"line": 229, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 229, "column": 6}, "end": {"line": 229, "column": 16}}, {"start": {"line": 229, "column": 20}, "end": {"line": 229, "column": 28}}], "line": 229}, "12": {"loc": {"start": {"line": 245, "column": 4}, "end": {"line": 247, "column": 5}}, "type": "if", "locations": [{"start": {"line": 245, "column": 4}, "end": {"line": 247, "column": 5}}, {"start": {}, "end": {}}], "line": 245}, "13": {"loc": {"start": {"line": 276, "column": 18}, "end": {"line": 276, "column": 30}}, "type": "default-arg", "locations": [{"start": {"line": 276, "column": 28}, "end": {"line": 276, "column": 30}}], "line": 276}, "14": {"loc": {"start": {"line": 276, "column": 32}, "end": {"line": 276, "column": 40}}, "type": "default-arg", "locations": [{"start": {"line": 276, "column": 39}, "end": {"line": 276, "column": 40}}], "line": 276}, "15": {"loc": {"start": {"line": 284, "column": 4}, "end": {"line": 286, "column": 5}}, "type": "if", "locations": [{"start": {"line": 284, "column": 4}, "end": {"line": 286, "column": 5}}, {"start": {}, "end": {}}], "line": 284}}, "s": {"0": 1, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 1, "21": 1, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 1, "36": 3, "37": 3, "38": 3, "39": 1, "40": 2, "41": 1, "42": 0, "43": 0, "44": 2, "45": 2, "46": 1, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 1, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 1, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 1, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 1, "4": 0, "5": 3, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [1, 2], "3": [3, 3, 3], "4": [0, 0], "5": [0, 0, 0], "6": [0, 0], "7": [0, 0], "8": [0], "9": [0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0], "14": [0], "15": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "ad31a29708924fd1b6ff418d37a7b66abf9b7a7e"}, "/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/models/domainReport.js": {"path": "/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/models/domainReport.js", "statementMap": {"0": {"start": {"line": 4, "column": 21}, "end": {"line": 36, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "8ed07b864c6738319f78dbd5be515c2f4651aa64"}, "/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/models/order.js": {"path": "/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/models/order.js", "statementMap": {"0": {"start": {"line": 4, "column": 14}, "end": {"line": 36, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "2af50cacebf8e6a942135c22911e09155727dad1"}, "/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/models/saleReport.js": {"path": "/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/models/saleReport.js", "statementMap": {"0": {"start": {"line": 4, "column": 19}, "end": {"line": 65, "column": 1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "e54826bed0918b5addbc681ae63595ffa04da3ee"}, "/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/utils/logger.js": {"path": "/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/utils/logger.js", "statementMap": {"0": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 21}}, "1": {"start": {"line": 9, "column": 0}, "end": {"line": 11, "column": 1}}, "2": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": 23}}, "3": {"start": {"line": 13, "column": 33}, "end": {"line": 19, "column": 2}}, "4": {"start": {"line": 21, "column": 15}, "end": {"line": 37, "column": 2}}}, "fnMap": {}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 0}, "end": {"line": 11, "column": 1}}, "type": "if", "locations": [{"start": {"line": 9, "column": 0}, "end": {"line": 11, "column": 1}}, {"start": {}, "end": {}}], "line": 9}}, "s": {"0": 1, "1": 1, "2": 0, "3": 1, "4": 1}, "f": {}, "b": {"0": [0, 1]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "90412bc1d8054dd520ee3fef6cf9f0768004ae4d"}}