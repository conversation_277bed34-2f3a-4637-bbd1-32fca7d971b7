<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1721277476704" clover="3.2.0">
  <project timestamp="1721277476704" name="All files">
    <metrics statements="118" coveredstatements="30" conditionals="34" coveredconditionals="6" methods="13" coveredmethods="3" elements="165" coveredelements="39" complexity="0" loc="118" ncloc="118" packages="5" files="7" classes="7"/>
    <package name="nodejs-be-proxy">
      <metrics statements="12" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="1"/>
      <file name="utils.js" path="/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/utils.js">
        <metrics statements="12" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="1"/>
        <line num="3" count="1" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
      </file>
    </package>
    <package name="nodejs-be-proxy.config">
      <metrics statements="5" coveredstatements="2" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="db.js" path="/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/config/db.js">
        <metrics statements="5" coveredstatements="2" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="4" count="2" type="stmt"/>
        <line num="6" count="2" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
      </file>
    </package>
    <package name="nodejs-be-proxy.controllers">
      <metrics statements="93" coveredstatements="16" conditionals="30" coveredconditionals="5" methods="10" coveredmethods="2"/>
      <file name="order.js" path="/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/controllers/order.js">
        <metrics statements="93" coveredstatements="16" conditionals="30" coveredconditionals="5" methods="10" coveredmethods="2"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="127" count="3" type="stmt"/>
        <line num="128" count="3" type="stmt"/>
        <line num="129" count="3" type="cond" truecount="5" falsecount="0"/>
        <line num="130" count="1" type="stmt"/>
        <line num="135" count="2" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="2" type="stmt"/>
        <line num="153" count="2" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="161" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="189" count="1" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="193" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="230" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="245" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="246" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="275" count="1" type="stmt"/>
        <line num="276" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="284" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="285" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
      </file>
    </package>
    <package name="nodejs-be-proxy.models">
      <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="domainReport.js" path="/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/models/domainReport.js">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="1" type="stmt"/>
      </file>
      <file name="order.js" path="/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/models/order.js">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="1" type="stmt"/>
      </file>
      <file name="saleReport.js" path="/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/models/saleReport.js">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="1" type="stmt"/>
      </file>
    </package>
    <package name="nodejs-be-proxy.utils">
      <metrics statements="5" coveredstatements="4" conditionals="2" coveredconditionals="1" methods="0" coveredmethods="0"/>
      <file name="logger.js" path="/Users/<USER>/Projects/Freelancer/nodejs-be-proxy/utils/logger.js">
        <metrics statements="5" coveredstatements="4" conditionals="2" coveredconditionals="1" methods="0" coveredmethods="0"/>
        <line num="6" count="1" type="stmt"/>
        <line num="9" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
