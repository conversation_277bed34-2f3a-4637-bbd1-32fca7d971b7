import express from "express";
import dotenv from "dotenv";
import routesRemote from "./routes/routesRemote.js";
import { compareOrdersHistory } from "./ImportOrder.js";
import cron from 'node-cron';
dotenv.config();

const app = express();
const port = process.env.REMOTE_PORT || 3007;

app.use("/api", routesRemote);
// cron.schedule('33 * * * *', compareOrdersHistory);
app.listen(port, async () => {
  try {
    
    console.log(
      "Connection to the remote database has been established successfully."
    );
    console.log(`Remote server is running on http://localhost:${port}`);
  } catch (error) {
    console.error("Unable to connect to the remote database:", error);
  }
});
