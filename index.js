// import express from "express";
// import dotenv from "dotenv";
// import sequelize from "./config/db.js";
// import orderRoutes from "./routes/orderRoutes.js";
// import { fetchData } from "./httpClient.js";
// import { clearCacheByTag, getCacheByTag } from "./config/cache.js";
// import LocalHistoryOrder from "./models/historyOrder.js";
// import { startCronjob } from "./syncCron.js";
// import { setupTunnel } from "./config/remote.js";
// import sequelizeRemote from "./config/dbRemote.js";

// dotenv.config();

// const app = express();
// const port = process.env.PORT || 3006;

// app.use(express.json());
// app.get("/proxy", async (req, res) => {
//   const { url } = req.query;

//   if (!url) {
//     return res.status(400).send("url is required");
//   }

//   try {
//     const data = await fetchData(url);
//     res.json(data);
//   } catch (error) {
//     res.status(500).send("Internal Server Error");
//   }
// });

// app.get("/clear-cache", (req, res) => {
//   const { tag } = req.query;

//   if (!tag) {
//     return res.status(400).send("Tag is required");
//   }

//   clearCacheByTag(tag);
//   res.send(`Cache cleared for tag: ${tag}`);
// });

// // API to get cache by tag
// app.get("/get-cache", async (req, res) => {
//   const { tag } = req.query;

//   if (!tag) {
//     return res.status(400).send("Tag is required");
//   }

//   try {
//     const data = await getCacheByTag(tag);
//     res.json(data);
//   } catch (error) {
//     res.status(500).send("Internal Server Error");
//   }
// });


// setupTunnel()
//   .then(() => {
//     app.listen(port, async () => {
//       try {
//         await sequelizeRemote.authenticate();
//         console.log(
//           "Connection to the remote database has been established successfully."
//         );
//         await sequelize.authenticate();
//         await sequelize.sync();
//         app.use("/api", orderRoutes);
//         console.log(
//           "Connection to the local database has been established successfully."
//         );

//         console.log(`Server is running on http://localhost:${port}`);
//       } catch (error) {
//         console.error("Unable to connect to the database:", error);
//       }
//     });
//   })
//   .catch((error) => {
//     console.error("Error setting up tunnel:", error);
//   });
