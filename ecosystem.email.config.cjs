module.exports = {
  apps: [
    {
      name: "email-worker",
      script: "./src/workers/emailWorker.js",
      env: {
        NODE_ENV: "production",
        NODE_OPTIONS: '--experimental-modules --es-module-specifier-resolution=node'
      },
      instances: 2,
      exec_mode: "fork",
      autorestart: true,
      watch: false,
      max_memory_restart: "300M",
      exp_backoff_restart_delay: 100,
      merge_logs: true,
      log_date_format: "YYYY-MM-DD HH:mm:ss",
      error_file: "logs/email-worker-err.log",
      out_file: "logs/email-worker-out.log",
      min_uptime: "10s",
      max_restarts: 10,
      kill_timeout: 5000,
    },
    {
      name: "truestore-worker",
      script: "./src/workers/trueStoreWorker.js",
      env: {
        NODE_ENV: "production",
        NODE_OPTIONS: '--experimental-modules --es-module-specifier-resolution=node'
      },
      instances: 2,
      exec_mode: "fork",
      autorestart: true,
      watch: false,
      max_memory_restart: "300M",
      exp_backoff_restart_delay: 100,
      merge_logs: true,
      log_date_format: "YYYY-MM-DD HH:mm:ss",
      error_file: "logs/truestore-worker-err.log",
      out_file: "logs/truestore-worker-out.log",
      min_uptime: "10s",
      max_restarts: 10,
      kill_timeout: 5000,
    }
  ]
}; 