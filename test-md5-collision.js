import crypto from 'crypto';

/**
 * Test MD5 collision risk với 12 chars vs full hash
 */

console.log('🧪 Testing MD5 collision risk\n');

function generateShortHash(data) {
  return crypto.createHash('md5').update(JSON.stringify(data)).digest('hex').substring(0, 12);
}

function generateFullHash(data) {
  return crypto.createHash('md5').update(JSON.stringify(data)).digest('hex'); // Full 32 chars
}

// Test với nhiều variations
const testCases = [];

// Base data
const baseOrder = {
  line_items: [
    { sku: "PROD_001", name: "Product 1", quantity: 1, price: "29.99" }
  ],
  shipping_lines: [
    { method_title: "Standard Shipping", total: "5.99" }
  ]
};

// Generate 1000 slight variations
for (let i = 0; i < 1000; i++) {
  const variation = {
    line_items: [
      { 
        sku: "PROD_001", 
        name: "Product 1", 
        quantity: 1, 
        price: "29.99",
        meta_data: [{ key: "variation", value: `var_${i}` }] // Slight variation
      }
    ],
    shipping_lines: baseOrder.shipping_lines
  };
  testCases.push(variation);
}

console.log(`Generated ${testCases.length} test cases with slight variations\n`);

// Test collision với 12-char hash
console.log('🔍 Testing 12-char MD5 hash collision:');
const shortHashes = new Set();
const shortCollisions = [];

testCases.forEach((testCase, index) => {
  const hash = generateShortHash(testCase);
  
  if (shortHashes.has(hash)) {
    shortCollisions.push({ index, hash, data: testCase });
  } else {
    shortHashes.add(hash);
  }
});

console.log(`Unique 12-char hashes: ${shortHashes.size}/${testCases.length}`);
console.log(`12-char collisions found: ${shortCollisions.length}`);

if (shortCollisions.length > 0) {
  console.log('❌ COLLISION DETECTED with 12-char hash!');
  console.log('First collision example:');
  console.log(`Hash: ${shortCollisions[0].hash}`);
  console.log(`Data: ${JSON.stringify(shortCollisions[0].data, null, 2)}`);
} else {
  console.log('✅ No collisions with 12-char hash in this test');
}

// Test collision với full hash
console.log('\n🔍 Testing FULL MD5 hash collision:');
const fullHashes = new Set();
const fullCollisions = [];

testCases.forEach((testCase, index) => {
  const hash = generateFullHash(testCase);
  
  if (fullHashes.has(hash)) {
    fullCollisions.push({ index, hash, data: testCase });
  } else {
    fullHashes.add(hash);
  }
});

console.log(`Unique full hashes: ${fullHashes.size}/${testCases.length}`);
console.log(`Full hash collisions found: ${fullCollisions.length}`);

if (fullCollisions.length > 0) {
  console.log('❌ COLLISION DETECTED with full hash!');
} else {
  console.log('✅ No collisions with full hash');
}

// Real-world scenario với user's data
console.log('\n🎯 Real-world collision test:');

const realScenarios = [
  {
    line_items: [{ sku: "20241128-60ts-18-01-273RO4", name: "Product", quantity: 1, price: 32.99, meta_data: [{ name: "BUY MORE SAVE MORE", option: "BUY 1" }] }],
    shipping_lines: [{ method_title: "Standard Shipping", total: "5.99" }]
  },
  {
    line_items: [{ sku: "20241128-60ts-18-01-273RO4", name: "Product", quantity: 1, price: 32.99, meta_data: [{ name: "BUY MORE SAVE MORE", option: "BUY 2" }] }],
    shipping_lines: [{ method_title: "Standard Shipping", total: "5.99" }]
  }
];

realScenarios.forEach((scenario, i) => {
  const shortHash = generateShortHash(scenario);
  const fullHash = generateFullHash(scenario);
  console.log(`Scenario ${i + 1}:`);
  console.log(`  12-char: ${shortHash}`);
  console.log(`  Full:    ${fullHash}`);
});

console.log('\n📊 Collision probability:');
console.log(`12-char MD5: 2^48 combinations = ~281 trillion`);
console.log(`Full MD5:    2^128 combinations = ~340 undecillion`);
console.log(`Recommendation: Use FULL MD5 to eliminate collision risk`);

console.log('\n💡 Solution:');
console.log('Change: .substring(0, 12) → Remove substring');
console.log('Result: 32-char hash instead of 12-char');
console.log('Benefit: Virtually zero collision probability'); 