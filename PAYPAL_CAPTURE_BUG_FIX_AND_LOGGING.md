# PayPal Capture Bug Fix & Transaction Logging

## 🚨 **CRITICAL BUG DISCOVERED**

### **Vấn đề hiện tại:**
- Order status: `processing` (API success)
- PayPal dashboard: `pending` (actual payment status)
- **Root Cause**: Code không check `capture.status` sau khi capture

### **Bug trong handleCapturePayPal:**
```javascript
// ❌ BUG: Assume capture luôn thành công
const captureData = await capturePayPalOrder(...);

// ❌ Direct update thành "processing" mà không check capture.status
const { updatedOrder } = await updateOrderAfterPayPalCapture(order, captureData);
```

---

## ✅ **BUG FIX: Correct PayPal Capture Flow**

### **PayPal Standard Process:**
1. **Check order status** = "APPROVED"
2. **Execute capture** 
3. **Check capture.status** ← **MISSING STEP**
4. **Update based on capture result**

### **Fixed handleCapturePayPal:**

```javascript
const handleCapturePayPal = async (req, res) => {
  let order;
  try {
    const { orderId } = req.params;
    
    // Step 1: Find and validate order
    order = await Order.findOne({ where: { paypal_order_id: orderId } });
    if (!order) {
      return res.status(404).json({ error: "Order not found" });
    }

    // Step 2: Check if order is APPROVED
    const paypalOrderStatus = await checkPaymentStatus(orderId, order.paypal_client_id);
    if (paypalOrderStatus !== "APPROVED") {
      return res.status(400).json({
        error: "Order is not approved for capture",
        details: `Current PayPal order status: ${paypalOrderStatus}`,
        code: "ORDER_NOT_APPROVED"
      });
    }

    // Step 3: Execute capture
    const captureData = await capturePayPalOrder(
      order.paypal_client_id,
      orderId,
      order.domain,
      order.orderData?.meta_data || []
    );

    // 🔥 CRITICAL FIX: Check capture status
    const captureStatus = captureData.status;
    const captureDetails = captureData.purchase_units[0]?.payments?.captures?.[0];
    const captureAmount = parseFloat(captureDetails?.amount?.value || 0);
    const expectedAmount = parseFloat(order.orderData.total || 0);

    // Step 4: Process based on capture status
    if (captureStatus === 'COMPLETED') {
      // ✅ SUCCESS: Payment completed
      
      // Security: Validate amount
      if (Math.abs(captureAmount - expectedAmount) > 0.01) {
        return res.status(400).json({
          error: "Amount mismatch",
          details: `Captured ${captureAmount}, expected ${expectedAmount}`,
          code: "AMOUNT_MISMATCH"
        });
      }

      // Update to processing
      const { updatedOrder } = await updateOrderAfterPayPalCapture(
        order, captureData, buildUpdatedOrderData(captureData, order)
      );

      return res.status(200).json(buildSuccessResponse(updatedOrder));

    } else if (captureStatus === 'PENDING') {
      // ⏳ PENDING: Payment under review
      
      await order.update({
        status: "pending", 
        transaction_id: captureDetails?.id || null,
        orderData: {
          ...order.orderData,
          set_paid: false,
          payment_status: "pending",
          capture_status: "PENDING",
          capture_reason: captureDetails?.status_details?.reason || "Under review"
        }
      });

      return res.status(202).json({
        id: order.id,
        status: "pending",
        message: "Payment is pending review",
        details: "You will be notified when payment is completed",
        capture_status: "PENDING",
        transaction_id: captureDetails?.id
      });

    } else if (captureStatus === 'DECLINED') {
      // ❌ DECLINED: Payment failed
      
      await order.update({
        status: "failed",
        orderData: {
          ...order.orderData,
          set_paid: false,
          payment_status: "failed",
          capture_status: "DECLINED"
        }
      });

      return res.status(400).json({
        error: "Payment declined",
        details: "Payment was declined. Try a different payment method.",
        code: "CAPTURE_DECLINED"
      });

    } else {
      // ❓ UNKNOWN: Unexpected status
      return res.status(500).json({
        error: "Unknown capture status",
        details: `PayPal returned unexpected status: ${captureStatus}`,
        code: "UNKNOWN_STATUS"
      });
    }

  } catch (error) {
    // Error handling...
    const paypalError = error.paypalError;
    const orderStatus = paypalError?.canRetry ? "pending" : "failed";

    if (order) {
      await order.update({
        status: orderStatus,
        orderData: {
          ...order.orderData,
          payment_status: orderStatus,
          payment_error: paypalError
        }
      });
    }

    res.status(paypalError?.status || 500).json({
      error: paypalError?.error || "Internal server error",
      details: paypalError?.details || "An unexpected error occurred",
      code: paypalError?.code
    });
  }
};

// Helper functions
function buildUpdatedOrderData(captureData, order) {
  const { payer } = captureData;
  const shipping = captureData.purchase_units[0]?.shipping;
  
  if (!shipping) return order.orderData;

  const shippingData = {
    city: shipping.address.admin_area_2,
    state: shipping.address.admin_area_1,
    country: shipping.address.country_code,
    postcode: shipping.address.postal_code,
    address_1: shipping.address.address_line_1,
    address_2: shipping.address.address_line_2 || "",
    last_name: shipping.name.full_name.split(" ").pop(),
    first_name: shipping.name.full_name.split(" ").shift(),
  };

  return {
    ...order.orderData,
    billing: {
      ...shippingData,
      phone: payer.phone?.phone_number?.national_number || "",
      email: payer.email_address,
    },
    set_paid: true,
    shipping: shippingData,
  };
}

function buildSuccessResponse(updatedOrder) {
  return {
    id: updatedOrder.id,
    order_key: updatedOrder.order_key,
    domain: updatedOrder.domain,
    transaction_id: updatedOrder.transaction_id,
    status: updatedOrder.status,
    orderData: {
      total: updatedOrder.orderData.total,
      sub_total: updatedOrder.orderData.sub_total,
      shipping_total: updatedOrder.orderData.shipping_total,
      billing: updatedOrder.orderData.billing,
      shipping: updatedOrder.orderData.shipping,
      line_items: updatedOrder.orderData.line_items,
      payment_method: updatedOrder.orderData.payment_method,
      payment_method_title: updatedOrder.orderData.payment_method_title,
      set_paid: updatedOrder.orderData.set_paid,
      invoice_id: updatedOrder.orderData.invoice_id,
      funding_source: updatedOrder.orderData.funding_source,
    },
    createdAt: updatedOrder.createdAt,
    updatedAt: updatedOrder.updatedAt,
  };
}
```

---

## 📊 **TRANSACTION LOGGING SYSTEM (REVISED)**

### **New Logging Strategy:**
- **Only log the final outcome of each PayPal capture attempt per transaction.**
- **Event types:**
  - `CAPTURE_SUCCESS` (capture completed)
  - `CAPTURE_PENDING` (capture under review)
  - `CAPTURE_FAILED` (capture declined or failed)
  - `CAPTURE_ERROR` (unexpected error)
- **No intermediate ATTEMPT/SUCCESS/FAILED logs for the same transaction.**
- **Each PayPal capture attempt = 1 record (final outcome only).**

### **Database Schema (unchanged):**
```javascript
// TransactionEventLog Model
const TransactionEventLog = sequelize.define("TransactionEventLog", {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  },
  order_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: { model: 'Orders', key: 'id' }
  },
  
  // Event Classification (chỉ PayPal capture events)
  event_type: {
    type: DataTypes.ENUM(
      'CAPTURE_SUCCESS',
      'CAPTURE_PENDING',
      'CAPTURE_FAILED',
      'CAPTURE_ERROR'
    ),
    allowNull: false,
  },
  
  // PayPal Data
  paypal_order_id: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  transaction_id: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  
  // Status Tracking
  old_status: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  new_status: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  
  // Data Storage
  paypal_api_response: {
    type: DataTypes.JSON,
    allowNull: true,
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  
  // Metadata from orderData.meta_data (as JSON)
  metadata: {
    type: DataTypes.JSON,
    allowNull: true, // Store all meta_data as JSON
  },
  
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  }
}, {
  indexes: [
    { fields: ['order_id'] },
    { fields: ['event_type'] },
    { fields: ['paypal_order_id'] },
    { fields: ['transaction_id'] },
    { fields: ['created_at'] },
  ]
});

// No changes needed to existing Order model
```

### **TransactionLogger Service (revised):**
```javascript
// src/services/transactionLogger.js
class TransactionLogger {
  // Extract metadata from existing orderData.meta_data (keep as JSON)
  static extractMetadata(order) {
    return order.orderData?.meta_data || null;
  }

  static async logFinalOutcome({
    orderId,
    eventType, // Only: CAPTURE_SUCCESS | CAPTURE_PENDING | CAPTURE_FAILED | CAPTURE_ERROR
    paypalOrderId = null,
    transactionId = null,
    oldStatus = null,
    newStatus = null,
    paypalApiResponse = null,
    notes = null,
    order = null
  }) {
    try {
      // Extract metadata from order (as JSON)
      let metadata = null;
      if (order) {
        metadata = this.extractMetadata(order);
      }

      const eventLog = await TransactionEventLog.create({
        order_id: orderId,
        event_type: eventType,
        paypal_order_id: paypalOrderId,
        transaction_id: transactionId,
        old_status: oldStatus,
        new_status: newStatus,
        paypal_api_response: paypalApiResponse,
        notes: notes,
        
        // Store metadata as JSON
        metadata: metadata
      });

      // No need to update order table

      logger.info("Transaction event logged", {
        eventLogId: eventLog.id,
        orderId,
        eventType,
        transactionId
      });

      return eventLog;
    } catch (error) {
      logger.error("Failed to log transaction event", {
        error: error.message,
        orderId,
        eventType
      });
    }
  }

  // Helper: Get order transaction history
  static async getOrderHistory(orderId) {
    return await TransactionEventLog.findAll({
      where: { order_id: orderId },
      order: [['created_at', 'ASC']]
    });
  }

  // Helper: Search by transaction ID
  static async findByTransactionId(transactionId) {
    return await TransactionEventLog.findAll({
      where: { transaction_id: transactionId },
      include: [{ model: Order }],
      order: [['created_at', 'DESC']]
    });
  }

  // Helper: Search by metadata (JSON search)
  static async findByMetadata(searchKey, searchValue) {
    return await TransactionEventLog.findAll({
      where: sequelize.literal(`JSON_EXTRACT(metadata, '$.${searchKey}') = '${searchValue}'`),
      include: [{ model: Order }],
      order: [['created_at', 'DESC']]
    });
  }
}

export default TransactionLogger;
```

### **handleCapturePayPal (revised logging):**
```javascript
const handleCapturePayPal = async (req, res) => {
  let order;
  try {
    const { orderId } = req.params;
    order = await Order.findOne({ where: { paypal_order_id: orderId } });
    if (!order) {
      return res.status(404).json({ error: "Order not found" });
    }
    // ... check status, capture, etc ...
    if (captureStatus === 'COMPLETED') {
      // ... update order ...
      await TransactionLogger.logFinalOutcome({
        orderId: updatedOrder.id,
        eventType: 'CAPTURE_SUCCESS',
        paypalOrderId: orderId,
        transactionId: captureDetails.id,
        oldStatus: order.status,
        newStatus: 'processing',
        paypalApiResponse: captureData,
        notes: `Capture completed successfully. Amount: ${captureDetails.amount.value}`,
        order: order
      });
      return res.status(200).json(buildSuccessResponse(updatedOrder));
    } else if (captureStatus === 'PENDING') {
      // ... update order ...
      await TransactionLogger.logFinalOutcome({
        orderId: order.id,
        eventType: 'CAPTURE_PENDING',
        paypalOrderId: orderId,
        transactionId: captureDetails?.id,
        oldStatus: order.status,
        newStatus: 'pending',
        paypalApiResponse: captureData,
        notes: `Capture pending: ${captureDetails?.status_details?.reason || 'Payment under review'}`,
        order: order
      });
      return res.status(202).json({/*...*/});
    } else if (captureStatus === 'DECLINED') {
      // ... update order ...
      await TransactionLogger.logFinalOutcome({
        orderId: order.id,
        eventType: 'CAPTURE_FAILED',
        paypalOrderId: orderId,
        transactionId: captureDetails?.id,
        oldStatus: order.status,
        newStatus: 'failed',
        paypalApiResponse: captureData,
        notes: `Capture declined`,
        order: order
      });
      return res.status(400).json({/*...*/});
    } else {
      // ... unknown status ...
      await TransactionLogger.logFinalOutcome({
        orderId: order.id,
        eventType: 'CAPTURE_ERROR',
        paypalOrderId: orderId,
        transactionId: captureDetails?.id,
        oldStatus: order.status,
        newStatus: order.status,
        paypalApiResponse: captureData,
        notes: `Unknown capture status: ${captureStatus}`,
        order: order
      });
      return res.status(500).json({/*...*/});
    }
  } catch (error) {
    if (order?.id) {
      await TransactionLogger.logFinalOutcome({
        orderId: order.id,
        eventType: 'CAPTURE_ERROR',
        paypalOrderId: req.params.orderId,
        notes: `Capture error: ${error.message}`,
        paypalApiResponse: error.paypalError || null,
        order: order
      });
    }
    // ... error response ...
  }
};
```

### **API Endpoints (unchanged, but only 1 record per capture attempt):**

```javascript
// Get transaction history for an order
const getOrderTransactionHistory = async (req, res) => {
  try {
    const { orderId } = req.params;
    
    const order = await Order.findByPk(orderId);
    if (!order) {
      return res.status(404).json({ error: "Order not found" });
    }

    const history = await TransactionLogger.getOrderHistory(orderId);
    
    res.json({
      orderId: order.id,
      orderKey: order.order_key,
      paypalOrderId: order.paypal_order_id,
      transactionId: order.transaction_id,
      currentStatus: order.status,
      history: history
    });
  } catch (error) {
    logger.error("Error getting order transaction history", {
      error: error.message,
      orderId: req.params.orderId
    });
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// Search by transaction ID
const searchByTransactionId = async (req, res) => {
  try {
    const { transactionId } = req.params;
    const events = await TransactionLogger.findByTransactionId(transactionId);
    
    if (events.length === 0) {
      return res.status(404).json({ error: "Transaction not found" });
    }

    res.json({
      transactionId: transactionId,
      eventsFound: events.length,
      events: events
    });
  } catch (error) {
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// Search by metadata (invoice_id, funding_source, etc.)
const searchByMetadata = async (req, res) => {
  try {
    const { key, value } = req.params;
    const events = await TransactionLogger.findByMetadata(key, value);
    
    if (events.length === 0) {
      return res.status(404).json({ error: `No events found for ${key}=${value}` });
    }

    res.json({
      searchKey: key,
      searchValue: value,
      eventsFound: events.length,
      events: events
    });
  } catch (error) {
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// Routes
router.get("/orders/:orderId/transaction-history", getOrderTransactionHistory);
router.get("/transactions/:transactionId/search", searchByTransactionId);
router.get("/invoices/:invoiceId/search", searchByInvoiceId);
```

### **Database Usage Examples (revised):**
```sql
-- Only 1 record per capture attempt, outcome only
SELECT * FROM TransactionEventLogs 
WHERE order_id = 777188 
ORDER BY created_at ASC;

-- Find all failed captures
SELECT * FROM TransactionEventLogs 
WHERE event_type = 'CAPTURE_FAILED'
ORDER BY created_at DESC;

-- Find all pending captures
SELECT * FROM TransactionEventLogs 
WHERE event_type = 'CAPTURE_PENDING'
ORDER BY created_at DESC;

-- Check capture outcome rate
SELECT 
  event_type, 
  COUNT(*) as count,
  (COUNT(*) * 100.0 / (SELECT COUNT(*) FROM TransactionEventLogs)) as percentage
FROM TransactionEventLogs 
GROUP BY event_type;
```

**Với chiến lược này, mỗi lần capture chỉ log 1 record duy nhất, phản ánh outcome cuối cùng. Không log các bước trung gian.**

**Đây là giải pháp hoàn chỉnh cho vấn đề hiện tại!**

---

## **Hướng dẫn migrate TransactionEventLog**
```bash
npx sequelize-cli db:migrate
``` 