const formData = require("form-data");
const Mailgun = require("mailgun.js");
const mailgun = new Mailgun(formData);
const fs = require("fs").promises;
const path = require("path");
const Handlebars = require("handlebars");
const axios = require("axios");

const keyTracking = "**************************************************";
const mg = mailgun.client({
  username: "api",
  key: keyTracking,
});

const DOMAIN = "true-tracking.com";

// Hàm helper để format tiền tệ
Handlebars.registerHelper("currency", function (value) {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(value);
});

// Đọc template
async function getTemplate() {
  const templatePath = path.join(
    __dirname,
    "..",
    "templates",
    "emails",
    "shippingConfirmation.html"
  );
  return await fs.readFile(templatePath, "utf-8");
}

// Fetch dữ liệu từ Firebase
async function fetchShopData(domain) {
  try {
    const shopKey = domain.replace(/\./g, "");
    const url = `https://king-fruit-slot.firebaseio.com/PXTRUE2/${shopKey}/.json`;
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error("Error fetching shop data:", error);
    return null;
  }
}

// Hàm để tạo URL tracking dựa trên tracking_id
function getTrackingUrl(tracking_id) {
  const baseUrl = "https://t.17track.net/en#nums=";
  if (tracking_id.startsWith("9")) {
    return `${baseUrl}${tracking_id}&fc=190199`;
  } else if (tracking_id.startsWith("Y")) {
    return `${baseUrl}${tracking_id}`;
  } else {
    // Trường hợp mặc định, có thể điều chỉnh theo nhu cầu
    return `${baseUrl}${tracking_id}`;
  }
}

// Hàm để xác định carrier dựa trên tracking_id
function getCarrier(tracking_id) {
  if (tracking_id.startsWith("9")) {
    return "JS EXPRESS";
  } else if (tracking_id.startsWith("Y")) {
    return "YUNEXPRESS";
  } else {
    // Trường hợp mặc định, có thể điều chỉnh theo nhu cầu
    return "Unknown Carrier";
  }
}

// Dữ liệu đơn hàng mẫu (đã cập nhật với thông tin shipping)
const sampleOrder = {
  id: 56764,
  order_key: "QuePabDQeY",
  domain: "cobaltcascadez.com",
  transaction_id: "YT2427021272130762",
  orderData: {
    total: "43.98",
    billing: {
      city: "Victoria",
      email: "<EMAIL>",
      phone: "",
      state: "TX",
      country: "US",
      postcode: "77901",
      address_1: "2909 Cypress Street",
      address_2: "",
      last_name: "conchola",
      first_name: "al",
    },
    set_paid: true,
    shipping: {
      city: "Victoria",
      state: "TX",
      country: "US",
      postcode: "77901",
      address_1: "2909 Cypress Street",
      address_2: "",
      last_name: "conchola",
      first_name: "al",
    },
    line_items: [
      {
        sku: "20240927-15ts-16-01-H61W2W",
        name: "🔥Last Day 49% OFF🎁Furniture lift mover tool",
        image:
          "https://admin.cobaltcascadez.com/wp-content/uploads/2024/09/5c9dd3859fedf3f741f68f4b1372e96ae74fab02f76766c886d696b2388fea54-600-ezgif.com-avif-to-jpg-converter.jpg",
        price: 38.99,
        total: "38.99",
        quantity: 1,
        meta_data: [
          {
            id: 0,
            name: "Color",
            option: "BLACK",
          },
          {
            id: 0,
            name: "Buy More Save More",
            option: "BUY 1",
          },
        ],
        product_id: 9287,
        product_link:
          "https://admin.cobaltcascadez.com/product/liftmovertool015ts/",
        variation_id: 9290,
      },
    ],
    payment_method: "ppcp-gateway",
    shipping_lines: [
      {
        total: "4.99",
        method_id: "flat_rate",
      },
    ],
    shipping_total: "4.99",
    transaction_id: "1J288738DX703933C",
    payment_method_title: "Credit or debit cards (PayPal)",
  },
  status: "shipped",
  tracking_id: "YT2427021272130762",
  port: null,
  createdAt: "2024-10-01 23:41:42",
  updatedAt: "2024-10-02 10:15:30",
};

async function sendShippingEmail() {
  try {
    const template = await getTemplate();
    const compiledTemplate = Handlebars.compile(template);
    const shopData = await fetchShopData(sampleOrder.domain);

    if (!shopData) {
      throw new Error("Failed to fetch shop data");
    }

    const shopEmail = shopData.email || "<EMAIL>";

    // Tạo URL tracking và xác định carrier
    const tracking_url = getTrackingUrl(sampleOrder.tracking_id);
    const carrier = getCarrier(sampleOrder.tracking_id);

    // Kết hợp dữ liệu đơn hàng với shopEmail và thông tin shipping
    const templateData = {
      ...sampleOrder,
      shopEmail: shopEmail,
      tracking_url: tracking_url,
      carrier: carrier,
    };

    const html = compiledTemplate(templateData);

    const result = await mg.messages.create(DOMAIN, {
      from: `Shipping Confirmation #${sampleOrder.id} <<EMAIL>>`,
      "h:Reply-To": shopEmail,
      to: ["<EMAIL>", "<EMAIL>"],
      subject: `Your order from ${sampleOrder.domain} has been shipped!`,
      html: html,
    });

    console.log(result);
  } catch (error) {
    console.error("Error sending shipping confirmation email:", error);
  }
}

sendShippingEmail();
