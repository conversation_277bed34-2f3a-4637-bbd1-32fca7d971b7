import axios from 'axios';

const orderData = {
    billing: {
        address_1: "5240 mcingvale",
        address_2: "",
        city: "Hernando",
        country: "US",
        email: "<EMAIL>",
        first_name: "paula",
        last_name: "powers",
        phone: "",
        postcode: "38632",
        state: "MS"
    },
    shipping: {
        address_1: "5240 mcingvale",
        address_2: "",
        city: "Hernando",
        country: "US",
        first_name: "paula",
        last_name: "powers",
        postcode: "38632",
        state: "MS"
    },
    payment_method: "ppcp-gateway",
    payment_method_title: "Paypal",
    set_paid: true,
    transaction_id: "5VF85220HR562640F",
    line_items: [
        {
            product_id: 2572,
            variation_id: 2573,
            quantity: 1
        }
    ],
    shipping_lines: [
        {
            method_id: "flate_rate",
            total: "6.99"
        }
    ],
    meta_data: [
        {},
        {
            key: "UA",
            value: "Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/21F90 [FBAN/FBIOS;FBAV/470.**********;FBBV/616253809;FBDV/iPhone14,7;FBMD/iPhone;FBSN/iOS;FBSV/17.5.1;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5;FBRV/617330632]"
        },
        {
            key: "QUERY",
            value: "sugranie.com/product/comfortbra01?fbclid=IwZXh0bgNhZW0BMAABHZOZ9o-00GOBKchS9tHvDzUqp1ZTcimJ5wr2vHsgh6AJ0ZlDzVB9VXE90g_aem_52az2EvmnTS51QfHuiDa-w&utm_medium=paid&utm_source=fb&utm_id=120211396334660536&utm_content=120211396335930536&utm_term=120211396335450536&utm_campaign=120211396334660536"
        },
        {
            key: "FB_UTM",
            value: "fbDHVpaidDHV120211396334660536DHV120211396335930536DHV120211396335450536"
        },
        {
            key: "invoice_id",
            value: "sugranie-bt706-33131.53106874507"
        },
        {
            key: "_ppcp_paypal_order_id",
            value: "5VF85220HR562640F"
        },
        {
            key: "_ppcp_paypal_intent",
            value: "CAPTURE"
        },
        {
            key: "_ppcp_paypal_payment_mode",
            value: "live"
        }
    ]
};

async function insertOrders() {
    const domain = '1siteclone';
    const startTime = Date.now();

    for (let i = 0; i < 1000; i++) {
        try {
            await axios.post('http://localhost:3006/api/orders', {
                domain,
                orderData: orderData,
                transaction_id: "1siteclone"
            });
        } catch (error) {
            console.error(`Failed to insert order ${i + 1}:`, error.message);
        }
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000; // convert to seconds
    console.log(`Inserted 1000 orders in ${duration} seconds.`);
}

insertOrders().then(() => {
    console.log('Finished inserting orders.');
    process.exit();
}).catch(error => {
    console.error('Error inserting orders:', error);
    process.exit(1);
});
