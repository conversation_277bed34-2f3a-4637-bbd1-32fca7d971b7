import axios from "axios";
import qs from "qs";
import { <PERSON><PERSON><PERSON> } from "buffer";

const PAYPAL_API = "https://api-m.sandbox.paypal.com";
const client_id =
  "AbGDiSDqoEj4eAGaTajmisiQGpbG1D38F9hv5W3Vu9_GeQpx8wMoThiSm8huWkE1SSzuZxDe9AeTn6HJ";
const secret_id =
  "EJZOmFcZFQFhxOkoMxxux7aVNVUjpWCtko_AaFKyqENYWQf5FbgcWz08zjqs9pGUElp7vfGfhf1LoJbz";

const getAccessToken = async () => {
  const auth = Buffer.from(`${client_id}:${secret_id}`).toString("base64");
  const config = {
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      Authorization: `Basic ${auth}`,
    },
  };

  const data = qs.stringify({ grant_type: "client_credentials" });

  try {
    const response = await axios.post(
      `${PAYPAL_API}/v1/oauth2/token`,
      data,
      config
    );
    return response.data.access_token;
  } catch (error) {
    console.error(
      "Error getting access token:",
      error.response ? error.response.data : error.message
    );
  }
};

const createTrackers = async (accessToken) => {
  const trackersData = {
    trackers: [
      {
        transaction_id: "7G047978CW500354L",
        tracking_number: "443844607820",
        status: "SHIPPED",
        carrier: "JS EXPRESS",
      },
    ],
  };

  const config = {
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
  };

  try {
    const response = await axios.post(`${PAYPAL_API}/v1/shipping/trackers-batch`, trackersData, config);
    console.log("Response:", response.data);
  } catch (error) {
    console.log(error.response)
    console.error(
      "Error creating trackers:",
      error.response ? error.response.data : error.message
    );
  }
};

const main = async () => {
  const accessToken = await getAccessToken();
  if (accessToken) {
    await createTrackers(accessToken);
  }
};

main();
