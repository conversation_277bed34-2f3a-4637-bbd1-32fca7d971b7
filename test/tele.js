import 'dotenv/config';
import TelegramBot from 'node-telegram-bot-api';

const token = process.env.TELEGRAM_BOT_TOKEN;

const bot = new TelegramBot(token, { polling: true });

const groupId = '-4203594287';

const message = 'Hello, this is a test message from my bot!';

bot.sendMessage(groupId, message)
  .then((res) => {
    console.log('Message sent successfully:', res);
  })
  .catch((err) => {
    console.error('Error while sending message:', err);
  });
