const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const Handlebars = require('handlebars');

const API_KEY = '815a76f432c4761c2b90f7ab8566d4f38f970d27';
const API_URL = 'https://api.smtp.com/v4/messages';
const CHANNEL = 'dev_truestore_vn';

// Hàm helper để format tiền tệ
Handlebars.registerHelper("currency", function (value) {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(value);
});

// Đọc template
async function getTemplate() {
  const templatePath = path.join(
    __dirname,
    "..",
    "templates",
    "emails",
    "orderConfirmation.html"
  );
  return await fs.readFile(templatePath, "utf-8");
}

// Fetch dữ liệu từ Firebase
async function fetchShopData(domain) {
  try {
    const shopKey = domain.replace(/\./g, "");
    const url = `https://king-fruit-slot.firebaseio.com/PXTRUE2/${shopKey}/.json`;
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error("Error fetching shop data:", error);
    return null;
  }
}

// Dữ liệu đơn hàng mẫu
const sampleOrder = {
  id: 56764,
  order_key: "QuePabDQeY",
  domain: "cobaltcascadez.com",
  transaction_id: "1J288738DX703933C",
  orderData: {
    total: "43.98",
    billing: {
      city: "Victoria",
      email: "<EMAIL>",
      phone: "",
      state: "TX",
      country: "US",
      postcode: "77901",
      address_1: "2909 Cypress Street",
      address_2: "",
      last_name: "conchola",
      first_name: "al",
    },
    set_paid: true,
    shipping: {
      city: "Victoria",
      state: "TX",
      country: "US",
      postcode: "77901",
      address_1: "2909 Cypress Street",
      address_2: "",
      last_name: "conchola",
      first_name: "al",
    },
    line_items: [
      {
        sku: "20240927-15ts-16-01-H61W2W",
        name: "🔥Last Day 49% OFF🎁Furniture lift mover tool",
        image:
          "https://admin.cobaltcascadez.com/wp-content/uploads/2024/09/5c9dd3859fedf3f741f68f4b1372e96ae74fab02f76766c886d696b2388fea54-600-ezgif.com-avif-to-jpg-converter.jpg",
        price: 38.99,
        total: "38.99",
        quantity: 1,
        meta_data: [
          {
            id: 0,
            name: "Color",
            option: "BLACK",
          },
          {
            id: 0,
            name: "Buy More Save More",
            option: "BUY 1",
          },
        ],
        product_id: 9287,
        product_link:
          "https://admin.cobaltcascadez.com/product/liftmovertool015ts/",
        variation_id: 9290,
      },
    ],
    payment_method: "ppcp-gateway",
    shipping_lines: [
      {
        total: "4.99",
        method_id: "flat_rate",
      },
    ],
    shipping_total: "4.99",
    transaction_id: "1J288738DX703933C",
    payment_method_title: "Credit or debit cards (PayPal)",
  },
  status: "processing",
  tracking_id: null,
  port: null,
  createdAt: "2024-10-01 23:41:42",
  updatedAt: "2024-10-01 23:41:42",
};

async function sendEmail() {
  try {
    const template = await getTemplate();
    const compiledTemplate = Handlebars.compile(template);
    const shopData = await fetchShopData(sampleOrder.domain);

    if (!shopData) {
      throw new Error("Failed to fetch shop data");
    }

    // Sử dụng email từ dữ liệu shop nếu có, nếu không sử dụng email mặc định
    const shopEmail = shopData.email || "<EMAIL>";

    // Kết hợp dữ liệu đơn hàng với shopEmail
    const templateData = {
      ...sampleOrder,
      shopEmail: shopEmail,
    };

    const html = compiledTemplate(templateData);

    const emailData = {
      "recipients": {
        "to": [
          {
            "address": "<EMAIL>"
          },
          {
            "address": "<EMAIL>"
          }
        ]
      },
      "originator": {
        "from": {
          "name": `Order Confirmation #${sampleOrder.id}`,
          "address": "<EMAIL>"
        },
        "reply_to": {
          "address": shopEmail
        }
      },
      "subject": `Your order confirmation from ${sampleOrder.domain}`,
      "body": {
        "parts": [
          {
            "version": "1.0",
            "type": "text/html",
            "charset": "UTF-8",
            "content": html
          }
        ]
      }
    };

    const response = await axios.post(
      `${API_URL}?channel=${CHANNEL}`,
      emailData,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${API_KEY}`
        }
      }
    );

    console.log('Email sent successfully:', response.data);
  } catch (error) {
    console.error('Error sending email:', error.response ? error.response.data : error.message);
  }
}

sendEmail();
