{"9F872162LU2685344": {"success": true, "data": {"id": "9F872162LU2685344", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "hexluvus-omzse-35572", "custom_id": "card", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "285160308W471941T"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "BU2HEWRHR2DGN"}, "create_time": "2024-10-18T14:08:42Z", "update_time": "2024-10-18T14:08:42Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/9F872162LU2685344", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/9F872162LU2685344/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/285160308W471941T", "rel": "up", "method": "GET"}]}, "account": "C114"}, "6KR989493N487204S": {"success": true, "data": {"id": "6KR989493N487204S", "amount": {"currency_code": "USD", "value": "36.94"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "36.94"}, "paypal_fee": {"currency_code": "USD", "value": "1.93"}, "net_amount": {"currency_code": "USD", "value": "35.01"}}, "invoice_id": "toygad-chcwk-66932", "custom_id": "card", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "64F10667CU135474H"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "BU2HEWRHR2DGN"}, "create_time": "2024-10-18T15:46:30Z", "update_time": "2024-10-18T15:46:30Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/6KR989493N487204S", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/6KR989493N487204S/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/64F10667CU135474H", "rel": "up", "method": "GET"}]}, "account": "C114"}, "12B310378N001511C": {"success": true, "data": {"id": "12B310378N001511C", "amount": {"currency_code": "USD", "value": "36.94"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "36.94"}, "paypal_fee": {"currency_code": "USD", "value": "1.93"}, "net_amount": {"currency_code": "USD", "value": "35.01"}}, "invoice_id": "toygad-vg2qo-10833", "custom_id": "card", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "9P005624BE2478312"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "BU2HEWRHR2DGN"}, "create_time": "2024-10-18T15:49:06Z", "update_time": "2024-10-18T15:49:06Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/12B310378N001511C", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/12B310378N001511C/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/9P005624BE2478312", "rel": "up", "method": "GET"}]}, "account": "C114"}, "91H84068T1840851E": {"success": true, "data": {"id": "91H84068T1840851E", "amount": {"currency_code": "USD", "value": "33.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "33.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.80"}, "net_amount": {"currency_code": "USD", "value": "32.18"}}, "invoice_id": "goaheadus-toahg-43014", "custom_id": "card", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "72K082673A089642U"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "BU2HEWRHR2DGN"}, "create_time": "2024-10-22T01:13:15Z", "update_time": "2024-10-22T01:13:15Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/91H84068T1840851E", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/91H84068T1840851E/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/72K082673A089642U", "rel": "up", "method": "GET"}]}, "account": "C114"}, "53K590175V409943F": {"success": true, "data": {"id": "53K590175V409943F", "amount": {"currency_code": "USD", "value": "33.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "33.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.80"}, "net_amount": {"currency_code": "USD", "value": "32.18"}}, "invoice_id": "goaheadus-qzcy5-85688", "custom_id": "paypal", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "0TX93725YH2521727"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "BU2HEWRHR2DGN"}, "create_time": "2024-10-22T02:00:28Z", "update_time": "2024-10-22T02:00:28Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/53K590175V409943F", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/53K590175V409943F/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/0TX93725YH2521727", "rel": "up", "method": "GET"}]}, "account": "C114"}, "7US0622368778491W": {"success": true, "data": {"id": "7US0622368778491W", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "zipdawn-5a-**************", "custom_id": "paypal", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "6RY07036CM825061G"}}, "payee": {"email_address": "contact.kus<PERSON><EMAIL>", "merchant_id": "EFSTJVRQADT56"}, "create_time": "2024-10-22T11:56:08Z", "update_time": "2024-10-22T11:56:08Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/7US0622368778491W", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/7US0622368778491W/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/6RY07036CM825061G", "rel": "up", "method": "GET"}]}, "account": "C121"}, "1EL09073EX1682212": {"success": true, "data": {"id": "1EL09073EX1682212", "amount": {"currency_code": "USD", "value": "54.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "54.98"}, "paypal_fee": {"currency_code": "USD", "value": "2.72"}, "net_amount": {"currency_code": "USD", "value": "52.26"}}, "invoice_id": "enjoyiz-lv-**************", "custom_id": "card", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "9CD05982G24894102"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "6T55USYPWZ6X2"}, "create_time": "2024-10-22T14:18:48Z", "update_time": "2024-10-22T14:18:48Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/1EL09073EX1682212", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/1EL09073EX1682212/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/9CD05982G24894102", "rel": "up", "method": "GET"}]}, "account": "C129"}, "5CK317616Y5937813": {"success": true, "data": {"id": "5CK317616Y5937813", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "invoice_id": "imagineitus-22-**************", "custom_id": "card", "status": "PENDING", "status_details": {"reason": "PENDING_REVIEW"}, "supplementary_data": {"related_ids": {"order_id": "8BF18200M86791526"}}, "payee": {"email_address": "contact.kus<PERSON><EMAIL>", "merchant_id": "EFSTJVRQADT56"}, "create_time": "2024-10-23T12:50:12Z", "update_time": "2024-10-23T12:50:17Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/5CK317616Y5937813", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/5CK317616Y5937813/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/8BF18200M86791526", "rel": "up", "method": "GET"}]}, "account": "C121"}, "5AA725891K604962H": {"success": true, "data": {"id": "5AA725891K604962H", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "popjarus-dr-**************", "custom_id": "paypal", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "4GA78977SD454083V"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "6T55USYPWZ6X2"}, "create_time": "2024-10-23T14:10:19Z", "update_time": "2024-10-23T14:10:19Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/5AA725891K604962H", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/5AA725891K604962H/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/4GA78977SD454083V", "rel": "up", "method": "GET"}]}, "account": "C129"}, "3RG04706Y5062091K": {"success": true, "data": {"id": "3RG04706Y5062091K", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "imagineitus-d7-**************", "custom_id": "card", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "2CE71335LN798611R"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "BCWDQEJ4FHPF2"}, "create_time": "2024-10-23T14:14:17Z", "update_time": "2024-10-23T14:14:17Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/3RG04706Y5062091K", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/3RG04706Y5062091K/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/2CE71335LN798611R", "rel": "up", "method": "GET"}]}, "account": "C131"}, "95N49891HH304081K": {"success": true, "data": {"id": "95N49891HH304081K", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "imagineitus-kf-**************", "custom_id": "card", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "40N561308T984784A"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "FVAGAWBBHCK42"}, "create_time": "2024-10-23T15:01:24Z", "update_time": "2024-10-23T15:01:24Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/95N49891HH304081K", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/95N49891HH304081K/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/40N561308T984784A", "rel": "up", "method": "GET"}]}, "account": "C138"}, "0KE19041PC544260T": {"success": true, "data": {"id": "0KE19041PC544260T", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "zipdawn-gn-**************", "custom_id": "card", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "4NB852817R786794W"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "FVAGAWBBHCK42"}, "create_time": "2024-10-23T15:05:41Z", "update_time": "2024-10-23T15:05:41Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/0KE19041PC544260T", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/0KE19041PC544260T/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/4NB852817R786794W", "rel": "up", "method": "GET"}]}, "account": "C138"}, "95X84202LR828020C": {"success": true, "data": {"id": "95X84202LR828020C", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "imagineitus-g8-**************", "custom_id": "card", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "82J01324FT972704V"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "FVAGAWBBHCK42"}, "create_time": "2024-10-23T15:28:39Z", "update_time": "2024-10-23T15:28:39Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/95X84202LR828020C", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/95X84202LR828020C/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/82J01324FT972704V", "rel": "up", "method": "GET"}]}, "account": "C138"}, "4M007282VX8587708": {"success": true, "data": {"id": "4M007282VX8587708", "amount": {"currency_code": "USD", "value": "157.94"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "157.94"}, "paypal_fee": {"currency_code": "USD", "value": "7.25"}, "net_amount": {"currency_code": "USD", "value": "150.69"}}, "invoice_id": "imagineitus-oc-**************", "custom_id": "card", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "1AY62826G50984814"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "VJRFVRYDMJQR8"}, "create_time": "2024-10-23T16:40:40Z", "update_time": "2024-10-23T16:40:40Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/4M007282VX8587708", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/4M007282VX8587708/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/1AY62826G50984814", "rel": "up", "method": "GET"}]}, "account": "C130"}, "3B873530AV470590L": {"success": true, "data": {"id": "3B873530AV470590L", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "invoice_id": "popjarus-cs-**************", "custom_id": "card", "status": "PENDING", "status_details": {"reason": "BUYER_COMPLAINT"}, "supplementary_data": {"related_ids": {"order_id": "5J3911911L808781E"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "6T55USYPWZ6X2"}, "create_time": "2024-10-23T18:17:13Z", "update_time": "2024-10-25T14:36:07Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/3B873530AV470590L", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/3B873530AV470590L/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/5J3911911L808781E", "rel": "up", "method": "GET"}]}, "account": "C129"}, "893192685L570930W": {"success": true, "data": {"id": "893192685L570930W", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "imagineitus-nn-**************", "custom_id": "card", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "7UC213128W8558027"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "BCWDQEJ4FHPF2"}, "create_time": "2024-10-24T14:16:16Z", "update_time": "2024-10-24T14:16:16Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/893192685L570930W", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/893192685L570930W/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/7UC213128W8558027", "rel": "up", "method": "GET"}]}, "account": "C131"}, "1R983207B71168455": {"success": true, "data": {"id": "1R983207B71168455", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "gojolts-3y-**************", "custom_id": "paypal", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "43P09888AG077401E"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "VJRFVRYDMJQR8"}, "create_time": "2024-10-24T15:43:11Z", "update_time": "2024-10-24T15:43:11Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/1R983207B71168455", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/1R983207B71168455/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/43P09888AG077401E", "rel": "up", "method": "GET"}]}, "account": "C130"}, "6DR051610F5093329": {"success": true, "data": {"id": "6DR051610F5093329", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "gojolts-wb-**************", "custom_id": "paypal", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "8X4872884L1067713"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "VJRFVRYDMJQR8"}, "create_time": "2024-10-24T21:30:47Z", "update_time": "2024-10-24T21:30:47Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/6DR051610F5093329", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/6DR051610F5093329/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/8X4872884L1067713", "rel": "up", "method": "GET"}]}, "account": "C130"}}