{"285160308W471941T": {"success": true, "data": {"id": "285160308W471941T", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "QU2F2B8ZKUJ32", "account_status": "UNVERIFIED", "name": {"given_name": "<PERSON>", "surname": "<PERSON>"}, "phone_number": {"national_number": "**********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "34.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "29.99"}, "shipping": {"currency_code": "USD", "value": "4.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "BU2HEWRHR2DGN"}, "description": "🔥Last Day 50% OFF🎨Early Childhood Enlightenment Book-[object Object]", "custom_id": "card", "invoice_id": "hexluvus-omzse-35572", "soft_descriptor": "PAYPAL *MAKOTAZPTEL", "items": [{"name": "🔥Last Day 50% OFF🎨Early Childhood Enlightenment Book-[object Object]", "unit_amount": {"currency_code": "USD", "value": "29.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20241016-30ts-18-01-15GTXL"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "14 Harwich Lane", "admin_area_2": "Stoughton", "admin_area_1": "MA", "postal_code": "92072", "country_code": "US"}}, "payments": {"captures": [{"id": "9F872162LU2685344", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "hexluvus-omzse-35572", "custom_id": "card", "links": [{"href": "https://api.paypal.com/v2/payments/captures/9F872162LU2685344", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/9F872162LU2685344/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/285160308W471941T", "rel": "up", "method": "GET"}], "create_time": "2024-10-18T14:08:42Z", "update_time": "2024-10-18T14:08:42Z"}]}}], "payer": {"name": {"given_name": "<PERSON>", "surname": "<PERSON>"}, "email_address": "<EMAIL>", "payer_id": "QU2F2B8ZKUJ32", "phone": {"phone_number": {"national_number": "**********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-18T14:08:42Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/285160308W471941T", "rel": "self", "method": "GET"}]}}, "64F10667CU135474H": {"success": true, "data": {"id": "64F10667CU135474H", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "Karen<PERSON><PERSON><PERSON><PERSON>@gmail.com", "account_id": "4NS2N4QB5V7ZL", "account_status": "UNVERIFIED", "name": {"given_name": "<PERSON>", "surname": "<PERSON>"}, "phone_number": {"national_number": "**********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "36.94", "breakdown": {"item_total": {"currency_code": "USD", "value": "29.95"}, "shipping": {"currency_code": "USD", "value": "6.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "BU2HEWRHR2DGN"}, "description": "🎁 HOT SALE 49% OFF 🔥 Clip in Bangs-[object Object]", "custom_id": "card", "invoice_id": "toygad-chcwk-66932", "soft_descriptor": "PAYPAL *MAKOTAZPTEL", "items": [{"name": "🎁 HOT SALE 49% OFF 🔥 Clip in Bangs-[object Object]", "unit_amount": {"currency_code": "USD", "value": "29.95"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20240926-ngs9-11-01-CB9BTQ"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "3800 Gordon Street, Lake Charles AL 70605", "admin_area_2": "Lake Charles", "admin_area_1": "LA", "postal_code": "70605", "country_code": "US"}}, "payments": {"captures": [{"id": "6KR989493N487204S", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "36.94"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "36.94"}, "paypal_fee": {"currency_code": "USD", "value": "1.93"}, "net_amount": {"currency_code": "USD", "value": "35.01"}}, "invoice_id": "toygad-chcwk-66932", "custom_id": "card", "links": [{"href": "https://api.paypal.com/v2/payments/captures/6KR989493N487204S", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/6KR989493N487204S/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/64F10667CU135474H", "rel": "up", "method": "GET"}], "create_time": "2024-10-18T15:46:30Z", "update_time": "2024-10-18T15:46:30Z"}]}}], "payer": {"name": {"given_name": "<PERSON>", "surname": "<PERSON>"}, "email_address": "Karen<PERSON><PERSON><PERSON><PERSON>@gmail.com", "payer_id": "4NS2N4QB5V7ZL", "phone": {"phone_number": {"national_number": "**********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-18T15:46:30Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/64F10667CU135474H", "rel": "self", "method": "GET"}]}}, "9P005624BE2478312": {"success": true, "data": {"id": "9P005624BE2478312", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "Karen<PERSON><PERSON><PERSON><PERSON>@gmail.com", "account_id": "4NS2N4QB5V7ZL", "account_status": "UNVERIFIED", "name": {"given_name": "<PERSON>", "surname": "<PERSON>"}, "phone_number": {"national_number": "**********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "36.94", "breakdown": {"item_total": {"currency_code": "USD", "value": "29.95"}, "shipping": {"currency_code": "USD", "value": "6.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "BU2HEWRHR2DGN"}, "description": "🎁 HOT SALE 49% OFF 🔥 Clip in Bangs-[object Object]", "custom_id": "card", "invoice_id": "toygad-vg2qo-10833", "soft_descriptor": "PAYPAL *MAKOTAZPTEL", "items": [{"name": "🎁 HOT SALE 49% OFF 🔥 Clip in Bangs-[object Object]", "unit_amount": {"currency_code": "USD", "value": "29.95"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20240926-ngs9-11-01-7GXZLS"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "3800 Gordon Street, Lake Charles AL 70605", "admin_area_2": "Lake Charles", "admin_area_1": "LA", "postal_code": "70605", "country_code": "US"}}, "payments": {"captures": [{"id": "12B310378N001511C", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "36.94"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "36.94"}, "paypal_fee": {"currency_code": "USD", "value": "1.93"}, "net_amount": {"currency_code": "USD", "value": "35.01"}}, "invoice_id": "toygad-vg2qo-10833", "custom_id": "card", "links": [{"href": "https://api.paypal.com/v2/payments/captures/12B310378N001511C", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/12B310378N001511C/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/9P005624BE2478312", "rel": "up", "method": "GET"}], "create_time": "2024-10-18T15:49:06Z", "update_time": "2024-10-18T15:49:06Z"}]}}], "payer": {"name": {"given_name": "<PERSON>", "surname": "<PERSON>"}, "email_address": "Karen<PERSON><PERSON><PERSON><PERSON>@gmail.com", "payer_id": "4NS2N4QB5V7ZL", "phone": {"phone_number": {"national_number": "**********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-18T15:49:06Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/9P005624BE2478312", "rel": "self", "method": "GET"}]}}, "72K082673A089642U": {"success": true, "data": {"id": "72K082673A089642U", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "TXUXMMDDTSE6C", "account_status": "UNVERIFIED", "name": {"given_name": "<PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>"}, "phone_number": {"national_number": "***********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "33.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "26.99"}, "shipping": {"currency_code": "USD", "value": "6.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "BU2HEWRHR2DGN"}, "description": "Summer Promotion 49% OFF-Chain Saw <PERSON>-[object Object]", "custom_id": "card", "invoice_id": "goaheadus-toahg-43014", "soft_descriptor": "PAYPAL *MAKOTAZPTEL", "items": [{"name": "Summer Promotion 49% OFF-Chain Saw <PERSON>-[object Object]", "unit_amount": {"currency_code": "USD", "value": "26.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20240912-ener-16-01-LCOXBN"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "504 Mills st", "admin_area_2": "Valdosta", "admin_area_1": "GA", "postal_code": "31601", "country_code": "US"}}, "payments": {"captures": [{"id": "91H84068T1840851E", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "33.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "33.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.80"}, "net_amount": {"currency_code": "USD", "value": "32.18"}}, "invoice_id": "goaheadus-toahg-43014", "custom_id": "card", "links": [{"href": "https://api.paypal.com/v2/payments/captures/91H84068T1840851E", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/91H84068T1840851E/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/72K082673A089642U", "rel": "up", "method": "GET"}], "create_time": "2024-10-22T01:13:15Z", "update_time": "2024-10-22T01:13:15Z"}]}}], "payer": {"name": {"given_name": "<PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>"}, "email_address": "<EMAIL>", "payer_id": "TXUXMMDDTSE6C", "phone": {"phone_number": {"national_number": "***********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-22T01:13:15Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/72K082673A089642U", "rel": "self", "method": "GET"}]}}, "0TX93725YH2521727": {"success": true, "data": {"id": "0TX93725YH2521727", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "HHSBUENQVGHWC", "account_status": "UNVERIFIED", "name": {"given_name": "<PERSON>", "surname": "Jeske"}, "phone_number": {"national_number": "**********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "33.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "26.99"}, "shipping": {"currency_code": "USD", "value": "6.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "BU2HEWRHR2DGN"}, "description": "Summer Promotion 49% OFF-Chain Saw <PERSON>-[object Object]", "custom_id": "paypal", "invoice_id": "goaheadus-qzcy5-85688", "soft_descriptor": "PAYPAL *MAKOTAZPTEL", "items": [{"name": "Summer Promotion 49% OFF-Chain Saw <PERSON>-[object Object]", "unit_amount": {"currency_code": "USD", "value": "26.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20240912-ener-16-01-LCOXBN"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "7660 Theodore trail", "admin_area_2": "<PERSON><PERSON><PERSON>", "admin_area_1": "NC", "postal_code": "28602", "country_code": "US"}}, "payments": {"captures": [{"id": "53K590175V409943F", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "33.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "33.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.80"}, "net_amount": {"currency_code": "USD", "value": "32.18"}}, "invoice_id": "goaheadus-qzcy5-85688", "custom_id": "paypal", "links": [{"href": "https://api.paypal.com/v2/payments/captures/53K590175V409943F", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/53K590175V409943F/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/0TX93725YH2521727", "rel": "up", "method": "GET"}], "create_time": "2024-10-22T02:00:28Z", "update_time": "2024-10-22T02:00:28Z"}]}}], "payer": {"name": {"given_name": "<PERSON>", "surname": "Jeske"}, "email_address": "<EMAIL>", "payer_id": "HHSBUENQVGHWC", "phone": {"phone_number": {"national_number": "**********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-22T02:00:28Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/0TX93725YH2521727", "rel": "self", "method": "GET"}]}}, "6RY07036CM825061G": {"success": true, "data": {"id": "6RY07036CM825061G", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "U8WVCEY8LUJ2E", "account_status": "VERIFIED", "name": {"given_name": "<PERSON>", "surname": "<PERSON>"}, "phone_number": {"national_number": "**********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "34.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "29.99"}, "shipping": {"currency_code": "USD", "value": "4.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "contact.kus<PERSON><EMAIL>", "merchant_id": "EFSTJVRQADT56"}, "description": "🔥Last Day 50% OFF 🎨 Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "custom_id": "paypal", "invoice_id": "zipdawn-5a-**************", "items": [{"name": "🔥Last Day 50% OFF 🎨 Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "unit_amount": {"currency_code": "USD", "value": "29.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20241016-30ts-18-01-15GTXL"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "9375 Falling Water Dr", "admin_area_2": "<PERSON><PERSON><PERSON>", "admin_area_1": "VA", "postal_code": "20136", "country_code": "US"}}, "payments": {"captures": [{"id": "7US0622368778491W", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "zipdawn-5a-**************", "custom_id": "paypal", "links": [{"href": "https://api.paypal.com/v2/payments/captures/7US0622368778491W", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/7US0622368778491W/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/6RY07036CM825061G", "rel": "up", "method": "GET"}], "create_time": "2024-10-22T11:56:08Z", "update_time": "2024-10-22T11:56:08Z"}]}}], "payer": {"name": {"given_name": "<PERSON>", "surname": "<PERSON>"}, "email_address": "<EMAIL>", "payer_id": "U8WVCEY8LUJ2E", "phone": {"phone_number": {"national_number": "**********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-22T11:56:08Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/6RY07036CM825061G", "rel": "self", "method": "GET"}]}}, "9CD05982G24894102": {"success": true, "data": {"id": "9CD05982G24894102", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "VUYKDYMFYXRTJ", "account_status": "UNVERIFIED", "name": {"given_name": "<PERSON>", "surname": "<PERSON>"}, "phone_number": {"national_number": "**********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "54.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "47.99"}, "shipping": {"currency_code": "USD", "value": "6.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "6T55USYPWZ6X2"}, "description": "🎁 Hot Sale 49% OFF 🔥 ARTISTIC STYLE LOOSE DRESS 💃-Color-Size", "custom_id": "card", "invoice_id": "enjoyiz-lv-**************", "soft_descriptor": "PAYPAL *BICHLOANTHI", "items": [{"name": "🎁 Hot Sale 49% OFF 🔥 ARTISTIC STYLE LOOSE DRESS 💃-Color-Size", "unit_amount": {"currency_code": "USD", "value": "47.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20241004-22ts-15-01-1Y15IQ"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "401 Central Park", "admin_area_2": "Edinburg", "admin_area_1": "TX", "postal_code": "78541", "country_code": "US"}}, "payments": {"captures": [{"id": "1EL09073EX1682212", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "54.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "54.98"}, "paypal_fee": {"currency_code": "USD", "value": "2.72"}, "net_amount": {"currency_code": "USD", "value": "52.26"}}, "invoice_id": "enjoyiz-lv-**************", "custom_id": "card", "links": [{"href": "https://api.paypal.com/v2/payments/captures/1EL09073EX1682212", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/1EL09073EX1682212/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/9CD05982G24894102", "rel": "up", "method": "GET"}], "create_time": "2024-10-22T14:18:48Z", "update_time": "2024-10-22T14:18:48Z"}]}}], "payer": {"name": {"given_name": "<PERSON>", "surname": "<PERSON>"}, "email_address": "<EMAIL>", "payer_id": "VUYKDYMFYXRTJ", "phone": {"phone_number": {"national_number": "**********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-22T14:18:48Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/9CD05982G24894102", "rel": "self", "method": "GET"}]}}, "8BF18200M86791526": {"success": true, "data": {"id": "8BF18200M86791526", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "DHTEK7UHUNQ7W", "account_status": "UNVERIFIED", "name": {"given_name": "<PERSON>", "surname": "<PERSON>"}, "phone_number": {"national_number": "**********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "34.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "29.99"}, "shipping": {"currency_code": "USD", "value": "4.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "contact.kus<PERSON><EMAIL>", "merchant_id": "EFSTJVRQADT56"}, "description": "🔥Last Day 50% OFF🎨Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "custom_id": "card", "invoice_id": "imagineitus-22-**************", "soft_descriptor": "PAYPAL *VANANHTHING", "items": [{"name": "🔥Last Day 50% OFF🎨Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "unit_amount": {"currency_code": "USD", "value": "29.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20241016-30ts-18-01-15GTXL"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "95215 Douglas Rd", "admin_area_2": "Fernandina Beach", "admin_area_1": "FL", "postal_code": "32034", "country_code": "US"}}, "payments": {"captures": [{"id": "5CK317616Y5937813", "status": "PENDING", "status_details": {"reason": "PENDING_REVIEW"}, "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "invoice_id": "imagineitus-22-**************", "custom_id": "card", "links": [{"href": "https://api.paypal.com/v2/payments/captures/5CK317616Y5937813", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/5CK317616Y5937813/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/8BF18200M86791526", "rel": "up", "method": "GET"}], "create_time": "2024-10-23T12:50:12Z", "update_time": "2024-10-23T12:50:17Z"}]}}], "payer": {"name": {"given_name": "<PERSON>", "surname": "<PERSON>"}, "email_address": "<EMAIL>", "payer_id": "DHTEK7UHUNQ7W", "phone": {"phone_number": {"national_number": "**********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-23T12:50:17Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/8BF18200M86791526", "rel": "self", "method": "GET"}]}}, "4GA78977SD454083V": {"success": true, "data": {"id": "4GA78977SD454083V", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "HGNFYP4L2Q4WS", "account_status": "VERIFIED", "name": {"given_name": "<PERSON>", "surname": "VanBuskirk"}, "phone_number": {"national_number": "**********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "34.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "29.99"}, "shipping": {"currency_code": "USD", "value": "4.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "6T55USYPWZ6X2"}, "description": "🔥Last Day 50% OFF 🎨 Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "custom_id": "paypal", "invoice_id": "popjarus-dr-**************", "items": [{"name": "🔥Last Day 50% OFF 🎨 Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "unit_amount": {"currency_code": "USD", "value": "29.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20241016-30ts-18-01-15GTXL"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "301 NW 82nd Ave", "address_line_2": "Outpatient Surgical Services", "admin_area_2": "Plantation", "admin_area_1": "FL", "postal_code": "33324-1811", "country_code": "US"}}, "payments": {"captures": [{"id": "5AA725891K604962H", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "popjarus-dr-**************", "custom_id": "paypal", "links": [{"href": "https://api.paypal.com/v2/payments/captures/5AA725891K604962H", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/5AA725891K604962H/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/4GA78977SD454083V", "rel": "up", "method": "GET"}], "create_time": "2024-10-23T14:10:19Z", "update_time": "2024-10-23T14:10:19Z"}]}}], "payer": {"name": {"given_name": "<PERSON>", "surname": "VanBuskirk"}, "email_address": "<EMAIL>", "payer_id": "HGNFYP4L2Q4WS", "phone": {"phone_number": {"national_number": "**********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-23T14:10:19Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/4GA78977SD454083V", "rel": "self", "method": "GET"}]}}, "2CE71335LN798611R": {"success": true, "data": {"id": "2CE71335LN798611R", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "U78MQR82PY4WA", "account_status": "UNVERIFIED", "name": {"given_name": "<PERSON>", "surname": "Darling"}, "phone_number": {"national_number": "**********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "34.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "29.99"}, "shipping": {"currency_code": "USD", "value": "4.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "BCWDQEJ4FHPF2"}, "description": "🔥Last Day 50% OFF🎨Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "custom_id": "card", "invoice_id": "imagineitus-d7-**************", "soft_descriptor": "PAYPAL *ANHDUCTRUON", "items": [{"name": "🔥Last Day 50% OFF🎨Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "unit_amount": {"currency_code": "USD", "value": "29.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20241016-30ts-18-01-15GTXL"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "39 lake end rd", "admin_area_2": "Newfoundland", "admin_area_1": "NJ", "postal_code": "07435", "country_code": "US"}}, "payments": {"captures": [{"id": "3RG04706Y5062091K", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "imagineitus-d7-**************", "custom_id": "card", "links": [{"href": "https://api.paypal.com/v2/payments/captures/3RG04706Y5062091K", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/3RG04706Y5062091K/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/2CE71335LN798611R", "rel": "up", "method": "GET"}], "create_time": "2024-10-23T14:14:17Z", "update_time": "2024-10-23T14:14:17Z"}]}}], "payer": {"name": {"given_name": "<PERSON>", "surname": "Darling"}, "email_address": "<EMAIL>", "payer_id": "U78MQR82PY4WA", "phone": {"phone_number": {"national_number": "**********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-23T14:14:17Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/2CE71335LN798611R", "rel": "self", "method": "GET"}]}}, "40N561308T984784A": {"success": true, "data": {"id": "40N561308T984784A", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "MFDTHKUVD3Y8S", "account_status": "UNVERIFIED", "name": {"given_name": "Georgia", "surname": "<PERSON>"}, "phone_number": {"national_number": "**********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "34.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "29.99"}, "shipping": {"currency_code": "USD", "value": "4.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "FVAGAWBBHCK42"}, "description": "🔥Last Day 50% OFF🎨Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "custom_id": "card", "invoice_id": "imagineitus-kf-**************", "soft_descriptor": "PAYPAL *LUZUDAPTELT", "items": [{"name": "🔥Last Day 50% OFF🎨Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "unit_amount": {"currency_code": "USD", "value": "29.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20241016-30ts-18-01-15GTXL"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "6426 <PERSON>. <PERSON>", "admin_area_2": "Centennial", "admin_area_1": "CO", "postal_code": "80112", "country_code": "US"}}, "payments": {"captures": [{"id": "95N49891HH304081K", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "imagineitus-kf-**************", "custom_id": "card", "links": [{"href": "https://api.paypal.com/v2/payments/captures/95N49891HH304081K", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/95N49891HH304081K/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/40N561308T984784A", "rel": "up", "method": "GET"}], "create_time": "2024-10-23T15:01:24Z", "update_time": "2024-10-23T15:01:24Z"}]}}], "payer": {"name": {"given_name": "Georgia", "surname": "<PERSON>"}, "email_address": "<EMAIL>", "payer_id": "MFDTHKUVD3Y8S", "phone": {"phone_number": {"national_number": "**********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-23T15:01:24Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/40N561308T984784A", "rel": "self", "method": "GET"}]}}, "4NB852817R786794W": {"success": true, "data": {"id": "4NB852817R786794W", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "Y9VPETLFXYUBA", "account_status": "UNVERIFIED", "name": {"given_name": "<PERSON>", "surname": "<PERSON><PERSON>"}, "phone_number": {"national_number": "**********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "34.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "29.99"}, "shipping": {"currency_code": "USD", "value": "4.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "FVAGAWBBHCK42"}, "description": "🔥Last Day 50% OFF 🎨 Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "custom_id": "card", "invoice_id": "zipdawn-gn-**************", "soft_descriptor": "PAYPAL *LUZUDAPTELT", "items": [{"name": "🔥Last Day 50% OFF 🎨 Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "unit_amount": {"currency_code": "USD", "value": "29.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20241016-30ts-18-01-15GTXL"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "8111 Glenmill Court", "admin_area_2": "Cincinnati", "admin_area_1": "OH", "postal_code": "45249", "country_code": "US"}}, "payments": {"captures": [{"id": "0KE19041PC544260T", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "zipdawn-gn-**************", "custom_id": "card", "links": [{"href": "https://api.paypal.com/v2/payments/captures/0KE19041PC544260T", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/0KE19041PC544260T/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/4NB852817R786794W", "rel": "up", "method": "GET"}], "create_time": "2024-10-23T15:05:41Z", "update_time": "2024-10-23T15:05:41Z"}]}}], "payer": {"name": {"given_name": "<PERSON>", "surname": "<PERSON><PERSON>"}, "email_address": "<EMAIL>", "payer_id": "Y9VPETLFXYUBA", "phone": {"phone_number": {"national_number": "**********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-23T15:05:41Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/4NB852817R786794W", "rel": "self", "method": "GET"}]}}, "82J01324FT972704V": {"success": true, "data": {"id": "82J01324FT972704V", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "RZNQJBYZG84EG", "account_status": "UNVERIFIED", "name": {"given_name": "Cara", "surname": "<PERSON>"}, "phone_number": {"national_number": "**********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "34.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "29.99"}, "shipping": {"currency_code": "USD", "value": "4.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "FVAGAWBBHCK42"}, "description": "🔥Last Day 50% OFF🎨Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "custom_id": "card", "invoice_id": "imagineitus-g8-**************", "soft_descriptor": "PAYPAL *LUZUDAPTELT", "items": [{"name": "🔥Last Day 50% OFF🎨Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "unit_amount": {"currency_code": "USD", "value": "29.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20241016-30ts-18-01-15GTXL"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "1060 Majella Rd", "admin_area_2": "Pebble Beach", "admin_area_1": "CA", "postal_code": "93953", "country_code": "US"}}, "payments": {"captures": [{"id": "95X84202LR828020C", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "imagineitus-g8-**************", "custom_id": "card", "links": [{"href": "https://api.paypal.com/v2/payments/captures/95X84202LR828020C", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/95X84202LR828020C/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/82J01324FT972704V", "rel": "up", "method": "GET"}], "create_time": "2024-10-23T15:28:39Z", "update_time": "2024-10-23T15:28:39Z"}]}}], "payer": {"name": {"given_name": "Cara", "surname": "<PERSON>"}, "email_address": "<EMAIL>", "payer_id": "RZNQJBYZG84EG", "phone": {"phone_number": {"national_number": "**********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-23T15:28:39Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/82J01324FT972704V", "rel": "self", "method": "GET"}]}}, "1AY62826G50984814": {"success": true, "data": {"id": "1AY62826G50984814", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "JJET3UZT7HL8E", "account_status": "UNVERIFIED", "name": {"given_name": "<PERSON>", "surname": "<PERSON><PERSON>"}, "phone_number": {"national_number": "**********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "157.94", "breakdown": {"item_total": {"currency_code": "USD", "value": "157.94"}, "shipping": {"currency_code": "USD", "value": "0.00"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "VJRFVRYDMJQR8"}, "description": "🔥Last Day 50% OFF🎨Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "custom_id": "card", "invoice_id": "imagineitus-oc-**************", "soft_descriptor": "PAYPAL *KIENTRUNGNG", "items": [{"name": "🔥Last Day 50% OFF🎨Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "unit_amount": {"currency_code": "USD", "value": "78.97"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "2", "sku": "20241016-30ts-18-03-JPEPWU"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "2610 State Line Road", "admin_area_2": "Adairville", "admin_area_1": "KY", "postal_code": "42202", "country_code": "US"}}, "payments": {"captures": [{"id": "4M007282VX8587708", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "157.94"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "157.94"}, "paypal_fee": {"currency_code": "USD", "value": "7.25"}, "net_amount": {"currency_code": "USD", "value": "150.69"}}, "invoice_id": "imagineitus-oc-**************", "custom_id": "card", "links": [{"href": "https://api.paypal.com/v2/payments/captures/4M007282VX8587708", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/4M007282VX8587708/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/1AY62826G50984814", "rel": "up", "method": "GET"}], "create_time": "2024-10-23T16:40:40Z", "update_time": "2024-10-23T16:40:40Z"}]}}], "payer": {"name": {"given_name": "<PERSON>", "surname": "<PERSON><PERSON>"}, "email_address": "<EMAIL>", "payer_id": "JJET3UZT7HL8E", "phone": {"phone_number": {"national_number": "**********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-23T16:40:40Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/1AY62826G50984814", "rel": "self", "method": "GET"}]}}, "5J3911911L808781E": {"success": true, "data": {"id": "5J3911911L808781E", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "claudia<PERSON><EMAIL>", "account_id": "2F3XCGB7LK3LL", "account_status": "UNVERIFIED", "name": {"given_name": "<PERSON>", "surname": "Withers"}, "phone_number": {"national_number": "**********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "34.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "29.99"}, "shipping": {"currency_code": "USD", "value": "4.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "6T55USYPWZ6X2"}, "description": "🔥Last Day 50% OFF 🎨 Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "custom_id": "card", "invoice_id": "popjarus-cs-**************", "soft_descriptor": "PAYPAL *BICHLOANTHI", "items": [{"name": "🔥Last Day 50% OFF 🎨 Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "unit_amount": {"currency_code": "USD", "value": "29.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20241016-30ts-18-01-15GTXL"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "3220 Connecticut Avenue, NW, Washington Dc 20008", "address_line_2": "104", "admin_area_2": "Washington", "admin_area_1": "DC", "postal_code": "20008", "country_code": "US"}}, "payments": {"captures": [{"id": "3B873530AV470590L", "status": "PENDING", "status_details": {"reason": "BUYER_COMPLAINT"}, "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "invoice_id": "popjarus-cs-**************", "custom_id": "card", "links": [{"href": "https://api.paypal.com/v2/payments/captures/3B873530AV470590L", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/3B873530AV470590L/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/5J3911911L808781E", "rel": "up", "method": "GET"}], "create_time": "2024-10-23T18:17:13Z", "update_time": "2024-10-25T14:36:07Z"}], "refunds": [{"id": "9HJ510564P044892H", "amount": {"currency_code": "USD", "value": "33.14"}, "seller_payable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "33.14"}, "paypal_fee": {"currency_code": "USD", "value": "0.00"}, "platform_fees": [{"amount": {"currency_code": "USD", "value": "0.00"}}], "net_amount": {"currency_code": "USD", "value": "33.14"}, "total_refunded_amount": {"currency_code": "USD", "value": "0.00"}}, "invoice_id": "popjarus-cs-**************", "custom_id": "card", "status": "PENDING", "links": [{"href": "https://api.paypal.com/v2/payments/refunds/9HJ510564P044892H", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/3B873530AV470590L", "rel": "up", "method": "GET"}], "create_time": "2024-10-25T07:36:07-07:00", "update_time": "2024-10-25T07:36:07-07:00"}]}}], "payer": {"name": {"given_name": "<PERSON>", "surname": "Withers"}, "email_address": "claudia<PERSON><EMAIL>", "payer_id": "2F3XCGB7LK3LL", "phone": {"phone_number": {"national_number": "**********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-25T14:36:07Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/5J3911911L808781E", "rel": "self", "method": "GET"}]}}, "7UC213128W8558027": {"success": true, "data": {"id": "7UC213128W8558027", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<PERSON>@mazzafinejewelry.com", "account_id": "4UJFNM29GNTN2", "account_status": "UNVERIFIED", "name": {"given_name": "<PERSON>", "surname": "Mazza"}, "phone_number": {"national_number": "**********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "34.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "29.99"}, "shipping": {"currency_code": "USD", "value": "4.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "BCWDQEJ4FHPF2"}, "description": "🔥Last Day 50% OFF🎨Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "custom_id": "card", "invoice_id": "imagineitus-nn-**************", "soft_descriptor": "PAYPAL *ANHDUCTRUON", "items": [{"name": "🔥Last Day 50% OFF🎨Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "unit_amount": {"currency_code": "USD", "value": "29.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20241016-30ts-18-01-15GTXL"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "491 Main St, Islip NY 11751", "admin_area_2": "<PERSON><PERSON>", "admin_area_1": "NY", "postal_code": "11751", "country_code": "US"}}, "payments": {"captures": [{"id": "893192685L570930W", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "NOT_ELIGIBLE"}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "imagineitus-nn-**************", "custom_id": "card", "links": [{"href": "https://api.paypal.com/v2/payments/captures/893192685L570930W", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/893192685L570930W/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/7UC213128W8558027", "rel": "up", "method": "GET"}], "create_time": "2024-10-24T14:16:16Z", "update_time": "2024-10-24T14:16:16Z"}]}}], "payer": {"name": {"given_name": "<PERSON>", "surname": "Mazza"}, "email_address": "<PERSON>@mazzafinejewelry.com", "payer_id": "4UJFNM29GNTN2", "phone": {"phone_number": {"national_number": "**********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-24T14:16:16Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/7UC213128W8558027", "rel": "self", "method": "GET"}]}}, "43P09888AG077401E": {"success": true, "data": {"id": "43P09888AG077401E", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "ZSCUQJZ5XL8KY", "account_status": "UNVERIFIED", "name": {"given_name": "jodi", "surname": "holzer"}, "phone_number": {"national_number": "**********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "34.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "29.99"}, "shipping": {"currency_code": "USD", "value": "4.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "VJRFVRYDMJQR8"}, "description": "🔥Last Day 50% OFF 🎨 Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "custom_id": "paypal", "invoice_id": "gojolts-3y-**************", "soft_descriptor": "PAYPAL *KIENTRUNGNG", "items": [{"name": "🔥Last Day 50% OFF 🎨 Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "unit_amount": {"currency_code": "USD", "value": "29.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20241016-30ts-18-01-15GTXL"}], "shipping": {"name": {"full_name": "<PERSON><PERSON>"}, "address": {"address_line_1": "9936 Hubbard Creek Rd", "admin_area_2": "Ump<PERSON>", "admin_area_1": "OR", "postal_code": "97486", "country_code": "US"}}, "payments": {"captures": [{"id": "1R983207B71168455", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "gojolts-3y-**************", "custom_id": "paypal", "links": [{"href": "https://api.paypal.com/v2/payments/captures/1R983207B71168455", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/1R983207B71168455/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/43P09888AG077401E", "rel": "up", "method": "GET"}], "create_time": "2024-10-24T15:43:11Z", "update_time": "2024-10-24T15:43:11Z"}]}}], "payer": {"name": {"given_name": "jodi", "surname": "holzer"}, "email_address": "<EMAIL>", "payer_id": "ZSCUQJZ5XL8KY", "phone": {"phone_number": {"national_number": "**********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-24T15:43:11Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/43P09888AG077401E", "rel": "self", "method": "GET"}]}}, "8X4872884L1067713": {"success": true, "data": {"id": "8X4872884L1067713", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "YED2PKWGLEC8G", "account_status": "VERIFIED", "name": {"given_name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON>"}, "phone_number": {"national_number": "**********"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "34.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "29.99"}, "shipping": {"currency_code": "USD", "value": "4.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}, "discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "VJRFVRYDMJQR8"}, "description": "🔥Last Day 50% OFF 🎨 Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "custom_id": "paypal", "invoice_id": "gojolts-wb-**************", "items": [{"name": "🔥Last Day 50% OFF 🎨 Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "unit_amount": {"currency_code": "USD", "value": "29.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20241016-30ts-18-01-15GTXL"}], "shipping": {"name": {"full_name": "<PERSON><PERSON>"}, "address": {"address_line_1": "6809 Quail Meadow Dr", "admin_area_2": "Watauga", "admin_area_1": "TX", "postal_code": "76148", "country_code": "US"}}, "payments": {"captures": [{"id": "6DR051610F5093329", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "gojolts-wb-**************", "custom_id": "paypal", "links": [{"href": "https://api.paypal.com/v2/payments/captures/6DR051610F5093329", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/6DR051610F5093329/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/8X4872884L1067713", "rel": "up", "method": "GET"}], "create_time": "2024-10-24T21:30:47Z", "update_time": "2024-10-24T21:30:47Z"}]}}], "payer": {"name": {"given_name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON>"}, "email_address": "<EMAIL>", "payer_id": "YED2PKWGLEC8G", "phone": {"phone_number": {"national_number": "**********"}}, "address": {"country_code": "US"}}, "update_time": "2024-10-24T21:30:47Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/8X4872884L1067713", "rel": "self", "method": "GET"}]}}}