{"4CA55086PC330325T": {"success": true, "data": {"id": "4CA55086PC330325T", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "95MCAWGUHZTXA", "account_status": "VERIFIED", "name": {"given_name": "JOAN", "surname": "ORENDER"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "43.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "38.99"}, "shipping": {"currency_code": "USD", "value": "4.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "contact.kus<PERSON><EMAIL>", "merchant_id": "EFSTJVRQADT56"}, "description": "🔥 Last Day 49% OFF 🎁 Furniture lift mover tool-[object Object]-[object Object]", "custom_id": "paypal", "invoice_id": "tapmugus-a104j-58522", "soft_descriptor": "VANANHTHING", "items": [{"name": "🔥 Last Day 49% OFF 🎁 Furniture lift mover tool-[object Object]-[object Object]", "unit_amount": {"currency_code": "USD", "value": "38.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20240927-15ts-16-01-U5LZVX"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "7206 BEAVER CREEK LN", "admin_area_2": "LINCOLN", "admin_area_1": "NE", "postal_code": "68516-5604", "country_code": "US"}}, "payments": {"captures": [{"id": "5NT51200L89263633", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "43.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "43.98"}, "paypal_fee": {"currency_code": "USD", "value": "2.24"}, "net_amount": {"currency_code": "USD", "value": "41.74"}}, "invoice_id": "tapmugus-a104j-58522", "custom_id": "paypal", "links": [{"href": "https://api.paypal.com/v2/payments/captures/5NT51200L89263633", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/5NT51200L89263633/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/4CA55086PC330325T", "rel": "up", "method": "GET"}], "create_time": "2024-10-17T21:56:00Z", "update_time": "2024-10-17T21:56:00Z"}]}}], "payer": {"name": {"given_name": "JOAN", "surname": "ORENDER"}, "email_address": "<EMAIL>", "payer_id": "95MCAWGUHZTXA", "address": {"country_code": "US"}}, "create_time": "2024-10-17T21:54:51Z", "update_time": "2024-10-17T21:56:00Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/4CA55086PC330325T", "rel": "self", "method": "GET"}]}}, "7KP96168YY6766043": {"success": true, "data": {"id": "7KP96168YY6766043", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "25JR7VGN6SAQA", "account_status": "VERIFIED", "name": {"given_name": "RICK", "surname": "HORNWOOD"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "43.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "38.99"}, "shipping": {"currency_code": "USD", "value": "4.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "contact.kus<PERSON><EMAIL>", "merchant_id": "EFSTJVRQADT56"}, "description": "🔥 Last Day 49% OFF 🎁 Furniture lift mover tool-[object Object]-[object Object]", "custom_id": "paypal", "invoice_id": "beezazus-skdgs-37445", "soft_descriptor": "PAYPAL *VANANHTHING", "items": [{"name": "🔥 Last Day 49% OFF 🎁 Furniture lift mover tool-[object Object]-[object Object]", "unit_amount": {"currency_code": "USD", "value": "38.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20240927-15ts-16-01-U5LZVX"}], "shipping": {"name": {"full_name": "<PERSON>"}, "address": {"address_line_1": "1966 La Ramada Dr", "admin_area_2": "<PERSON><PERSON><PERSON>", "admin_area_1": "CA", "postal_code": "93012-9320", "country_code": "US"}}, "payments": {"captures": [{"id": "2YV25145AF844953T", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "43.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "43.98"}, "paypal_fee": {"currency_code": "USD", "value": "2.24"}, "net_amount": {"currency_code": "USD", "value": "41.74"}}, "invoice_id": "beezazus-skdgs-37445", "custom_id": "paypal", "links": [{"href": "https://api.paypal.com/v2/payments/captures/2YV25145AF844953T", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/2YV25145AF844953T/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/7KP96168YY6766043", "rel": "up", "method": "GET"}], "create_time": "2024-10-18T03:39:57Z", "update_time": "2024-10-18T03:39:57Z"}]}}], "payer": {"name": {"given_name": "RICK", "surname": "HORNWOOD"}, "email_address": "<EMAIL>", "payer_id": "25JR7VGN6SAQA", "address": {"country_code": "US"}}, "create_time": "2024-10-18T03:37:56Z", "update_time": "2024-10-18T03:39:57Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/7KP96168YY6766043", "rel": "self", "method": "GET"}]}}, "6FX3827493853542K": {"success": true, "data": {"id": "6FX3827493853542K", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "BDTD4GJXS9ZKN", "account_status": "VERIFIED", "name": {"given_name": "<PERSON><PERSON>", "surname": "Bethel"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "31.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "24.99"}, "shipping": {"currency_code": "USD", "value": "6.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "6T55USYPWZ6X2"}, "description": "My Preschool Busy Book 📚 🎅 Best Christmas Gift for kids-Buy More Save More", "custom_id": "paypal", "invoice_id": "hortihavenus-otweq-24140", "soft_descriptor": "BICHLOANTHI", "items": [{"name": "My Preschool Busy Book 📚 🎅 Best Christmas Gift for kids-Buy More Save More", "unit_amount": {"currency_code": "USD", "value": "24.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20240927-book-12-01-RG324L"}], "shipping": {"name": {"full_name": "<PERSON><PERSON>"}, "address": {"address_line_1": "21223 Normandie Avenue", "admin_area_2": "Torrance", "admin_area_1": "CA", "postal_code": "90501", "country_code": "US"}}, "payments": {"captures": [{"id": "55F64000A0956871H", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "31.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "31.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.71"}, "net_amount": {"currency_code": "USD", "value": "30.27"}}, "invoice_id": "hortihavenus-otweq-24140", "custom_id": "paypal", "links": [{"href": "https://api.paypal.com/v2/payments/captures/55F64000A0956871H", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/55F64000A0956871H/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/6FX3827493853542K", "rel": "up", "method": "GET"}], "create_time": "2024-10-18T14:23:29Z", "update_time": "2024-11-08T07:58:18Z"}]}}], "payer": {"name": {"given_name": "<PERSON><PERSON>", "surname": "Bethel"}, "email_address": "<EMAIL>", "payer_id": "BDTD4GJXS9ZKN", "address": {"country_code": "US"}}, "create_time": "2024-10-18T14:21:29Z", "update_time": "2024-10-18T14:23:29Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/6FX3827493853542K", "rel": "self", "method": "GET"}]}}, "8ME64812TX950973Y": {"success": true, "data": {"id": "8ME64812TX950973Y", "intent": "CAPTURE", "status": "COMPLETED", "payment_source": {"paypal": {"email_address": "<EMAIL>", "account_id": "ZAUBQZVXYFCTL", "account_status": "UNVERIFIED", "name": {"given_name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>"}, "address": {"country_code": "US"}}}, "purchase_units": [{"reference_id": "default", "amount": {"currency_code": "USD", "value": "34.98", "breakdown": {"item_total": {"currency_code": "USD", "value": "29.99"}, "shipping": {"currency_code": "USD", "value": "4.99"}, "handling": {"currency_code": "USD", "value": "0.00"}, "insurance": {"currency_code": "USD", "value": "0.00"}, "shipping_discount": {"currency_code": "USD", "value": "0.00"}}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "VJRFVRYDMJQR8"}, "description": "🔥Last Day 50% OFF 🎨 Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "custom_id": "paypal", "invoice_id": "zipdawn-lh-**************", "soft_descriptor": "PAYPAL *KIENTRUNGNG", "items": [{"name": "🔥Last Day 50% OFF 🎨 Early Childhood Enlightenment Book-BUY MORE SAVE MORE", "unit_amount": {"currency_code": "USD", "value": "29.99"}, "tax": {"currency_code": "USD", "value": "0.00"}, "quantity": "1", "sku": "20241016-30ts-18-01-15GTXL"}], "shipping": {"name": {"full_name": "<PERSON><PERSON>"}, "address": {"address_line_1": "111 <PERSON>", "admin_area_2": "<PERSON><PERSON>", "admin_area_1": "LA", "postal_code": "70668-3219", "country_code": "US"}}, "payments": {"captures": [{"id": "0G053192V35273700", "status": "COMPLETED", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "zipdawn-lh-**************", "custom_id": "paypal", "links": [{"href": "https://api.paypal.com/v2/payments/captures/0G053192V35273700", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/0G053192V35273700/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/8ME64812TX950973Y", "rel": "up", "method": "GET"}], "create_time": "2024-10-29T06:29:29Z", "update_time": "2024-10-29T06:29:29Z"}]}}], "payer": {"name": {"given_name": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>"}, "email_address": "<EMAIL>", "payer_id": "ZAUBQZVXYFCTL", "address": {"country_code": "US"}}, "create_time": "2024-10-29T06:28:38Z", "update_time": "2024-10-29T06:29:29Z", "links": [{"href": "https://api.paypal.com/v2/checkout/orders/8ME64812TX950973Y", "rel": "self", "method": "GET"}]}}}