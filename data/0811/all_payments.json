{"5NT51200L89263633": {"success": true, "data": {"id": "5NT51200L89263633", "amount": {"currency_code": "USD", "value": "43.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "43.98"}, "paypal_fee": {"currency_code": "USD", "value": "2.24"}, "net_amount": {"currency_code": "USD", "value": "41.74"}}, "invoice_id": "tapmugus-a104j-58522", "custom_id": "paypal", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "4CA55086PC330325T"}}, "payee": {"email_address": "contact.kus<PERSON><EMAIL>", "merchant_id": "EFSTJVRQADT56"}, "create_time": "2024-10-17T21:56:00Z", "update_time": "2024-10-17T21:56:00Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/5NT51200L89263633", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/5NT51200L89263633/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/4CA55086PC330325T", "rel": "up", "method": "GET"}]}, "account": "C121"}, "2YV25145AF844953T": {"success": true, "data": {"id": "2YV25145AF844953T", "amount": {"currency_code": "USD", "value": "43.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "43.98"}, "paypal_fee": {"currency_code": "USD", "value": "2.24"}, "net_amount": {"currency_code": "USD", "value": "41.74"}}, "invoice_id": "beezazus-skdgs-37445", "custom_id": "paypal", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "7KP96168YY6766043"}}, "payee": {"email_address": "contact.kus<PERSON><EMAIL>", "merchant_id": "EFSTJVRQADT56"}, "create_time": "2024-10-18T03:39:57Z", "update_time": "2024-10-18T03:39:57Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/2YV25145AF844953T", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/2YV25145AF844953T/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/7KP96168YY6766043", "rel": "up", "method": "GET"}]}, "account": "C121"}, "55F64000A0956871H": {"success": true, "data": {"id": "55F64000A0956871H", "amount": {"currency_code": "USD", "value": "31.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "31.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.71"}, "net_amount": {"currency_code": "USD", "value": "30.27"}}, "invoice_id": "hortihavenus-otweq-24140", "custom_id": "paypal", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "6FX3827493853542K"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "6T55USYPWZ6X2"}, "create_time": "2024-10-18T14:23:29Z", "update_time": "2024-11-08T07:58:18Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/55F64000A0956871H", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/55F64000A0956871H/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/6FX3827493853542K", "rel": "up", "method": "GET"}]}, "account": "C129"}, "0G053192V35273700": {"success": true, "data": {"id": "0G053192V35273700", "amount": {"currency_code": "USD", "value": "34.98"}, "final_capture": true, "seller_protection": {"status": "ELIGIBLE", "dispute_categories": ["ITEM_NOT_RECEIVED", "UNAUTHORIZED_TRANSACTION"]}, "seller_receivable_breakdown": {"gross_amount": {"currency_code": "USD", "value": "34.98"}, "paypal_fee": {"currency_code": "USD", "value": "1.84"}, "net_amount": {"currency_code": "USD", "value": "33.14"}}, "invoice_id": "zipdawn-lh-**************", "custom_id": "paypal", "status": "COMPLETED", "supplementary_data": {"related_ids": {"order_id": "8ME64812TX950973Y"}}, "payee": {"email_address": "<EMAIL>", "merchant_id": "VJRFVRYDMJQR8"}, "create_time": "2024-10-29T06:29:29Z", "update_time": "2024-10-29T06:29:29Z", "links": [{"href": "https://api.paypal.com/v2/payments/captures/0G053192V35273700", "rel": "self", "method": "GET"}, {"href": "https://api.paypal.com/v2/payments/captures/0G053192V35273700/refund", "rel": "refund", "method": "POST"}, {"href": "https://api.paypal.com/v2/checkout/orders/8ME64812TX950973Y", "rel": "up", "method": "GET"}]}, "account": "C130"}}