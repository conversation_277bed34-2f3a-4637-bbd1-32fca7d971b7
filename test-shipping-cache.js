import { generateDraftCacheKey } from './src/utils/keyGenerator.js';

/**
 * Test shipping impact on cache keys
 */

console.log('🧪 Testing shipping impact on cache keys\n');

const metaData = [
  { key: "ip", value: "127.0.0.1" },
  { key: "UA", value: "Mozilla/5.0 (Test)" }
];

const lineItems = [
  {
    "sku": "PROD_001",
    "name": "Test Product",
    "quantity": 1,
    "price": "29.99"
  }
];

// Same product, different shipping methods
const standardShipping = [
  {
    "method_title": "Standard Shipping",
    "total": "5.99"
  }
];

const expressShipping = [
  {
    "method_title": "Express Shipping", 
    "total": "12.99"
  }
];

const freeShipping = [
  {
    "method_title": "Free Shipping",
    "total": "0.00"
  }
];

// Generate cache keys - bây giờ include shipping_lines
const cacheKey1 = generateDraftCacheKey('example.com', metaData, lineItems, standardShipping);
const cacheKey2 = generateDraftCacheKey('example.com', metaData, lineItems, expressShipping);
const cacheKey3 = generateDraftCacheKey('example.com', metaData, lineItems, freeShipping);

console.log('📦 Same product with different shipping:');
console.log(`Standard Shipping ($5.99): ${cacheKey1}`);
console.log(`Express Shipping ($12.99): ${cacheKey2}`);
console.log(`Free Shipping ($0.00): ${cacheKey3}`);

console.log('\n🔍 Current Problem:');
if (cacheKey1 === cacheKey2 && cacheKey2 === cacheKey3) {
  console.log('❌ PROBLEM: All cache keys are SAME!');
  console.log('❌ User changing shipping method will hit old cache');
  console.log('❌ Wrong total price will be used');
} else {
  console.log('✅ GOOD: Different shipping creates different cache keys');
}

console.log('\n📊 Real-world impact:');
console.log('• User selects Standard Shipping → Creates PayPal order ($35.98)');
console.log('• User changes to Express Shipping → Hits cache → Wrong total ($35.98 instead of $42.98)');
console.log('• PayPal shows wrong amount → User confusion/payment failure');

console.log('\n🔧 Solution needed:');
console.log('• Include shipping_lines in cache key generation');
console.log('• Hash shipping method + total');
console.log('• Different shipping → Different cache keys'); 