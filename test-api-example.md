# Test API Example

## Request
```bash
curl -H "x-domain: oplexuk.com" \
     https://api-order-service.truestore.us/api/public/v3/orders/check/1750016047691ae1wM2jA
```

## Expected Response
```json
{
  "success": true,
  "data": {
    "id": 778471,
    "order_key": "1750016047691ae1wM2jA",
    "domain": "oplexuk.com",
    "status": "shipped",
    "tracking_id": "SDH0072287226",
    "createdAt": "2025-06-15T12:34:08.000Z",
    "updatedAt": "2025-06-17T01:37:18.000Z",
    "total": 76.98,
    "billing": {
      "city": "Brandon ",
      "email": "<EMAIL>",
      "phone": "",
      "state": "FL",
      "country": "US",
      "postcode": "33511",
      "address_1": "2219 Malibu drive ",
      "address_2": "",
      "last_name": "Vognet",
      "first_name": "<PERSON>"
    },
    "currency": "USD",
    "shipping": {
      "city": "<PERSON> ",
      "state": "FL",
      "country": "US",
      "postcode": "33511",
      "address_1": "2219 Malibu drive ",
      "address_2": "",
      "last_name": "Vognet",
      "first_name": "Frank"
    },
    "sub_total": 69.99,
    "tip_total": 0,
    "line_items": [
      {
        "sku": "20250611-n176-13-01-TORQ6S",
        "name": "🎁 Hot Sale 50% OFF 🔥 Solar Pool Fountain with Lights Dual Arc - Above/Inground Pool Fountain Lights with Remote Control-Mode",
        "image": "https://admin.trueinvestz.com/wp-content/uploads/2025/05/89c79e53-403e-4aa2-b1f9-dcad3d9469e6.png",
        "price": 69.99,
        "total": "69.99",
        "quantity": 1,
        "meta_data": [
          {
            "key": "Model",
            "display_key": "Model",
            "display_value": "Solar LED Light Models"
          }
        ],
        "product_id": 41928,
        "product_link": "https://oplexuk.com/product/poolfountain176ts",
        "variation_id": 524923
      }
    ],
    "discount_total": 0,
    "shipping_lines": [
      {
        "total": "6.99",
        "method_id": "flat_rate"
      }
    ],
    "shipping_total": 6.99
  }
}
```

## Error Cases

### Missing x-domain header
```bash
curl https://api-order-service.truestore.us/api/public/v3/orders/check/1750016047691ae1wM2jA
```
Response: `400 Bad Request`
```json
{
  "error": "Missing required header",
  "details": "x-domain header is required to access order information"
}
```

### Wrong domain
```bash
curl -H "x-domain: wrongdomain.com" \
     https://api-order-service.truestore.us/api/public/v3/orders/check/1750016047691ae1wM2jA
```
Response: `403 Forbidden`
```json
{
  "error": "Domain access denied",
  "details": "The provided domain does not match the order domain"
}
```

### Order not found
```bash
curl -H "x-domain: oplexuk.com" \
     https://api-order-service.truestore.us/api/public/v3/orders/check/nonexistentkey
```
Response: `404 Not Found`
```json
{
  "error": "Order not found",
  "details": "No order found with the provided order key"
}
```

### Rate limit exceeded
After 20 requests in 5 minutes:
Response: `429 Too Many Requests`
```json
{
  "error": "Too many order check requests from this IP",
  "details": "Please try again later. Rate limit: 20 requests per 5 minutes.",
  "retryAfter": 300
}
```
