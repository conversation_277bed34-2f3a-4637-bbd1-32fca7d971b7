module.exports = {
  apps: [
    {
      name: "local-server",
      script: "./src/localServer.js",
      env: {
        NODE_ENV: "production",
        PORT: 3006,
      },
      instances: 5,
      exec_mode: "cluster",
      autorestart: true,
      watch: false,
      max_memory_restart: "300M",
      exp_backoff_restart_delay: 100,
      merge_logs: true,
      log_date_format: "YYYY-MM-DD HH:mm:ss",
      error_file: "logs/err.log",
      out_file: "logs/out.log",
    },
    {
      name: "order-check-worker",
      script: "./src/cron/worker.js",
      env: {
        NODE_ENV: "production",
        NODE_OPTIONS: '--experimental-modules --es-module-specifier-resolution=node'
      },
      instances: 1,
      exec_mode: "fork",
      autorestart: true,
      watch: false,
      max_memory_restart: "300M",
      exp_backoff_restart_delay: 100,
      merge_logs: true,
      log_date_format: "YYYY-MM-DD HH:mm:ss",
      error_file: "logs/worker-err.log",
      out_file: "logs/worker-out.log",
    },
    {
      name: "email-worker",
      script: "./src/workers/emailWorker.js",
      env: {
        NODE_ENV: "production",
        NODE_OPTIONS: '--experimental-modules --es-module-specifier-resolution=node'
      },
      instances: 2,
      exec_mode: "fork",
      autorestart: true,
      watch: false,
      max_memory_restart: "300M",
      exp_backoff_restart_delay: 100,
      merge_logs: true,
      log_date_format: "YYYY-MM-DD HH:mm:ss",
      error_file: "logs/email-worker-err.log",
      out_file: "logs/email-worker-out.log",
    }
  ],
  deploy: {
    production: {
      user: "root",
      host: "***************",
      ref: "origin/main",
      repo: "**************:thanhnhoncntt/truestore-be-proxy.git",
      path: "/root/be-proxy",
      "pre-deploy-local": "",
      "post-deploy":
        "source ~/.nvm/nvm.sh && nvm use 20.15.0 && npm install && pm2 reload ecosystem.config.js --env production",
      "pre-setup": "",
      env: {
        NODE_ENV: "production",
      },
    },
  },
};