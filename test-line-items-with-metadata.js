import { generateOrderDataHash } from './src/utils/keyGenerator.js';

/**
 * Test script để verify vấn đề với meta_data trong line items
 */

console.log('🧪 Testing line items with meta_data variations\n');

// Test case: Same product, same quantity, but different meta_data options
console.log('TEST: Same product with different meta_data options');

const lineItem1 = {
  "sku": "20241128-60ts-18-01-273RO4",
  "name": "🔥 Last Day - 49% OFF 🔥 DIY Sponge Finger Painting Kit-BUY MORE SAVE MORE BUY 1",
  "price": 32.99,
  "quantity": 1,
  "meta_data": [
    {
      "name": "BUY MORE SAVE MORE",
      "option": "BUY 1"
    }
  ]
};

const lineItem2 = {
  "sku": "20241128-60ts-18-01-273RO4", // Same SKU
  "name": "🔥 Last Day - 49% OFF 🔥 DIY Sponge Finger Painting Kit-BUY MORE SAVE MORE BUY 2", 
  "price": 32.99, // Same price
  "quantity": 1,   // Same quantity
  "meta_data": [
    {
      "name": "BUY MORE SAVE MORE",
      "option": "BUY 2"  // Different option!
    }
  ]
};

const hash1 = generateOrderDataHash([lineItem1], []);
const hash2 = generateOrderDataHash([lineItem2], []);

console.log('Product with "BUY 1" option:');
console.log('Hash:', hash1);
console.log('\nProduct with "BUY 2" option:');
console.log('Hash:', hash2);

console.log('\n🔍 Results:');
if (hash1 === hash2) {
  console.log('❌ PROBLEM: Same hash for different options!');
  console.log('   This means user changing from BUY 1 → BUY 2 will hit old cache');
  console.log('   Need to include meta_data in hash calculation');
} else {
  console.log('✅ GOOD: Different hash for different options');
}

// Test case: Multiple items with different meta_data
console.log('\n\nTEST: Multiple items with different meta_data');

const cart1 = [
  {
    "sku": "PROD_A",
    "price": 19.99,
    "quantity": 1,
    "meta_data": [{"name": "Size", "option": "Small"}]
  },
  {
    "sku": "PROD_B", 
    "price": 29.99,
    "quantity": 1,
    "meta_data": [{"name": "Color", "option": "Red"}]
  }
];

const cart2 = [
  {
    "sku": "PROD_A",
    "price": 19.99,
    "quantity": 1,
    "meta_data": [{"name": "Size", "option": "Large"}] // Different size
  },
  {
    "sku": "PROD_B",
    "price": 29.99, 
    "quantity": 1,
    "meta_data": [{"name": "Color", "option": "Blue"}] // Different color
  }
];

const cartHash1 = generateOrderDataHash(cart1, []);
const cartHash2 = generateOrderDataHash(cart2, []);

console.log('Cart with Small + Red:', cartHash1);
console.log('Cart with Large + Blue:', cartHash2);

if (cartHash1 === cartHash2) {
  console.log('❌ PROBLEM: Same hash for different product variants!');
} else {
  console.log('✅ GOOD: Different hash for different product variants');
} 